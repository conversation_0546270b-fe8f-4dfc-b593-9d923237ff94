#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终兼容性验证脚本
验证所有修复是否成功，系统是否完全兼容
"""

import os
import sys
import sqlite3
import importlib.util

def verify_function_signatures():
    """验证函数签名是否正确"""
    print("🔍 验证函数签名...")
    
    file_path = "chatdb/backend/app/services/text2sql_service.py"
    
    if not os.path.exists(file_path):
        print(f"  ❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复后的函数签名
        expected_signature = "def construct_prompt(query: str, schema_context: Dict[str, Any], value_mappings: Dict[str, Dict[str, str]], metadata: Dict[str, Any] = None) -> str:"
        
        if expected_signature in content:
            print("  ✅ construct_prompt 函数签名正确")
            return True
        else:
            print("  ❌ construct_prompt 函数签名不正确")
            return False
            
    except Exception as e:
        print(f"  ❌ 验证失败: {e}")
        return False

def verify_orm_models():
    """验证ORM模型是否正确创建"""
    print("🔍 验证ORM模型...")
    
    model_file = "chatdb/backend/app/models/metadata_models.py"
    base_file = "chatdb/backend/app/db/base.py"
    
    checks = []
    
    # 检查模型文件是否存在
    if os.path.exists(model_file):
        print("  ✅ metadata_models.py 文件存在")
        checks.append(True)
        
        # 检查模型类是否定义
        try:
            with open(model_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            required_classes = [
                "class FieldRelationship(Base):",
                "class QueryPattern(Base):",
                "class DataQualityRule(Base):",
                "class AIPromptTemplate(Base):",
                "class EnhancedColumnDescription(Base):"
            ]
            
            for class_def in required_classes:
                if class_def in content:
                    print(f"  ✅ {class_def.split('(')[0].replace('class ', '')} 类已定义")
                    checks.append(True)
                else:
                    print(f"  ❌ {class_def.split('(')[0].replace('class ', '')} 类未找到")
                    checks.append(False)
                    
        except Exception as e:
            print(f"  ❌ 读取模型文件失败: {e}")
            checks.append(False)
    else:
        print("  ❌ metadata_models.py 文件不存在")
        checks.append(False)
    
    # 检查base.py是否更新
    if os.path.exists(base_file):
        try:
            with open(base_file, 'r', encoding='utf-8') as f:
                base_content = f.read()
            
            if "from app.models.metadata_models import" in base_content:
                print("  ✅ base.py 已更新导入新模型")
                checks.append(True)
            else:
                print("  ❌ base.py 未更新导入")
                checks.append(False)
                
        except Exception as e:
            print(f"  ❌ 检查base.py失败: {e}")
            checks.append(False)
    else:
        print("  ❌ base.py 文件不存在")
        checks.append(False)
    
    return all(checks)

def verify_database_structure():
    """验证数据库结构"""
    print("🔍 验证数据库结构...")
    
    db_path = 'fin_data.db'
    if not os.path.exists(db_path):
        print(f"  ❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查所有必需的表
        required_tables = [
            'financial_data',
            'column_descriptions',
            'business_rules',
            'field_relationships',
            'query_patterns',
            'data_quality_rules',
            'ai_prompt_templates'
        ]
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        table_checks = []
        for table in required_tables:
            if table in existing_tables:
                print(f"  ✅ 表 {table} 存在")
                table_checks.append(True)
            else:
                print(f"  ❌ 表 {table} 不存在")
                table_checks.append(False)
        
        # 检查column_descriptions表的新增字段
        if 'column_descriptions' in existing_tables:
            cursor.execute("PRAGMA table_info(column_descriptions)")
            columns = [col[1] for col in cursor.fetchall()]
            
            new_fields = [
                'field_category',
                'usage_scenarios', 
                'common_values',
                'related_fields',
                'calculation_rules',
                'ai_prompt_hints'
            ]
            
            field_checks = []
            for field in new_fields:
                if field in columns:
                    print(f"  ✅ 字段 {field} 存在")
                    field_checks.append(True)
                else:
                    print(f"  ❌ 字段 {field} 不存在")
                    field_checks.append(False)
            
            table_checks.extend(field_checks)
        
        conn.close()
        return all(table_checks)
        
    except Exception as e:
        print(f"  ❌ 数据库验证失败: {e}")
        return False

def verify_configuration():
    """验证配置文件"""
    print("🔍 验证配置文件...")
    
    config_file = "chatdb/backend/app/core/config.py"
    env_file = "chatdb/.env"
    
    checks = []
    
    # 检查config.py
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_content = f.read()
            
            required_configs = [
                'ENABLE_ENHANCED_PROMPTS',
                'ENHANCED_PROMPT_VERSION',
                'ENABLE_DATA_QUALITY_CHECK',
                'AI_PROMPT_TEMPLATE_VERSION',
                'ENABLE_QUERY_PATTERN_MATCHING'
            ]
            
            for config in required_configs:
                if config in config_content:
                    print(f"  ✅ 配置项 {config} 存在")
                    checks.append(True)
                else:
                    print(f"  ❌ 配置项 {config} 不存在")
                    checks.append(False)
                    
        except Exception as e:
            print(f"  ❌ 读取config.py失败: {e}")
            checks.append(False)
    else:
        print("  ❌ config.py 文件不存在")
        checks.append(False)
    
    # 检查.env文件
    if os.path.exists(env_file):
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                env_content = f.read()
            
            required_env_vars = [
                'ENABLE_ENHANCED_PROMPTS=true',
                'ENHANCED_PROMPT_VERSION=v2.0',
                'ENABLE_DATA_QUALITY_CHECK=true'
            ]
            
            for env_var in required_env_vars:
                if env_var in env_content:
                    print(f"  ✅ 环境变量 {env_var} 设置正确")
                    checks.append(True)
                else:
                    print(f"  ❌ 环境变量 {env_var} 设置不正确")
                    checks.append(False)
                    
        except Exception as e:
            print(f"  ❌ 读取.env失败: {e}")
            checks.append(False)
    else:
        print("  ❌ .env 文件不存在")
        checks.append(False)
    
    return all(checks)

def verify_imports():
    """验证导入是否正确"""
    print("🔍 验证导入关系...")
    
    # 检查关键文件的导入
    files_to_check = [
        "chatdb/backend/app/services/text2sql_service.py",
        "chatdb/backend/app/services/enhanced_prompt_service.py"
    ]
    
    checks = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if file_path.endswith('text2sql_service.py'):
                    if "from app.services.enhanced_prompt_service import EnhancedPromptService" in content:
                        print(f"  ✅ {file_path} 导入正确")
                        checks.append(True)
                    else:
                        print(f"  ❌ {file_path} 导入不正确")
                        checks.append(False)
                
                elif file_path.endswith('enhanced_prompt_service.py'):
                    if "from app.core.config import settings" in content:
                        print(f"  ✅ {file_path} 导入正确")
                        checks.append(True)
                    else:
                        print(f"  ❌ {file_path} 导入不正确")
                        checks.append(False)
                        
            except Exception as e:
                print(f"  ❌ 检查 {file_path} 失败: {e}")
                checks.append(False)
        else:
            print(f"  ❌ 文件不存在: {file_path}")
            checks.append(False)
    
    return all(checks)

def run_final_verification():
    """运行最终兼容性验证"""
    print("🔍 智能数据分析系统最终兼容性验证")
    print("=" * 60)
    
    verifications = [
        ("函数签名验证", verify_function_signatures),
        ("ORM模型验证", verify_orm_models),
        ("数据库结构验证", verify_database_structure),
        ("配置文件验证", verify_configuration),
        ("导入关系验证", verify_imports)
    ]
    
    success_count = 0
    total_verifications = len(verifications)
    
    for verification_name, verification_function in verifications:
        print(f"\n{success_count + 1}. {verification_name}")
        print("-" * 40)
        
        try:
            if verification_function():
                success_count += 1
                print(f"  ✅ {verification_name} 通过")
            else:
                print(f"  ❌ {verification_name} 失败")
        except Exception as e:
            print(f"  ❌ {verification_name} 异常: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"🎯 验证结果: {success_count}/{total_verifications}")
    
    if success_count == total_verifications:
        print("🎉 所有兼容性验证通过！")
        print("\n✅ 系统状态:")
        print("  - 函数参数兼容 ✅")
        print("  - ORM模型完整 ✅")
        print("  - 数据库结构正确 ✅")
        print("  - 配置文件完整 ✅")
        print("  - 导入关系正确 ✅")
        print("\n🚀 系统已准备就绪，可以安全部署！")
        
        print("\n💡 建议的下一步操作:")
        print("1. 重启后端服务: cd chatdb/backend && python main.py")
        print("2. 测试增强功能: 发起一个财务查询")
        print("3. 监控系统性能和查询质量")
        
    else:
        print("⚠️ 部分验证失败，请检查上述错误信息")
        print("\n🔧 建议的修复步骤:")
        print("1. 检查失败的验证项")
        print("2. 手动修复相关问题")
        print("3. 重新运行验证脚本")
    
    return success_count == total_verifications

if __name__ == "__main__":
    run_final_verification()
