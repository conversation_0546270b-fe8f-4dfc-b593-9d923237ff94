#!/usr/bin/env python3
"""
详细测试Schema检索功能
"""
import asyncio
import sys
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent / "chatdb" / "backend"
sys.path.insert(0, str(project_root))

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

async def test_with_detailed_logging():
    """带详细日志的测试"""
    print("🧪 详细Schema检索测试")
    print("=" * 50)
    
    try:
        from app.db.session import SessionLocal
        from app.services.text2sql_utils import retrieve_relevant_schema
        
        # 创建数据库会话
        db = SessionLocal()
        
        try:
            # 测试参数
            connection_id = 1
            test_query = "查询销售数据"
            
            print(f"测试参数:")
            print(f"  连接ID: {connection_id}")
            print(f"  查询: {test_query}")
            print()
            
            # 执行Schema检索（会输出详细日志）
            print("🚀 开始执行Schema检索（查看日志输出）...")
            result = await retrieve_relevant_schema(db, connection_id, test_query)
            
            # 分析结果
            print("\n📊 详细结果分析:")
            print(f"  找到表数量: {len(result.get('tables', []))}")
            print(f"  找到列数量: {len(result.get('columns', []))}")
            print(f"  找到关系数量: {len(result.get('relationships', []))}")
            
            # 检查监控信息
            if '_monitoring' in result:
                monitoring = result['_monitoring']
                print(f"\n📈 监控信息:")
                print(f"  总耗时: {monitoring.get('total_duration', 'N/A')}")
                print(f"  数据源: {monitoring.get('source', 'N/A')}")
                
                # 显示管道各阶段详细状态
                stages = monitoring.get('pipeline_stages', {})
                if stages:
                    print(f"  管道阶段详情:")
                    for stage_name, stage_info in stages.items():
                        status = stage_info.get('status', 'UNKNOWN')
                        duration = stage_info.get('duration', 'N/A')
                        print(f"    {stage_name}:")
                        print(f"      状态: {status}")
                        print(f"      耗时: {duration}s")
                        
                        # 显示特定阶段的额外信息
                        if stage_name == 'column_retrieval':
                            print(f"      实体数量: {stage_info.get('entities_processed', 'N/A')}")
                            print(f"      找到列数: {stage_info.get('columns_found', 'N/A')}")
                            print(f"      检索方法: {stage_info.get('retrieval_method', 'N/A')}")
                        elif stage_name == 'base_tables_fetch':
                            print(f"      表数量: {stage_info.get('tables_found', 'N/A')}")
                        elif stage_name == 'semantic_search':
                            print(f"      相关表数: {stage_info.get('relevant_tables_found', 'N/A')}")
                
                # 显示错误和警告
                errors = monitoring.get('errors', [])
                warnings = monitoring.get('warnings', [])
                if errors:
                    print(f"  ❌ 错误 ({len(errors)}):")
                    for error in errors:
                        print(f"    - {error}")
                if warnings:
                    print(f"  ⚠️ 警告 ({len(warnings)}):")
                    for warning in warnings:
                        print(f"    - {warning}")
            
            # 显示实际数据
            if result.get('tables'):
                print(f"\n📋 找到的表:")
                for i, table in enumerate(result['tables'][:5]):
                    print(f"  {i+1}. {table.get('name', 'N/A')} (ID: {table.get('id', 'N/A')})")
                    if len(result['tables']) > 5:
                        print(f"  ... 还有 {len(result['tables']) - 5} 个表")
            
            if result.get('columns'):
                print(f"\n🏛️ 找到的列:")
                for i, column in enumerate(result['columns'][:10]):
                    print(f"  {i+1}. {column.get('table_name', 'N/A')}.{column.get('name', 'N/A')} ({column.get('type', 'N/A')})")
                if len(result['columns']) > 10:
                    print(f"  ... 还有 {len(result['columns']) - 10} 个列")
            else:
                print(f"\n❌ 未找到任何列数据")
            
            # 检查是否使用了回退方案
            if result.get('_fallback'):
                print(f"\n⚠️ 使用了回退方案:")
                print(f"  原因: {result.get('_fallback_reason', '未知原因')}")
                if result.get('_original_error'):
                    print(f"  原始错误: {result.get('_original_error')}")
            
            return len(result.get('columns', [])) > 0
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    success = await test_with_detailed_logging()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试成功 - 找到了列数据！")
    else:
        print("❌ 测试失败 - 未找到列数据")

if __name__ == "__main__":
    asyncio.run(main())
