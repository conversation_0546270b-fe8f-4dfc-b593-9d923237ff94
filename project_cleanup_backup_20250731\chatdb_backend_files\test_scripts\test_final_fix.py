#!/usr/bin/env python3
"""
测试最终修复效果
模拟用户查询，验证系统是否能正确处理
"""

import requests
import json

def test_query_api():
    """测试查询API"""
    
    # API配置
    API_BASE = "http://localhost:8000/api/v1"
    
    # 测试查询
    test_query = "查询2024年5月各公司的管理费用合计"
    
    print(f"🔍 测试查询: {test_query}")
    print("=" * 50)
    
    try:
        # 发送查询请求
        response = requests.post(
            f"{API_BASE}/query/",
            json={
                "connection_id": 1,
                "natural_language_query": test_query
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ 查询成功!")
            print(f"📊 生成的SQL:")
            print(result.get('sql_query', '未找到SQL'))
            print()
            
            if result.get('success') and result.get('data'):
                print("📈 查询结果:")
                data = result['data']
                if isinstance(data, list) and len(data) > 0:
                    # 显示前几行结果
                    for i, row in enumerate(data[:5]):
                        print(f"  {i+1}. {row}")
                    if len(data) > 5:
                        print(f"  ... 还有 {len(data) - 5} 行数据")
                else:
                    print("  无数据返回")
            else:
                print("❌ 查询执行失败:")
                print(result.get('error', '未知错误'))
                
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求错误: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

def test_schema_retrieval():
    """测试表结构检索"""
    
    print("\n" + "=" * 50)
    print("🔍 测试表结构检索")
    print("=" * 50)
    
    try:
        # 这里可以添加直接测试表结构检索的代码
        # 由于需要导入项目模块，暂时跳过
        print("⏭️  表结构检索测试需要在项目环境中运行")
        
    except Exception as e:
        print(f"❌ 表结构检索测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试最终修复效果...")
    
    # 测试API查询
    test_query_api()
    
    # 测试表结构检索
    test_schema_retrieval()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成!")
    print("=" * 50)
