#!/usr/bin/env python3
"""
调试Schema检索问题
"""
import asyncio
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent / "chatdb" / "backend"
sys.path.insert(0, str(project_root))

async def debug_llm_analysis():
    """调试LLM分析"""
    print("🧠 调试LLM查询分析")
    print("=" * 40)
    
    try:
        from app.services.text2sql_utils import analyze_query_with_llm
        
        test_queries = [
            "查询销售数据",
            "显示所有客户信息",
            "统计财务收入",
            "查看用户订单"
        ]
        
        for query in test_queries:
            print(f"\n📝 查询: {query}")
            try:
                analysis = await analyze_query_with_llm(query)
                print(f"  实体: {analysis.get('entities', [])}")
                print(f"  意图: {analysis.get('intent', 'unknown')}")
                print(f"  实体数量: {len(analysis.get('entities', []))}")
            except Exception as e:
                print(f"  ❌ LLM分析失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM分析调试失败: {e}")
        return False

async def debug_column_query():
    """调试列查询"""
    print("\n🏛️ 调试列查询逻辑")
    print("=" * 40)
    
    try:
        from app.services.neo4j_connection_pool import get_neo4j_pool
        
        pool = await get_neo4j_pool()
        connection_id = 1
        
        # 测试1: 直接查询所有列
        print("📊 测试1: 查询连接ID 1 的所有列")
        all_columns = await pool.execute_read_query("""
            MATCH (t:Table {connection_id: $connection_id})-[:HAS_COLUMN]->(c:Column)
            RETURN c.id AS id, c.name AS name, c.type AS type, c.description AS description,
                   c.is_pk AS is_pk, c.is_fk AS is_fk, t.id AS table_id, t.name AS table_name
            LIMIT 10
        """, {'connection_id': connection_id})
        
        print(f"  找到列数量: {len(all_columns)}")
        for i, col in enumerate(all_columns[:5]):
            print(f"    {i+1}. {col['table_name']}.{col['name']} ({col['type']})")
        
        # 测试2: 使用实体搜索列
        print("\n📊 测试2: 使用实体搜索列")
        test_entities = ["销售", "客户", "订单", "收入", "financial", "data"]
        
        for entity in test_entities:
            columns = await pool.execute_read_query("""
                MATCH (c:Column {connection_id: $connection_id})
                WHERE toLower(c.name) CONTAINS $entity OR toLower(c.description) CONTAINS $entity
                MATCH (t:Table)-[:HAS_COLUMN]->(c)
                RETURN c.id AS id, c.name AS name, c.type AS type, c.description AS description,
                       c.is_pk AS is_pk, c.is_fk AS is_fk, t.id AS table_id, t.name AS table_name
                LIMIT 5
            """, {
                'connection_id': connection_id,
                'entity': entity.lower()
            })
            
            print(f"  实体 '{entity}': {len(columns)} 列")
            for col in columns[:2]:
                print(f"    - {col['table_name']}.{col['name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 列查询调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def debug_final_result_building():
    """调试最终结果构建"""
    print("\n🔧 调试最终结果构建")
    print("=" * 40)
    
    try:
        from app.db.session import SessionLocal
        from app import crud
        
        db = SessionLocal()
        try:
            connection_id = 1
            
            # 检查SQLite中的数据获取
            print("📊 SQLite数据获取测试:")
            
            # 获取表
            tables = crud.schema_table.get_by_connection(db, connection_id=connection_id)
            print(f"  表数量: {len(tables)}")
            
            if tables:
                # 获取第一个表的列
                first_table = tables[0]
                columns = crud.schema_column.get_by_table(db, table_id=first_table.id)
                print(f"  表 '{first_table.table_name}' 的列数量: {len(columns)}")
                
                for i, col in enumerate(columns[:5]):
                    print(f"    {i+1}. {col.column_name} ({col.data_type})")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 结果构建调试失败: {e}")
        return False

async def test_modified_schema_retrieval():
    """测试修改后的Schema检索"""
    print("\n🧪 测试修改后的Schema检索")
    print("=" * 40)
    
    try:
        from app.db.session import SessionLocal
        from app.services.neo4j_connection_pool import get_neo4j_pool
        from app import crud
        
        db = SessionLocal()
        pool = await get_neo4j_pool()
        
        try:
            connection_id = 1
            query = "查询销售数据"
            
            print(f"测试参数: 连接ID={connection_id}, 查询='{query}'")
            
            # 步骤1: 获取所有表
            all_tables = await pool.execute_read_query("""
                MATCH (t:Table {connection_id: $connection_id})
                RETURN t.id AS id, t.name AS name, t.description AS description
            """, {'connection_id': connection_id})
            
            print(f"步骤1 - 获取表: {len(all_tables)} 个")
            
            # 步骤2: 直接获取所有表的列（不依赖实体匹配）
            if all_tables:
                table_ids = [t["id"] for t in all_tables]
                
                all_columns_data = await pool.execute_read_query("""
                    MATCH (t:Table {connection_id: $connection_id})-[:HAS_COLUMN]->(c:Column)
                    WHERE t.id IN $table_ids
                    RETURN c.id AS id, c.name AS name, c.type AS type, c.description AS description,
                           c.is_pk AS is_pk, c.is_fk AS is_fk, t.id AS table_id, t.name AS table_name
                """, {
                    'connection_id': connection_id,
                    'table_ids': table_ids
                })
                
                print(f"步骤2 - 获取列: {len(all_columns_data)} 个")
                
                # 构建结果
                tables_list = [{"id": t["id"], "name": t["name"], "description": t["description"]} for t in all_tables]
                
                columns_list = []
                for col in all_columns_data:
                    columns_list.append({
                        "id": col["id"],
                        "name": col["name"],
                        "type": col["type"],
                        "description": col["description"],
                        "is_primary_key": col["is_pk"],
                        "is_foreign_key": col["is_fk"],
                        "table_id": col["table_id"],
                        "table_name": col["table_name"]
                    })
                
                print(f"最终结果: {len(tables_list)} 表, {len(columns_list)} 列")
                
                if columns_list:
                    print("✅ 成功获取列数据！")
                    print("前5个列:")
                    for i, col in enumerate(columns_list[:5]):
                        print(f"  {i+1}. {col['table_name']}.{col['name']} ({col['type']})")
                    return True
                else:
                    print("❌ 仍然没有获取到列数据")
                    return False
            else:
                print("❌ 没有获取到表数据")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🔍 Schema检索问题深度调试")
    print("=" * 60)
    
    # 调试LLM分析
    llm_ok = await debug_llm_analysis()
    
    # 调试列查询
    column_ok = await debug_column_query()
    
    # 调试结果构建
    result_ok = await debug_final_result_building()
    
    # 测试修改后的逻辑
    modified_ok = await test_modified_schema_retrieval()
    
    print("\n" + "=" * 60)
    print("📊 调试结果总结:")
    print(f"  LLM分析: {'✅ 正常' if llm_ok else '❌ 异常'}")
    print(f"  列查询: {'✅ 正常' if column_ok else '❌ 异常'}")
    print(f"  结果构建: {'✅ 正常' if result_ok else '❌ 异常'}")
    print(f"  修改后逻辑: {'✅ 成功' if modified_ok else '❌ 失败'}")
    
    if modified_ok:
        print("\n💡 建议: 修改Schema检索逻辑，不要完全依赖LLM实体识别来获取列数据")
    else:
        print("\n⚠️ 需要进一步调查问题根因")

if __name__ == "__main__":
    asyncio.run(main())
