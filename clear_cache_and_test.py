#!/usr/bin/env python3
"""
清空缓存并重新测试Schema检索
"""
import asyncio
import sys
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent / "chatdb" / "backend"
sys.path.insert(0, str(project_root))

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

async def clear_cache():
    """清空缓存"""
    print("🧹 清空缓存")
    print("=" * 30)
    
    try:
        from app.services.enhanced_cache_service import enhanced_cache
        
        # 清空预加载缓存
        enhanced_cache.preload_cache.clear()
        print("✅ 预加载缓存已清空")
        
        # 清空查询模式缓存
        enhanced_cache.query_patterns.clear()
        print("✅ 查询模式缓存已清空")
        
        # 尝试清空基础缓存（如果支持）
        if hasattr(enhanced_cache.base_cache, 'clear'):
            enhanced_cache.base_cache.clear()
            print("✅ 基础缓存已清空")
        elif hasattr(enhanced_cache.base_cache, '_cache'):
            enhanced_cache.base_cache._cache.clear()
            print("✅ 基础缓存已清空（通过_cache属性）")
        else:
            print("⚠️ 无法清空基础缓存（不支持clear方法）")
        
        print("🎉 缓存清空完成")
        return True
        
    except Exception as e:
        print(f"❌ 缓存清空失败: {e}")
        return False

async def test_schema_retrieval_no_cache():
    """测试Schema检索（无缓存）"""
    print("\n🧪 测试Schema检索（无缓存）")
    print("=" * 40)
    
    try:
        from app.db.session import SessionLocal
        from app.services.text2sql_utils import retrieve_relevant_schema
        
        # 创建数据库会话
        db = SessionLocal()
        
        try:
            # 测试参数
            connection_id = 1
            test_query = "查询销售数据"
            
            print(f"测试参数:")
            print(f"  连接ID: {connection_id}")
            print(f"  查询: {test_query}")
            print()
            
            # 执行Schema检索
            print("🚀 开始执行Schema检索（应该不会命中缓存）...")
            result = await retrieve_relevant_schema(db, connection_id, test_query)
            
            # 分析结果
            print("\n📊 检索结果:")
            print(f"  找到表数量: {len(result.get('tables', []))}")
            print(f"  找到列数量: {len(result.get('columns', []))}")
            print(f"  找到关系数量: {len(result.get('relationships', []))}")
            
            # 检查数据源
            if '_monitoring' in result:
                monitoring = result['_monitoring']
                source = monitoring.get('source', 'UNKNOWN')
                print(f"  数据源: {source}")
                
                # 显示管道阶段
                stages = monitoring.get('pipeline_stages', {})
                if stages:
                    print(f"  执行的管道阶段:")
                    for stage_name, stage_info in stages.items():
                        status = stage_info.get('status', 'UNKNOWN')
                        print(f"    {stage_name}: {status}")
                        
                        # 特别关注列检索阶段
                        if stage_name == 'column_retrieval':
                            columns_found = stage_info.get('columns_found', 0)
                            method = stage_info.get('retrieval_method', 'unknown')
                            print(f"      找到列数: {columns_found}")
                            print(f"      检索方法: {method}")
            
            # 显示列数据样例
            if result.get('columns'):
                print(f"\n🏛️ 列数据样例 (前10个):")
                for i, column in enumerate(result['columns'][:10]):
                    print(f"  {i+1}. {column.get('table_name', 'N/A')}.{column.get('name', 'N/A')} ({column.get('type', 'N/A')})")
                
                if len(result['columns']) > 10:
                    print(f"  ... 还有 {len(result['columns']) - 10} 个列")
                
                return True
            else:
                print(f"\n❌ 仍然未找到列数据")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def verify_neo4j_data():
    """验证Neo4j中的数据"""
    print("\n🔍 验证Neo4j数据")
    print("=" * 30)
    
    try:
        from app.services.neo4j_connection_pool import get_neo4j_pool
        
        pool = await get_neo4j_pool()
        
        # 检查连接1的表和列数据
        result = await pool.execute_read_query("""
            MATCH (t:Table {connection_id: 1})-[:HAS_COLUMN]->(c:Column)
            RETURN t.name as table_name, count(c) as column_count
            ORDER BY column_count DESC
            LIMIT 10
        """)
        
        print("连接ID 1 的表-列统计:")
        total_columns = 0
        for record in result:
            print(f"  {record['table_name']}: {record['column_count']} 列")
            total_columns += record['column_count']
        
        print(f"总列数: {total_columns}")
        
        return total_columns > 0
        
    except Exception as e:
        print(f"❌ Neo4j数据验证失败: {e}")
        return False

async def main():
    print("🔧 缓存清理和Schema检索修复测试")
    print("=" * 60)
    
    # 步骤1: 验证Neo4j数据
    neo4j_ok = await verify_neo4j_data()
    
    if not neo4j_ok:
        print("❌ Neo4j中没有列数据，无法继续测试")
        return
    
    # 步骤2: 清空缓存
    cache_cleared = await clear_cache()
    
    if not cache_cleared:
        print("⚠️ 缓存清空失败，但继续测试")
    
    # 步骤3: 重新测试Schema检索
    test_success = await test_schema_retrieval_no_cache()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"  Neo4j数据: {'✅ 正常' if neo4j_ok else '❌ 异常'}")
    print(f"  缓存清理: {'✅ 成功' if cache_cleared else '❌ 失败'}")
    print(f"  Schema检索: {'✅ 成功' if test_success else '❌ 失败'}")
    
    if test_success:
        print("\n🎉 问题已解决！Schema检索现在可以正常获取列数据。")
        print("💡 建议：定期清理缓存以避免类似问题。")
    else:
        print("\n⚠️ 问题仍然存在，需要进一步调查。")

if __name__ == "__main__":
    asyncio.run(main())
