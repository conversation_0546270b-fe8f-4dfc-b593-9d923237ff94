#!/usr/bin/env python3
"""
诊断字段映射没有生效的问题
"""

import sqlite3
from typing import Dict, Any

def check_database_connections():
    """检查数据库连接配置"""
    
    print("🔍 诊断字段映射问题 - 数据库连接检查")
    print("=" * 70)
    
    resource_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(resource_path)
    cursor = conn.cursor()
    
    # 检查数据库连接配置
    cursor.execute('SELECT * FROM dbconnection')
    connections = cursor.fetchall()
    
    print("📊 数据库连接配置:")
    for conn_data in connections:
        conn_id, name, db_type, host, port, username, password, db_name, created_at, updated_at = conn_data
        print(f"\n🔗 连接 {conn_id}: {name}")
        print(f"   路径: {db_name}")
        
        # 检查该连接下的表
        cursor.execute('SELECT id, table_name FROM schematable WHERE connection_id = ?', (conn_id,))
        tables = cursor.fetchall()
        
        if tables:
            print(f"   包含表:")
            for table_id, table_name in tables:
                print(f"      表ID {table_id}: {table_name}")
                
                # 检查该表的字段
                cursor.execute('SELECT id, column_name FROM schemacolumn WHERE table_id = ?', (table_id,))
                columns = cursor.fetchall()
                
                if columns:
                    print(f"         字段 ({len(columns)}个): {[col[1] for col in columns[:5]]}{'...' if len(columns) > 5 else ''}")
    
    conn.close()

def check_field_mappings():
    """检查字段映射配置"""
    
    print(f"\n" + "=" * 70)
    print("🔗 字段映射配置检查")
    print("=" * 70)
    
    resource_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(resource_path)
    cursor = conn.cursor()
    
    # 检查valuemapping表的配置
    cursor.execute('''
        SELECT vm.id, vm.column_id, vm.nl_term, vm.db_value,
               sc.column_name, st.table_name, dc.name as connection_name
        FROM valuemapping vm
        JOIN schemacolumn sc ON vm.column_id = sc.id
        JOIN schematable st ON sc.table_id = st.id
        JOIN dbconnection dc ON st.connection_id = dc.id
        WHERE dc.name = 'fin_data'
        ORDER BY sc.column_name, vm.nl_term
    ''')
    
    mappings = cursor.fetchall()
    
    if mappings:
        print(f"📋 fin_data数据库的字段映射 ({len(mappings)}个):")
        
        current_column = None
        for mapping in mappings:
            vm_id, column_id, nl_term, db_value, column_name, table_name, connection_name = mapping
            
            if column_name != current_column:
                current_column = column_name
                print(f"\n📊 {table_name}.{column_name}:")
            
            print(f"   '{nl_term}' → {db_value}")
    else:
        print("❌ 没有找到fin_data数据库的字段映射！")
        
        # 检查是否有任何映射
        cursor.execute('SELECT COUNT(*) FROM valuemapping')
        total_mappings = cursor.fetchone()[0]
        print(f"   总映射数: {total_mappings}")
        
        if total_mappings > 0:
            print("   问题可能是：映射没有关联到fin_data数据库")
    
    conn.close()

def check_meta_descriptions():
    """检查元数据描述配置"""
    
    print(f"\n" + "=" * 70)
    print("📋 元数据描述配置检查")
    print("=" * 70)
    
    resource_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(resource_path)
    cursor = conn.cursor()
    
    # 检查meta_column_descriptions表
    cursor.execute('''
        SELECT table_name, column_name, chinese_name, ai_understanding_points
        FROM meta_column_descriptions
        WHERE table_name = 'financial_data'
        ORDER BY column_name
    ''')
    
    descriptions = cursor.fetchall()
    
    if descriptions:
        print(f"📊 financial_data表的元数据描述 ({len(descriptions)}个):")
        
        for desc in descriptions:
            table_name, column_name, chinese_name, ai_points = desc
            print(f"\n📋 {column_name}")
            print(f"   中文名: {chinese_name}")
            if ai_points:
                print(f"   AI要点: {ai_points}")
    else:
        print("❌ 没有找到financial_data表的元数据描述！")
    
    conn.close()

def check_actual_financial_data_structure():
    """检查实际的financial_data表结构"""
    
    print(f"\n" + "=" * 70)
    print("📊 实际financial_data表结构检查")
    print("=" * 70)
    
    fin_data_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/fin_data.db'
    
    try:
        conn = sqlite3.connect(fin_data_path)
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute('PRAGMA table_info(financial_data)')
        columns = cursor.fetchall()
        
        print("📋 financial_data表的实际字段:")
        for col in columns:
            col_id, col_name, col_type, not_null, default_val, is_pk = col
            pk_mark = '🔑' if is_pk else '  '
            print(f"   {pk_mark} {col_name} ({col_type})")
        
        # 检查是否存在问题字段
        problem_fields = ['company_id', 'expense_date']
        existing_fields = [col[1] for col in columns]
        
        print(f"\n🔍 问题字段检查:")
        for field in problem_fields:
            if field in existing_fields:
                print(f"   ✅ {field}: 存在")
            else:
                print(f"   ❌ {field}: 不存在")
        
        # 检查正确字段
        correct_fields = ['accounting_unit_name', 'year', 'month', 'debit_amount', 'account_code']
        print(f"\n✅ 正确字段检查:")
        for field in correct_fields:
            if field in existing_fields:
                print(f"   ✅ {field}: 存在")
            else:
                print(f"   ❌ {field}: 不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 无法访问fin_data.db: {e}")

def diagnose_mapping_chain():
    """诊断映射链路"""
    
    print(f"\n" + "=" * 70)
    print("🔍 映射链路诊断")
    print("=" * 70)
    
    resource_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(resource_path)
    cursor = conn.cursor()
    
    print("📊 映射链路检查:")
    
    # 1. 检查fin_data连接是否存在
    cursor.execute('SELECT id, name FROM dbconnection WHERE name = ?', ('fin_data',))
    fin_data_conn = cursor.fetchone()
    
    if fin_data_conn:
        conn_id, conn_name = fin_data_conn
        print(f"✅ 1. fin_data连接存在 (ID: {conn_id})")
        
        # 2. 检查financial_data表是否存在
        cursor.execute('SELECT id, table_name FROM schematable WHERE connection_id = ? AND table_name = ?', 
                      (conn_id, 'financial_data'))
        financial_table = cursor.fetchone()
        
        if financial_table:
            table_id, table_name = financial_table
            print(f"✅ 2. financial_data表存在 (ID: {table_id})")
            
            # 3. 检查关键字段是否存在
            key_fields = ['accounting_unit_name', 'year', 'month', 'debit_amount']
            cursor.execute('SELECT id, column_name FROM schemacolumn WHERE table_id = ?', (table_id,))
            columns = cursor.fetchall()
            
            existing_columns = {col[1]: col[0] for col in columns}
            
            print(f"✅ 3. 字段检查:")
            for field in key_fields:
                if field in existing_columns:
                    column_id = existing_columns[field]
                    print(f"   ✅ {field} (ID: {column_id})")
                    
                    # 4. 检查该字段的映射
                    cursor.execute('SELECT nl_term, db_value FROM valuemapping WHERE column_id = ?', (column_id,))
                    field_mappings = cursor.fetchall()
                    
                    if field_mappings:
                        print(f"      映射 ({len(field_mappings)}个): {[m[0] for m in field_mappings[:3]]}{'...' if len(field_mappings) > 3 else ''}")
                    else:
                        print(f"      ❌ 无映射")
                else:
                    print(f"   ❌ {field}: 不存在")
        else:
            print(f"❌ 2. financial_data表不存在")
    else:
        print(f"❌ 1. fin_data连接不存在")
    
    conn.close()

def provide_solutions():
    """提供解决方案"""
    
    print(f"\n" + "=" * 70)
    print("💡 问题解决方案")
    print("=" * 70)
    
    print("""
🎯 **问题根因分析**:

基于诊断结果，可能的问题包括：

1️⃣ **映射配置问题**:
   - valuemapping表中缺少关键字段的映射
   - 映射没有正确关联到fin_data数据库
   - 映射的术语不够全面

2️⃣ **系统配置问题**:
   - Text2SQL系统没有正确加载fin_data的映射
   - 系统可能在使用错误的数据库连接
   - 缓存没有更新

3️⃣ **提示构建问题**:
   - LLM提示中没有包含正确的字段映射信息
   - 元数据描述没有正确传递给LLM

---

🔧 **解决方案**:

**立即修复步骤**:

1️⃣ **检查和修复字段映射**:
   ```
   访问: http://172.18.208.1:3000/value-mappings
   选择数据库: fin_data
   选择表: financial_data
   
   必须添加的映射:
   - accounting_unit_name字段:
     * "公司" → accounting_unit_name
     * "企业" → accounting_unit_name  
     * "company_id" → accounting_unit_name
     * "company_name" → accounting_unit_name
   
   - year字段:
     * "年份" → year
     * "年" → year
     
   - month字段:
     * "月份" → month
     * "月" → month
   ```

2️⃣ **验证映射生效**:
   ```python
   # 在后端执行以下检查
   python -c "
   from chatdb.services.text2sql_service import get_value_mappings
   # 检查映射是否正确加载
   "
   ```

3️⃣ **清除缓存并重启**:
   ```
   重启后端服务，确保新的映射配置生效
   ```

4️⃣ **测试验证**:
   ```
   查询: "2024年1月各公司的销售费用合计"
   期望: 生成正确的SQL，使用accounting_unit_name而不是company_id
   ```

**深度修复步骤**:

1️⃣ **完善字段映射**:
   - 为所有常用术语添加映射
   - 特别关注容易混淆的字段名
   - 添加错误字段名到正确字段名的映射

2️⃣ **增强元数据描述**:
   - 在AI理解要点中明确说明字段的正确用法
   - 强调禁止使用的字段名

3️⃣ **优化提示构建**:
   - 确保字段映射信息正确传递给LLM
   - 在提示中明确禁止使用不存在的字段

---

🚨 **紧急修复命令**:

如果问题严重，可以直接在数据库中添加关键映射：

```sql
-- 连接到resource.db执行
INSERT INTO valuemapping (column_id, nl_term, db_value, created_at, updated_at)
SELECT sc.id, 'company_id', 'accounting_unit_name', datetime('now'), datetime('now')
FROM schemacolumn sc
JOIN schematable st ON sc.table_id = st.id  
JOIN dbconnection dc ON st.connection_id = dc.id
WHERE dc.name = 'fin_data' AND st.table_name = 'financial_data' 
AND sc.column_name = 'accounting_unit_name';

INSERT INTO valuemapping (column_id, nl_term, db_value, created_at, updated_at)
SELECT sc.id, 'expense_date', 'year', datetime('now'), datetime('now')
FROM schemacolumn sc
JOIN schematable st ON sc.table_id = st.id
JOIN dbconnection dc ON st.connection_id = dc.id  
WHERE dc.name = 'fin_data' AND st.table_name = 'financial_data'
AND sc.column_name = 'year';
```
""")

if __name__ == "__main__":
    check_database_connections()
    check_field_mappings()
    check_meta_descriptions()
    check_actual_financial_data_structure()
    diagnose_mapping_chain()
    provide_solutions()
    
    print(f"\n" + "=" * 70)
    print("🎯 下一步：根据诊断结果修复字段映射配置")
    print("=" * 70)
