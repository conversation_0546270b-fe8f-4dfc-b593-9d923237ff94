#!/usr/bin/env python3
"""
修复关键字段映射，确保所有常见的错误字段名都有正确的映射
"""

import sqlite3
from datetime import datetime

def fix_critical_mappings():
    """修复关键字段映射"""
    
    print("🔧 修复关键字段映射")
    print("=" * 60)
    
    resource_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(resource_path)
    cursor = conn.cursor()
    
    try:
        # 定义关键映射
        critical_mappings = [
            # accounting_unit_name字段的映射
            {
                'field': 'accounting_unit_name',
                'mappings': [
                    'company_id',
                    'company_name', 
                    'company',
                    'enterprise_id',
                    'enterprise_name',
                    'organization_id',
                    'organization_name',
                    'unit_id',
                    'unit_name'
                ]
            },
            # year字段的映射
            {
                'field': 'year',
                'mappings': [
                    'date',
                    'expense_date',
                    'transaction_date',
                    'record_date',
                    'fiscal_year',
                    'year_field'
                ]
            },
            # month字段的映射
            {
                'field': 'month',
                'mappings': [
                    'month_field',
                    'fiscal_month',
                    'period'
                ]
            },
            # debit_amount字段的映射
            {
                'field': 'debit_amount',
                'mappings': [
                    'expense_amount',
                    'cost_amount',
                    'debit_value',
                    'expense_value',
                    'cost_value'
                ]
            },
            # credit_amount字段的映射
            {
                'field': 'credit_amount',
                'mappings': [
                    'income_amount',
                    'revenue_amount',
                    'credit_value',
                    'income_value',
                    'revenue_value'
                ]
            }
        ]
        
        # 获取fin_data连接的字段ID
        cursor.execute('''
            SELECT sc.id, sc.column_name
            FROM schemacolumn sc
            JOIN schematable st ON sc.table_id = st.id
            JOIN dbconnection dc ON st.connection_id = dc.id
            WHERE dc.name = 'fin_data' AND st.table_name = 'financial_data'
        ''')
        
        field_ids = {row[1]: row[0] for row in cursor.fetchall()}
        
        print(f"📊 找到 {len(field_ids)} 个字段")
        
        # 添加关键映射
        total_added = 0
        
        for field_config in critical_mappings:
            field_name = field_config['field']
            mappings = field_config['mappings']
            
            if field_name not in field_ids:
                print(f"⚠️ 字段 {field_name} 不存在，跳过")
                continue
            
            column_id = field_ids[field_name]
            print(f"\n📋 处理字段: {field_name} (ID: {column_id})")
            
            added_count = 0
            for nl_term in mappings:
                try:
                    # 检查映射是否已存在
                    cursor.execute('''
                        SELECT COUNT(*) FROM valuemapping 
                        WHERE column_id = ? AND nl_term = ?
                    ''', (column_id, nl_term))
                    
                    if cursor.fetchone()[0] == 0:
                        # 添加新映射
                        cursor.execute('''
                            INSERT INTO valuemapping (column_id, nl_term, db_value, created_at, updated_at)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (column_id, nl_term, field_name, datetime.now(), datetime.now()))
                        
                        added_count += 1
                        total_added += 1
                        print(f"   ✅ 添加: '{nl_term}' → {field_name}")
                    else:
                        print(f"   ⏭️ 跳过: '{nl_term}' (已存在)")
                        
                except Exception as e:
                    print(f"   ❌ 添加映射失败: {nl_term} - {e}")
            
            print(f"   📊 {field_name}: 新增 {added_count} 个映射")
        
        # 提交更改
        conn.commit()
        print(f"\n✅ 修复完成！总共添加了 {total_added} 个关键映射")
        
        # 验证修复结果
        print(f"\n🔍 验证修复结果:")
        
        # 检查关键术语的映射
        key_terms = ['company_id', 'company_name', 'date', 'expense_date']
        
        for term in key_terms:
            cursor.execute('''
                SELECT vm.nl_term, vm.db_value, sc.column_name
                FROM valuemapping vm
                JOIN schemacolumn sc ON vm.column_id = sc.id
                JOIN schematable st ON sc.table_id = st.id
                JOIN dbconnection dc ON st.connection_id = dc.id
                WHERE dc.name = 'fin_data' AND vm.nl_term = ?
            ''', (term,))
            
            results = cursor.fetchall()
            if results:
                for result in results:
                    nl_term, db_value, column_name = result
                    print(f"   ✅ '{nl_term}' → {db_value} (字段: {column_name})")
            else:
                print(f"   ❌ '{term}': 没有找到映射")
        
        # 统计总映射数
        cursor.execute('''
            SELECT COUNT(*) FROM valuemapping vm
            JOIN schemacolumn sc ON vm.column_id = sc.id
            JOIN schematable st ON sc.table_id = st.id
            JOIN dbconnection dc ON st.connection_id = dc.id
            WHERE dc.name = 'fin_data'
        ''')
        
        total_mappings = cursor.fetchone()[0]
        print(f"\n📊 fin_data数据库总映射数: {total_mappings}")
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        conn.rollback()
    finally:
        conn.close()

def test_mapping_effectiveness():
    """测试映射有效性"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试映射有效性")
    print("=" * 60)
    
    # 模拟process_sql_with_value_mappings函数的简化版本
    import re
    
    def simple_field_replacement(sql: str, mappings: dict) -> str:
        """简化的字段替换函数"""
        for nl_term, db_value in mappings.items():
            if nl_term == db_value:
                continue
            field_pattern = rf'\b{re.escape(nl_term)}\b'
            sql = re.sub(field_pattern, db_value, sql, flags=re.IGNORECASE)
        return sql
    
    # 测试映射
    test_mappings = {
        'company_id': 'accounting_unit_name',
        'company_name': 'accounting_unit_name',
        'date': 'year',
        'expense_date': 'year'
    }
    
    # 测试SQL
    test_sqls = [
        "SELECT company_id, SUM(debit_amount) FROM financial_data WHERE date = '2024-01-01'",
        "SELECT company_name, debit_amount FROM financial_data WHERE expense_date BETWEEN '2024-01-01' AND '2024-01-31'",
        "SELECT company_id, company_name FROM financial_data GROUP BY company_id, company_name"
    ]
    
    print("📝 测试SQL替换:")
    
    for i, sql in enumerate(test_sqls, 1):
        print(f"\n{i}️⃣ 测试SQL {i}:")
        print(f"   原始: {sql}")
        
        fixed_sql = simple_field_replacement(sql, test_mappings)
        print(f"   修复: {fixed_sql}")
        
        # 检查是否还有问题字段
        problem_fields = ['company_id', 'company_name', 'date', 'expense_date']
        remaining_problems = [field for field in problem_fields if field in fixed_sql.lower()]
        
        if remaining_problems:
            print(f"   ❌ 仍有问题字段: {remaining_problems}")
        else:
            print(f"   ✅ 所有问题字段已替换")

def provide_next_steps():
    """提供下一步操作指导"""
    
    print(f"\n" + "=" * 60)
    print("🎯 下一步操作指导")
    print("=" * 60)
    
    print("""
✅ **映射修复已完成**，现在需要：

1️⃣ **重启后端服务**:
   ```bash
   # 停止当前服务 (Ctrl+C)
   # 重新启动
   cd chatdb/backend
   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2️⃣ **运行日志测试**:
   ```bash
   python test_logging_system.py
   ```

3️⃣ **测试实际查询**:
   - 查询: "2024年1月各公司的销售费用合计"
   - 期望: 不再出现 company_id 或 date 字段错误

4️⃣ **监控日志**:
   - 访问: http://localhost:8000/api/debug/text2sql-logs
   - 查看详细的处理流程
   - 确认映射替换是否生效

5️⃣ **如果问题仍存在**:
   - 检查日志中的 "SQL POST-PROCESSING" 部分
   - 确认 process_sql_with_value_mappings 是否被调用
   - 查看 LLM 生成的原始SQL和处理后的SQL对比

🔍 **调试要点**:
- 关注查询ID，便于追踪特定查询的完整流程
- 检查 "Available mappings" 数量是否正确
- 确认 "SQL was modified during processing" 消息
""")

if __name__ == "__main__":
    fix_critical_mappings()
    test_mapping_effectiveness()
    provide_next_steps()
    
    print(f"\n" + "=" * 60)
    print("🎯 关键映射修复完成！请重启服务并测试")
    print("=" * 60)
