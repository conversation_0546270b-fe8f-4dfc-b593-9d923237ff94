#!/usr/bin/env python3
"""
智能数据分析系统快速修复脚本
用于修复系统升级后的常见问题
"""

import os
import sys
import sqlite3
import json
import shutil
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "chatdb" / "backend"))

class SystemRepair:
    """系统修复器"""
    
    def __init__(self):
        self.backup_dir = Path(f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        self.repair_log = []
        
    def run_auto_repair(self):
        """运行自动修复"""
        print("🔧 开始系统自动修复...")
        print("=" * 60)
        
        # 创建备份目录
        self.backup_dir.mkdir(exist_ok=True)
        self._log("创建备份目录", f"备份目录: {self.backup_dir}")
        
        # 1. 修复环境配置
        self._repair_environment_config()
        
        # 2. 修复数据库连接
        self._repair_database_connections()
        
        # 3. 重建元数据表
        self._repair_metadata_tables()
        
        # 4. 清理和重建缓存
        self._repair_cache_system()
        
        # 5. 修复Neo4j连接配置
        self._repair_neo4j_config()
        
        # 6. 验证修复结果
        self._verify_repairs()
        
        # 7. 生成修复报告
        self._generate_repair_report()
        
        print("\n✅ 自动修复完成!")
        
    def _repair_environment_config(self):
        """修复环境配置"""
        print("\n📋 修复环境配置...")
        
        env_file = Path("chatdb/.env")
        
        # 备份原配置文件
        if env_file.exists():
            backup_env = self.backup_dir / ".env.backup"
            shutil.copy2(env_file, backup_env)
            self._log("备份配置文件", f"备份到: {backup_env}")
        
        # 检查并修复配置项
        config_updates = self._get_config_updates()
        
        if config_updates:
            self._update_env_file(env_file, config_updates)
            self._log("更新配置文件", f"更新了 {len(config_updates)} 个配置项")
        else:
            self._log("配置文件检查", "配置文件无需修复")
    
    def _get_config_updates(self):
        """获取需要更新的配置项"""
        updates = {}
        
        # 检查数据库配置
        if not Path("resource.db").exists():
            self._log("警告", "resource.db文件不存在，可能需要重新初始化")
        
        if not Path("fin_data.db").exists():
            self._log("警告", "fin_data.db文件不存在，可能需要导入数据")
        
        # 检查LLM配置升级
        env_file = Path("chatdb/.env")
        if env_file.exists():
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否还在使用旧的DeepSeek配置
            if "api.deepseek.com" in content:
                updates['OPENAI_API_BASE'] = 'https://dashscope.aliyuncs.com/compatible-mode/v1'
                updates['LLM_MODEL'] = 'qwen-max'
                self._log("配置升级", "检测到旧的DeepSeek配置，将升级到阿里云百炼")
        
        return updates
    
    def _update_env_file(self, env_file: Path, updates: dict):
        """更新环境配置文件"""
        if not env_file.exists():
            self._log("错误", f"配置文件不存在: {env_file}")
            return
        
        # 读取现有配置
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 更新配置项
        updated_lines = []
        updated_keys = set()
        
        for line in lines:
            line = line.strip()
            if '=' in line and not line.startswith('#'):
                key = line.split('=')[0].strip()
                if key in updates:
                    updated_lines.append(f"{key}={updates[key]}\n")
                    updated_keys.add(key)
                    self._log("配置更新", f"{key} -> {updates[key]}")
                else:
                    updated_lines.append(line + '\n')
            else:
                updated_lines.append(line + '\n')
        
        # 添加新的配置项
        for key, value in updates.items():
            if key not in updated_keys:
                updated_lines.append(f"{key}={value}\n")
                self._log("配置添加", f"{key} -> {value}")
        
        # 写回文件
        with open(env_file, 'w', encoding='utf-8') as f:
            f.writelines(updated_lines)
    
    def _repair_database_connections(self):
        """修复数据库连接"""
        print("\n🗄️ 修复数据库连接...")
        
        # 检查数据库文件权限
        db_files = ['resource.db', 'fin_data.db']
        
        for db_file in db_files:
            db_path = Path(db_file)
            if db_path.exists():
                # 检查文件权限
                if not os.access(db_path, os.R_OK | os.W_OK):
                    try:
                        os.chmod(db_path, 0o644)
                        self._log("权限修复", f"修复了 {db_file} 的文件权限")
                    except Exception as e:
                        self._log("权限修复失败", f"{db_file}: {e}")
                
                # 测试数据库连接
                try:
                    conn = sqlite3.connect(db_path, timeout=5)
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    conn.close()
                    self._log("数据库测试", f"{db_file} 连接正常")
                except Exception as e:
                    self._log("数据库错误", f"{db_file} 连接失败: {e}")
            else:
                self._log("文件缺失", f"{db_file} 文件不存在")
    
    def _repair_metadata_tables(self):
        """修复元数据表"""
        print("\n📊 修复元数据表...")
        
        metadata_db = Path("resource.db")
        if not metadata_db.exists():
            self._log("错误", "元数据库文件不存在，无法修复元数据表")
            return
        
        try:
            conn = sqlite3.connect(metadata_db)
            cursor = conn.cursor()
            
            # 检查并创建缺失的元数据表
            self._create_missing_metadata_tables(cursor)
            
            # 检查并修复表结构
            self._repair_metadata_table_structure(cursor)
            
            conn.commit()
            conn.close()
            
            self._log("元数据修复", "元数据表修复完成")
            
        except Exception as e:
            self._log("元数据修复失败", f"修复元数据表时出错: {e}")
    
    def _create_missing_metadata_tables(self, cursor):
        """创建缺失的元数据表"""
        
        # 表描述表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT,
                business_purpose TEXT,
                data_scale TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 字段描述表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS column_descriptions (
                table_name TEXT,
                column_name TEXT,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT,
                field_category TEXT DEFAULT '',
                usage_scenarios TEXT DEFAULT '',
                common_values TEXT DEFAULT '',
                related_fields TEXT DEFAULT '',
                calculation_rules TEXT DEFAULT '',
                ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )
        """)
        
        # 业务规则表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT,
                rule_category TEXT,
                rule_description TEXT,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'MEDIUM',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 查询模式表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS query_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_name TEXT,
                pattern_description TEXT,
                natural_language_examples TEXT,
                sql_template TEXT,
                required_fields TEXT,
                business_scenario TEXT,
                difficulty_level TEXT DEFAULT 'MEDIUM',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        self._log("表创建", "检查并创建了缺失的元数据表")
    
    def _repair_metadata_table_structure(self, cursor):
        """修复元数据表结构"""
        
        # 检查column_descriptions表是否有新增字段
        cursor.execute("PRAGMA table_info(column_descriptions)")
        columns = [row[1] for row in cursor.fetchall()]
        
        new_columns = [
            ('field_category', 'TEXT DEFAULT ""'),
            ('usage_scenarios', 'TEXT DEFAULT ""'),
            ('common_values', 'TEXT DEFAULT ""'),
            ('related_fields', 'TEXT DEFAULT ""'),
            ('calculation_rules', 'TEXT DEFAULT ""'),
            ('ai_prompt_hints', 'TEXT DEFAULT ""')
        ]
        
        for column_name, column_def in new_columns:
            if column_name not in columns:
                try:
                    cursor.execute(f"ALTER TABLE column_descriptions ADD COLUMN {column_name} {column_def}")
                    self._log("表结构更新", f"添加字段 {column_name} 到 column_descriptions 表")
                except Exception as e:
                    self._log("表结构更新失败", f"添加字段 {column_name} 失败: {e}")
    
    def _repair_cache_system(self):
        """修复缓存系统"""
        print("\n🚀 修复缓存系统...")
        
        try:
            # 尝试导入缓存服务
            from app.services.cache_service import CacheService
            
            cache_service = CacheService()
            
            # 清理所有缓存
            cache_service.clear()
            self._log("缓存清理", "清理了所有内存缓存")
            
            # 测试缓存功能
            test_key = "repair_test"
            test_value = {"repair": True}
            
            cache_service.set(test_key, test_value, 60)
            retrieved = cache_service.get(test_key)
            
            if retrieved == test_value:
                cache_service.delete(test_key)
                self._log("缓存测试", "缓存系统工作正常")
            else:
                self._log("缓存测试失败", "缓存系统可能存在问题")
                
        except ImportError as e:
            self._log("缓存修复失败", f"无法导入缓存服务: {e}")
        except Exception as e:
            self._log("缓存修复失败", f"缓存系统修复失败: {e}")
    
    def _repair_neo4j_config(self):
        """修复Neo4j连接配置"""
        print("\n🕸️ 修复Neo4j连接配置...")
        
        env_file = Path("chatdb/.env")
        if not env_file.exists():
            self._log("配置文件缺失", "无法修复Neo4j配置")
            return
        
        # 读取当前配置
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查Neo4j配置
        if "NEO4J_URI" in content:
            # 测试当前配置的连接
            try:
                from neo4j import GraphDatabase
                
                # 从环境变量获取配置
                neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
                neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
                neo4j_password = os.getenv('NEO4J_PASSWORD', 'password')
                
                driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
                with driver.session() as session:
                    session.run("RETURN 1")
                driver.close()
                
                self._log("Neo4j连接", "Neo4j连接正常")
                
            except ImportError:
                self._log("Neo4j驱动", "Neo4j驱动未安装")
            except Exception as e:
                self._log("Neo4j连接失败", f"连接失败: {e}")
                
                # 尝试修复为本地连接
                updates = {
                    'NEO4J_URI': 'bolt://localhost:7687',
                    'NEO4J_USER': 'neo4j',
                    'NEO4J_PASSWORD': 'password'
                }
                self._update_env_file(env_file, updates)
                self._log("Neo4j配置", "已重置为本地默认配置")
    
    def _verify_repairs(self):
        """验证修复结果"""
        print("\n✅ 验证修复结果...")
        
        verification_results = {
            'config_file': self._verify_config_file(),
            'database_connections': self._verify_database_connections(),
            'metadata_tables': self._verify_metadata_tables()
        }
        
        success_count = sum(1 for result in verification_results.values() if result)
        total_count = len(verification_results)
        
        self._log("修复验证", f"验证通过: {success_count}/{total_count}")
        
        return verification_results
    
    def _verify_config_file(self):
        """验证配置文件"""
        env_file = Path("chatdb/.env")
        if env_file.exists():
            self._log("配置验证", "配置文件存在")
            return True
        else:
            self._log("配置验证失败", "配置文件不存在")
            return False
    
    def _verify_database_connections(self):
        """验证数据库连接"""
        try:
            for db_file in ['resource.db', 'fin_data.db']:
                if Path(db_file).exists():
                    conn = sqlite3.connect(db_file, timeout=5)
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    conn.close()
            
            self._log("数据库验证", "数据库连接正常")
            return True
        except Exception as e:
            self._log("数据库验证失败", f"数据库连接失败: {e}")
            return False
    
    def _verify_metadata_tables(self):
        """验证元数据表"""
        try:
            conn = sqlite3.connect("resource.db")
            cursor = conn.cursor()
            
            required_tables = ['table_descriptions', 'column_descriptions', 'business_rules']
            for table in required_tables:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if not cursor.fetchone():
                    conn.close()
                    self._log("元数据验证失败", f"缺少表: {table}")
                    return False
            
            conn.close()
            self._log("元数据验证", "元数据表完整")
            return True
            
        except Exception as e:
            self._log("元数据验证失败", f"验证失败: {e}")
            return False
    
    def _log(self, action: str, message: str):
        """记录修复日志"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = {
            'timestamp': timestamp,
            'action': action,
            'message': message
        }
        self.repair_log.append(log_entry)
        print(f"  [{timestamp}] {action}: {message}")
    
    def _generate_repair_report(self):
        """生成修复报告"""
        print("\n" + "=" * 60)
        print("📋 修复报告")
        print("=" * 60)
        
        report = {
            'repair_time': datetime.now().isoformat(),
            'backup_directory': str(self.backup_dir),
            'repair_log': self.repair_log,
            'summary': {
                'total_actions': len(self.repair_log),
                'backup_created': self.backup_dir.exists()
            }
        }
        
        # 保存报告
        report_file = f"repair_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 修复报告已保存到: {report_file}")
        print(f"💾 备份文件位于: {self.backup_dir}")
        
        # 显示修复摘要
        print(f"\n📊 修复摘要:")
        print(f"  - 执行操作: {len(self.repair_log)} 个")
        print(f"  - 备份目录: {self.backup_dir}")
        print(f"  - 修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    print("🔧 智能数据分析系统修复工具")
    print(f"📅 修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    repair = SystemRepair()
    repair.run_auto_repair()

if __name__ == "__main__":
    main()
