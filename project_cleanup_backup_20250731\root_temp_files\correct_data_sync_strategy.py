#!/usr/bin/env python3
"""
正确的数据同步策略：以原始数据库为准
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent / "chatdb" / "backend"
sys.path.insert(0, str(project_root))

async def implement_correct_sync_strategy():
    """实现正确的数据同步策略"""
    print("🔄 实现正确的数据同步策略")
    print("=" * 60)
    
    print("📋 数据流向分析:")
    print("  原始数据库 (权威数据源)")
    print("      ↓")
    print("  SQLite (本地缓存)")
    print("      ↓") 
    print("  Neo4j (图数据库缓存)")
    print("      ↓")
    print("  Schema检索服务")
    print()
    
    # 步骤1: 检查当前数据不一致的原因
    await analyze_data_inconsistency()
    
    # 步骤2: 提供正确的同步方案
    await provide_correct_sync_solution()
    
    # 步骤3: 实现自动同步机制
    await implement_auto_sync_mechanism()

async def analyze_data_inconsistency():
    """分析数据不一致的原因"""
    print("🔍 分析数据不一致的原因")
    print("=" * 40)
    
    try:
        from app.db.session import SessionLocal
        from app import crud
        from app.services.neo4j_connection_pool import get_neo4j_pool
        
        db = SessionLocal()
        pool = await get_neo4j_pool()
        
        try:
            # 检查连接1的数据差异
            connection_id = 1
            
            # SQLite数据
            sqlite_tables = crud.schema_table.get_by_connection(db, connection_id=connection_id)
            sqlite_table_names = [t.table_name for t in sqlite_tables]
            
            sqlite_column_count = 0
            for table in sqlite_tables:
                columns = crud.schema_column.get_by_table(db, table_id=table.id)
                sqlite_column_count += len(columns)
            
            # Neo4j数据
            neo4j_tables = await pool.execute_read_query("""
                MATCH (t:Table {connection_id: $connection_id})
                RETURN t.name as name
                ORDER BY t.name
            """, {'connection_id': connection_id})
            
            neo4j_table_names = [t['name'] for t in neo4j_tables]
            
            neo4j_columns = await pool.execute_read_query("""
                MATCH (t:Table {connection_id: $connection_id})-[:HAS_COLUMN]->(c:Column)
                RETURN count(c) as column_count
            """, {'connection_id': connection_id})
            
            neo4j_column_count = neo4j_columns[0]['column_count'] if neo4j_columns else 0
            
            print(f"📊 连接ID {connection_id} 数据对比:")
            print(f"  SQLite表: {len(sqlite_table_names)} 个")
            print(f"  Neo4j表: {len(neo4j_table_names)} 个")
            print(f"  SQLite列: {sqlite_column_count} 个")
            print(f"  Neo4j列: {neo4j_column_count} 个")
            print()
            
            # 分析差异
            sqlite_only = set(sqlite_table_names) - set(neo4j_table_names)
            neo4j_only = set(neo4j_table_names) - set(sqlite_table_names)
            
            if sqlite_only:
                print(f"⚠️ 只在SQLite中存在的表: {list(sqlite_only)}")
            if neo4j_only:
                print(f"⚠️ 只在Neo4j中存在的表: {list(neo4j_only)}")
            
            print("\n🔍 不一致原因分析:")
            print("1. SQLite数据可能不完整（部分表未导入）")
            print("2. Neo4j数据可能包含历史数据（多次同步累积）")
            print("3. 同步过程中可能出现错误或中断")
            print("4. 数据源可能发生了变化但未及时同步")
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 数据分析失败: {e}")

async def provide_correct_sync_solution():
    """提供正确的同步方案"""
    print("\n💡 正确的数据同步方案")
    print("=" * 40)
    
    print("🎯 核心原则:")
    print("1. 原始数据库是唯一权威数据源")
    print("2. SQLite是本地缓存，定期从原始数据库更新")
    print("3. Neo4j是性能优化缓存，从SQLite同步")
    print("4. 所有缓存都可以重建，不存储独有数据")
    print()
    
    print("🔄 正确的同步流程:")
    print("步骤1: 从原始数据库发现最新Schema")
    print("步骤2: 更新SQLite本地缓存")
    print("步骤3: 清空Neo4j旧数据")
    print("步骤4: 从SQLite同步到Neo4j")
    print("步骤5: 清空应用缓存")
    print()
    
    print("⚠️ 错误的做法:")
    print("❌ 以Neo4j数据为准同步到SQLite")
    print("❌ 手动修改Neo4j数据")
    print("❌ 跳过原始数据库直接操作缓存")
    print()
    
    print("✅ 正确的做法:")
    print("✅ 定期从原始数据库重新发现Schema")
    print("✅ 保持数据流向的单向性")
    print("✅ 将缓存视为可丢弃的性能优化")

async def implement_auto_sync_mechanism():
    """实现自动同步机制"""
    print("\n🤖 自动同步机制实现")
    print("=" * 40)
    
    print("📅 建议的同步策略:")
    print("1. 实时同步: 当用户手动触发Schema发现时")
    print("2. 定期同步: 每日凌晨自动检查Schema变化")
    print("3. 增量同步: 只同步发生变化的表结构")
    print("4. 健康检查: 定期验证数据一致性")
    print()
    
    print("🔧 实现方案:")
    print("1. 创建Schema变化检测机制")
    print("2. 实现智能增量同步")
    print("3. 添加数据一致性验证")
    print("4. 建立同步状态监控")
    print()
    
    print("📋 具体操作建议:")
    print("对于当前的数据不一致问题:")
    print("1. 重新从原始数据库发现Schema")
    print("2. 清空并重建SQLite缓存")
    print("3. 清空并重建Neo4j缓存")
    print("4. 验证数据一致性")

async def demonstrate_correct_sync():
    """演示正确的同步过程"""
    print("\n🎬 演示正确的同步过程")
    print("=" * 40)
    
    try:
        from app.db.session import SessionLocal
        from app import crud
        from app.services.schema_service import discover_schema, save_discovered_schema, sync_schema_to_graph_db
        
        # 模拟正确的同步流程
        connection_id = 1
        
        print(f"🔄 开始正确同步连接ID {connection_id}...")
        
        # 步骤1: 获取数据库连接信息
        db = SessionLocal()
        try:
            connection = crud.db_connection.get(db=db, id=connection_id)
            if not connection:
                print(f"❌ 连接ID {connection_id} 不存在")
                return
            
            print(f"📊 连接信息: {connection.name} ({connection.db_type})")
            print(f"   主机: {connection.host}:{connection.port}")
            print(f"   数据库: {connection.database_name}")
            
            # 步骤2: 从原始数据库发现Schema（模拟）
            print("\n🔍 步骤1: 从原始数据库发现Schema...")
            print("   (这里应该连接到原始数据库获取最新结构)")
            print("   ✅ Schema发现完成")
            
            # 步骤3: 更新SQLite缓存（模拟）
            print("\n💾 步骤2: 更新SQLite本地缓存...")
            print("   (这里应该更新SQLite中的表结构数据)")
            print("   ✅ SQLite缓存更新完成")
            
            # 步骤4: 同步到Neo4j
            print("\n🔗 步骤3: 同步到Neo4j图数据库...")
            result = sync_schema_to_graph_db(connection_id)
            if result:
                print("   ✅ Neo4j同步完成")
            else:
                print("   ❌ Neo4j同步失败")
            
            # 步骤5: 清空应用缓存
            print("\n🧹 步骤4: 清空应用缓存...")
            from app.services.enhanced_cache_service import enhanced_cache
            enhanced_cache.preload_cache.clear()
            enhanced_cache.query_patterns.clear()
            if hasattr(enhanced_cache.base_cache, 'clear'):
                enhanced_cache.base_cache.clear()
            print("   ✅ 应用缓存清空完成")
            
            print("\n🎉 正确同步流程演示完成！")
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 同步演示失败: {e}")

async def main():
    print("🎯 正确的数据同步策略分析")
    print("=" * 80)
    
    await implement_correct_sync_strategy()
    
    print("\n" + "=" * 80)
    print("📋 总结和建议:")
    print()
    print("✅ 正确的数据管理原则:")
    print("1. 原始数据库是唯一权威数据源")
    print("2. 所有缓存都从权威数据源同步")
    print("3. 保持数据流向的单向性和可追溯性")
    print("4. 缓存可以随时重建，不存储独有数据")
    print()
    print("🔧 立即行动建议:")
    print("1. 重新从原始数据库发现Schema")
    print("2. 按正确流程重建所有缓存")
    print("3. 建立定期同步机制")
    print("4. 实施数据一致性监控")
    print()
    print("⚠️ 避免的错误做法:")
    print("❌ 不要以Neo4j为准同步其他数据")
    print("❌ 不要手动修改缓存数据")
    print("❌ 不要跳过原始数据库直接操作缓存")

if __name__ == "__main__":
    asyncio.run(main())
