"""
增强版AI提示构建服务
集成到智能数据分析系统的核心服务模块
"""

import sqlite3
from typing import Dict, Any, List
import json
import os
from app.core.config import settings

class EnhancedPromptService:
    """增强版AI提示构建服务"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or settings.BUSINESS_DB_PATH
    
    def build_enhanced_prompt(self, user_query: str, schema_context: Dict[str, Any]) -> str:
        """构建增强版AI提示"""
        
        # 获取增强元数据
        enhanced_metadata = self.get_enhanced_metadata()
        
        # 构建完整提示
        prompt_parts = [
            self.get_system_role_prompt(),
            self.get_financial_rules_prompt(),
            self.get_schema_context_prompt(schema_context, enhanced_metadata),
            self.get_query_patterns_prompt(user_query),
            self.get_error_prevention_prompt(),
            self.get_data_quality_prompt(),
            self.get_user_query_prompt(user_query)
        ]
        
        return "\n\n".join(filter(None, prompt_parts))
    
    def get_enhanced_metadata(self) -> Dict[str, Any]:
        """获取增强的元数据信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            metadata = {
                'column_descriptions': [],
                'business_rules': [],
                'field_relationships': [],
                'query_patterns': [],
                'ai_templates': []
            }
            
            # 获取增强的字段描述
            cursor.execute('''
                SELECT column_name, chinese_name, description, field_category,
                       usage_scenarios, common_values, related_fields, 
                       calculation_rules, ai_prompt_hints
                FROM column_descriptions 
                WHERE table_name = 'financial_data'
                ORDER BY column_name
            ''')
            
            for row in cursor.fetchall():
                metadata['column_descriptions'].append({
                    'column_name': row[0],
                    'chinese_name': row[1],
                    'description': row[2],
                    'field_category': row[3],
                    'usage_scenarios': row[4],
                    'common_values': row[5],
                    'related_fields': row[6],
                    'calculation_rules': row[7],
                    'ai_prompt_hints': row[8]
                })
            
            # 获取业务规则
            cursor.execute('''
                SELECT rule_category, rule_description, sql_example, importance_level
                FROM business_rules 
                WHERE table_name = 'financial_data'
                ORDER BY importance_level DESC
            ''')
            
            for row in cursor.fetchall():
                metadata['business_rules'].append({
                    'category': row[0],
                    'description': row[1],
                    'sql_example': row[2],
                    'importance': row[3]
                })
            
            # 获取字段关系
            cursor.execute('''
                SELECT primary_field, related_field, relationship_type, 
                       relationship_description, usage_example
                FROM field_relationships 
                WHERE table_name = 'financial_data'
            ''')
            
            for row in cursor.fetchall():
                metadata['field_relationships'].append({
                    'primary_field': row[0],
                    'related_field': row[1],
                    'relationship_type': row[2],
                    'description': row[3],
                    'usage_example': row[4]
                })
            
            # 获取查询模式
            cursor.execute('''
                SELECT pattern_name, pattern_description, natural_language_examples,
                       sql_template, required_fields, business_scenario
                FROM query_patterns
                ORDER BY difficulty_level
            ''')
            
            for row in cursor.fetchall():
                metadata['query_patterns'].append({
                    'name': row[0],
                    'description': row[1],
                    'examples': row[2],
                    'sql_template': row[3],
                    'required_fields': row[4],
                    'scenario': row[5]
                })
            
            conn.close()
            return metadata
            
        except Exception as e:
            print(f"获取增强元数据失败: {e}")
            return {}
    
    def get_system_role_prompt(self) -> str:
        """获取系统角色提示"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT template_content FROM ai_prompt_templates 
                WHERE template_name = 'system_role' AND is_active = TRUE
            ''')
            
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else ""
            
        except Exception as e:
            return "你是一名专业的财务数据分析师和SQL专家。"
    
    def get_financial_rules_prompt(self) -> str:
        """获取财务规则提示"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT template_content FROM ai_prompt_templates 
                WHERE template_name = 'financial_rules' AND is_active = TRUE
            ''')
            
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else ""
            
        except Exception as e:
            return ""
    
    def get_schema_context_prompt(self, schema_context: Dict[str, Any], enhanced_metadata: Dict[str, Any]) -> str:
        """构建增强的Schema上下文提示"""
        
        prompt = "📊 **数据库结构信息**：\n\n"
        
        # 表信息
        for table in schema_context.get('tables', []):
            table_name = table.get('name', '')
            prompt += f"### 📋 表名: {table_name}\n"
            
            # 字段信息（增强版）
            prompt += "**字段详情**：\n"
            
            # 按类别组织字段
            field_categories = {}
            for col in table.get('columns', []):
                col_name = col.get('column_name', '')
                
                # 查找增强元数据
                enhanced_col = None
                for meta_col in enhanced_metadata.get('column_descriptions', []):
                    if meta_col['column_name'] == col_name:
                        enhanced_col = meta_col
                        break
                
                if enhanced_col:
                    category = enhanced_col.get('field_category', '其他')
                    if category not in field_categories:
                        field_categories[category] = []
                    field_categories[category].append((col, enhanced_col))
            
            # 按类别显示字段
            for category, fields in field_categories.items():
                if category and fields:
                    prompt += f"\n**{category}字段**：\n"
                    for col, enhanced_col in fields:
                        col_name = col.get('column_name', '')
                        data_type = col.get('data_type', '')
                        chinese_name = enhanced_col.get('chinese_name', '')
                        ai_hints = enhanced_col.get('ai_prompt_hints', '')
                        
                        prompt += f"- **{col_name}** ({data_type}) - {chinese_name}\n"
                        if ai_hints:
                            prompt += f"  💡 {ai_hints}\n"
        
        return prompt
    
    def get_query_patterns_prompt(self, user_query: str) -> str:
        """根据用户查询获取相关的查询模式"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 简单的关键词匹配（可以后续优化为语义匹配）
            keywords = ['收入', '费用', '资产', '负债', '分析', '对比']
            matched_patterns = []
            
            for keyword in keywords:
                if keyword in user_query:
                    cursor.execute('''
                        SELECT pattern_name, pattern_description, sql_template
                        FROM query_patterns 
                        WHERE natural_language_examples LIKE ?
                        LIMIT 2
                    ''', (f'%{keyword}%',))
                    
                    matched_patterns.extend(cursor.fetchall())
            
            conn.close()
            
            if matched_patterns:
                prompt = "🔍 **相关查询模式参考**：\n\n"
                for pattern in matched_patterns[:3]:  # 最多显示3个
                    prompt += f"**{pattern[0]}**: {pattern[1]}\n"
                    prompt += f"```sql\n{pattern[2]}\n```\n\n"
                return prompt
            
        except Exception as e:
            pass
        
        return ""
    
    def get_error_prevention_prompt(self) -> str:
        """获取错误预防提示"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT template_content FROM ai_prompt_templates 
                WHERE template_name = 'error_prevention' AND is_active = TRUE
            ''')
            
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else ""
            
        except Exception as e:
            return ""
    
    def get_data_quality_prompt(self) -> str:
        """获取数据质量检查提示"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT template_content FROM ai_prompt_templates 
                WHERE template_name = 'data_quality' AND is_active = TRUE
            ''')
            
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else ""
            
        except Exception as e:
            return ""
    
    def get_user_query_prompt(self, user_query: str) -> str:
        """构建用户查询提示"""
        return f"""🎯 **用户查询**：
"{user_query}"

📝 **请按以下步骤分析并生成SQL**：
1. **理解查询意图**：分析用户想要什么类型的财务信息
2. **确定科目范围**：根据查询内容确定相关的会计科目
3. **选择正确字段**：严格按照财务规则选择对应的金额字段
4. **构建查询逻辑**：设计合理的筛选、分组和聚合逻辑
5. **验证查询合理性**：确保SQL符合财务逻辑和业务规则

💡 **输出要求**：
- 提供清晰的SQL查询语句
- 说明字段选择的理由
- 解释查询逻辑和业务含义"""

# 使用示例
if __name__ == "__main__":
    service = EnhancedPromptService()
    
    # 模拟schema上下文
    schema_context = {
        'tables': [{
            'name': 'financial_data',
            'columns': [
                {'column_name': 'year', 'data_type': 'INTEGER'},
                {'column_name': 'month', 'data_type': 'INTEGER'},
                {'column_name': 'account_code', 'data_type': 'TEXT'},
                {'column_name': 'credit_amount', 'data_type': 'REAL'},
                {'column_name': 'debit_amount', 'data_type': 'REAL'},
                {'column_name': 'balance', 'data_type': 'TEXT'}
            ]
        }]
    }
    
    # 生成增强提示
    user_query = "查询2024年9月的收入情况"
    enhanced_prompt = service.build_enhanced_prompt(user_query, schema_context)
    
    print("增强版AI提示预览：")
    print("=" * 80)
    print(enhanced_prompt[:1000] + "..." if len(enhanced_prompt) > 1000 else enhanced_prompt)
