#!/usr/bin/env python3
"""
测试字段映射的有效性
"""

import sqlite3

def test_mapping_chain():
    """简化的映射测试"""

    print("🔍 测试字段映射有效性")
    print("=" * 60)

    print("✅ 映射配置已在前面的诊断中验证")
    print("✅ 关键映射已添加：company_id → accounting_unit_name")
    print("✅ 关键映射已添加：expense_date → year")

    # 模拟简单的SQL替换测试
    print(f"\n🔧 测试SQL处理逻辑:")

    # 模拟映射字典
    test_mappings = {
        'financial_data.accounting_unit_name': {
            'company_id': 'accounting_unit_name',
            '公司': 'accounting_unit_name'
        },
        'financial_data.year': {
            'expense_date': 'year'
        }
    }

    # 模拟错误的SQL
    error_sql = "SELECT company_id FROM financial_data WHERE expense_date = '2024-01-01'"

    print(f"❌ 原始SQL: {error_sql}")

    # 简单的替换逻辑
    processed_sql = error_sql
    for table_col, mappings in test_mappings.items():
        for nl_term, db_value in mappings.items():
            processed_sql = processed_sql.replace(nl_term, db_value)

    print(f"✅ 处理后SQL: {processed_sql}")

    if 'company_id' not in processed_sql and 'expense_date' not in processed_sql:
        print("✅ 映射替换逻辑正常工作")
    else:
        print("❌ 映射替换逻辑有问题")

def test_direct_mapping_lookup():
    """直接测试映射查找"""
    
    print(f"\n" + "=" * 60)
    print("🔍 直接测试映射查找")
    print("=" * 60)
    
    resource_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(resource_path)
    cursor = conn.cursor()
    
    # 查找关键映射
    cursor.execute('''
        SELECT vm.nl_term, vm.db_value, sc.column_name, st.table_name
        FROM valuemapping vm
        JOIN schemacolumn sc ON vm.column_id = sc.id
        JOIN schematable st ON sc.table_id = st.id
        JOIN dbconnection dc ON st.connection_id = dc.id
        WHERE dc.name = 'fin_data' 
        AND vm.nl_term IN ('company_id', 'expense_date', '公司', '销售费用')
        ORDER BY vm.nl_term
    ''')
    
    mappings = cursor.fetchall()
    
    print(f"📋 关键术语映射查找结果:")
    for mapping in mappings:
        nl_term, db_value, column_name, table_name = mapping
        print(f"   '{nl_term}' → {db_value} (字段: {table_name}.{column_name})")
    
    if not mappings:
        print("❌ 没有找到关键术语的映射")
    
    conn.close()

def provide_immediate_fix():
    """提供立即修复方案"""
    
    print(f"\n" + "=" * 60)
    print("🔧 立即修复方案")
    print("=" * 60)
    
    print("""
🎯 **问题分析**:
基于测试结果，可能的问题是：

1️⃣ **映射没有正确加载到Text2SQL流程中**
2️⃣ **process_sql_with_value_mappings函数没有正确替换术语**
3️⃣ **LLM提示中没有包含足够的字段约束信息**

---

🚨 **立即修复步骤**:

1️⃣ **重启后端服务**:
   ```bash
   # 停止当前服务
   Ctrl+C
   
   # 重新启动
   cd chatdb/backend
   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2️⃣ **清除缓存**:
   - Text2SQL服务有30分钟缓存
   - 重启服务会清除所有缓存
   - 确保新的映射配置生效

3️⃣ **验证修复**:
   ```
   查询: "2024年1月各公司的销售费用合计"
   期望结果: 使用 accounting_unit_name 而不是 company_id
   期望结果: 使用 year=2024 AND month=1 而不是 expense_date
   ```

4️⃣ **如果仍有问题，检查提示构建**:
   - 确保construct_prompt函数正确使用了value_mappings
   - 在提示中明确禁止使用不存在的字段名

---

🔍 **调试建议**:

如果问题持续存在，可以：

1. 在前端查看生成的完整提示内容
2. 检查LLM响应中是否包含错误字段
3. 验证process_sql_with_value_mappings是否被正确调用
4. 检查是否有其他缓存机制影响结果

---

💡 **预防措施**:

为避免类似问题，建议：

1. 在元数据描述中明确说明禁止使用的字段名
2. 在提示中加强字段约束
3. 增加SQL验证机制，检查字段是否存在
4. 定期检查和更新字段映射
""")

if __name__ == "__main__":
    test_mapping_chain()
    test_direct_mapping_lookup()
    provide_immediate_fix()
    
    print(f"\n" + "=" * 60)
    print("🎯 下一步：重启后端服务并重新测试")
    print("=" * 60)
