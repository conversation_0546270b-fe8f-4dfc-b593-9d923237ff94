#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def enhance_ai_prompts():
    """增强AI提示模板，提高模型理解能力"""
    
    db_path = 'fin_data.db'
    if not os.path.exists(db_path):
        print(f'数据库文件不存在: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🤖 开始增强AI提示模板...")
        
        # AI提示模板
        ai_prompt_templates = [
            # 系统角色定义模板
            ('system_role', 'SYSTEM', '''你是一名资深的财务数据分析专家和SQL开发专家，具有以下专业能力：

🎯 **核心专长**：
1. **财务专业知识**：深度理解会计准则、财务报表结构、科目分类体系
2. **SQL技术能力**：精通复杂查询、数据聚合、多表关联、性能优化
3. **业务理解能力**：准确理解用户的财务分析需求和业务场景
4. **数据质量意识**：关注数据准确性、完整性和一致性

🏗️ **工作原则**：
- 严格遵循财务业务规则，确保查询结果的准确性
- 优先考虑数据的业务含义，而非单纯的技术实现
- 提供清晰的查询逻辑说明和结果解释
- 注重查询性能和可维护性''', '系统角色定义', 10),

            # 财务规则强化模板
            ('financial_rules', 'RULES', '''📊 **财务数据查询核心规则** (必须严格遵守)：

🔴 **科目分类与字段对应规则**：
┌─────────────────┬──────────────────┬─────────────────┐
│   科目类型      │    科目编号范围   │   使用字段      │
├─────────────────┼──────────────────┼─────────────────┤
│ 资产类科目      │ 1001-1999       │ balance         │
│ 负债类科目      │ 2001-2999       │ balance         │
│ 所有者权益类    │ 3001-3999       │ balance         │
│ 收入类科目      │ 6001-6199       │ credit_amount   │
│ 费用类科目      │ 6401-6699       │ debit_amount    │
└─────────────────┴──────────────────┴─────────────────┘

⚠️ **关键注意事项**：
- balance字段为TEXT类型，必须使用 CAST(balance AS REAL) 转换
- 错误的字段选择将导致完全错误的财务分析结果
- 根据科目编号自动判断科目类型，选择正确的金额字段''', '财务规则强化', 10),

            # 查询构建指导模板
            ('query_construction', 'GUIDANCE', '''🔧 **SQL查询构建指导**：

📝 **标准查询流程**：
1. **理解业务需求** → 识别查询目的（收入分析/费用分析/余额查询等）
2. **确定科目范围** → 根据科目编号确定查询范围
3. **选择正确字段** → 根据科目类型选择对应的金额字段
4. **添加筛选条件** → 时间、组织、项目等维度筛选
5. **设计聚合逻辑** → GROUP BY和聚合函数的使用
6. **优化查询性能** → 合理使用索引和筛选条件

💡 **常用查询模式**：
- 收入分析：WHERE account_code LIKE '60%' → SUM(credit_amount)
- 费用分析：WHERE account_code LIKE '64%' OR account_code LIKE '66%' → SUM(debit_amount)  
- 资产分析：WHERE account_code LIKE '1%' → SUM(CAST(balance AS REAL))
- 时间筛选：WHERE year = 2024 AND month = 9
- 组织筛选：WHERE accounting_unit_name LIKE '%关键词%' ''', '查询构建指导', 8),

            # 错误预防模板
            ('error_prevention', 'WARNING', '''🚨 **常见错误预防指南**：

❌ **严禁的错误操作**：
1. 收入查询使用balance字段 → 结果完全错误
2. 费用查询使用credit_amount字段 → 结果完全错误  
3. 资产查询使用debit_amount字段 → 结果完全错误
4. balance字段不进行类型转换 → 无法正确计算
5. 忽略科目编号规律 → 科目分类错误

✅ **正确的操作示例**：
```sql
-- ✅ 正确：收入查询
SELECT SUM(credit_amount) FROM financial_data WHERE account_code LIKE '60%';

-- ❌ 错误：收入查询  
SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_name LIKE '%收入%';

-- ✅ 正确：资产查询
SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_code LIKE '1%';

-- ❌ 错误：资产查询
SELECT SUM(debit_amount) FROM financial_data WHERE account_name LIKE '%资产%';
```''', '错误预防指南', 9),

            # 业务场景模板
            ('business_scenarios', 'CONTEXT', '''🏢 **常见业务分析场景**：

📈 **收入分析场景**：
- 月度收入统计、年度收入对比、收入结构分析
- 主营业务收入 vs 其他业务收入分析
- 各组织单位收入贡献度分析

💰 **费用分析场景**：
- 成本费用结构分析、费用控制效果评估
- 管理费用、销售费用、财务费用分类统计
- 费用率分析（费用/收入比例）

🏦 **资产负债分析场景**：
- 资产结构分析、负债水平评估
- 流动资产 vs 非流动资产分析
- 资产负债率、流动比率等财务指标计算

📊 **综合分析场景**：
- 利润表编制（收入-费用）
- 资产负债表编制（资产=负债+所有者权益）
- 现金流量分析、财务比率分析''', '业务场景指导', 7),

            # 数据质量检查模板
            ('data_quality', 'VALIDATION', '''🔍 **数据质量检查指导**：

📋 **查询前检查**：
1. 确认时间范围的合理性（年份、月份）
2. 验证组织单位名称的准确性
3. 检查科目编号的有效性

📊 **查询后验证**：
1. 检查结果数量的合理性
2. 验证金额数据的逻辑性（正负值）
3. 确认汇总数据的平衡性

⚖️ **借贷平衡验证**：
```sql
-- 验证借贷平衡
SELECT 
    SUM(debit_amount) as 借方总额,
    SUM(credit_amount) as 贷方总额,
    SUM(debit_amount) - SUM(credit_amount) as 差额
FROM financial_data 
WHERE year = 2024 AND month = 9;
```''', '数据质量检查', 6)
        ]
        
        # 插入AI提示模板
        for template in ai_prompt_templates:
            cursor.execute('''
                INSERT OR REPLACE INTO ai_prompt_templates 
                (template_name, template_type, template_content, usage_scenario, priority_level)
                VALUES (?, ?, ?, ?, ?)
            ''', template)
        
        print(f"✅ 已添加 {len(ai_prompt_templates)} 个AI提示模板")
        
        # 添加数据质量规则
        print("🔍 添加数据质量规则...")
        data_quality_rules = [
            ('financial_data', 'year', '范围检查', '年份应在合理范围内(2020-2030)', 
             'SELECT COUNT(*) FROM financial_data WHERE year < 2020 OR year > 2030', 
             '年份超出合理范围', 'WARNING'),
            
            ('financial_data', 'month', '范围检查', '月份应在1-12之间',
             'SELECT COUNT(*) FROM financial_data WHERE month < 1 OR month > 12',
             '月份超出有效范围', 'ERROR'),
            
            ('financial_data', 'account_code', '格式检查', '科目编号应为4位数字',
             'SELECT COUNT(*) FROM financial_data WHERE LENGTH(account_code) != 4',
             '科目编号格式不正确', 'WARNING'),
            
            ('financial_data', 'balance', '类型检查', 'balance字段应可转换为数值',
             'SELECT COUNT(*) FROM financial_data WHERE balance IS NOT NULL AND balance != "" AND CAST(balance AS REAL) IS NULL',
             'balance字段包含无法转换的数据', 'ERROR'),
            
            ('financial_data', 'debit_amount', '逻辑检查', '借方金额应为非负数',
             'SELECT COUNT(*) FROM financial_data WHERE debit_amount < 0',
             '借方金额出现负值', 'WARNING'),
            
            ('financial_data', 'credit_amount', '逻辑检查', '贷方金额应为非负数',
             'SELECT COUNT(*) FROM financial_data WHERE credit_amount < 0', 
             '贷方金额出现负值', 'WARNING')
        ]
        
        for rule in data_quality_rules:
            cursor.execute('''
                INSERT OR REPLACE INTO data_quality_rules 
                (table_name, field_name, rule_type, rule_description, validation_sql, error_message, severity_level)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', rule)
        
        print(f"✅ 已添加 {len(data_quality_rules)} 条数据质量规则")
        
        conn.commit()
        conn.close()
        
        print("🎉 AI提示模板增强完成！")
        
    except Exception as e:
        print(f"❌ 增强AI提示模板时出错: {e}")

if __name__ == "__main__":
    enhance_ai_prompts()
