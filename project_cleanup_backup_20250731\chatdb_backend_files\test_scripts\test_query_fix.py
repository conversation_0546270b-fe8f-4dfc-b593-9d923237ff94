#!/usr/bin/env python3
"""
测试查询修复效果的脚本
"""

import sqlite3

def test_field_mappings():
    """测试字段映射是否正确添加"""
    
    metadata_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(metadata_db)
    cursor = conn.cursor()
    
    print("=== 测试字段映射 ===")
    
    # 测试关键映射
    test_mappings = ['company_id', '公司', 'date', '日期', '管理费用', '收入']
    
    for term in test_mappings:
        cursor.execute('''
            SELECT vm.nl_term, vm.db_value, sc.column_name
            FROM valuemapping vm
            JOIN schemacolumn sc ON vm.column_id = sc.id
            WHERE vm.nl_term = ?
        ''', (term,))
        
        results = cursor.fetchall()
        if results:
            print(f"✅ '{term}' -> {results[0][2]}")
        else:
            print(f"❌ '{term}' 没有找到映射")
    
    conn.close()

def test_actual_table_structure():
    """测试实际表结构"""
    
    business_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/fin_data.db'
    conn = sqlite3.connect(business_db)
    cursor = conn.cursor()
    
    print("\n=== 实际表结构验证 ===")
    
    # 检查关键字段是否存在
    cursor.execute("PRAGMA table_info(financial_data)")
    columns = [col[1] for col in cursor.fetchall()]
    
    key_fields = ['accounting_unit_name', 'year', 'month', 'account_code', 'debit_amount', 'credit_amount']
    
    for field in key_fields:
        if field in columns:
            print(f"✅ {field} 字段存在")
        else:
            print(f"❌ {field} 字段不存在")
    
    # 测试一个简单的查询
    print("\n=== 测试查询 ===")
    try:
        cursor.execute('''
            SELECT accounting_unit_name, 
                   SUM(CAST(debit_amount AS REAL)) as total_amount
            FROM financial_data 
            WHERE year = 2024 AND month = 5
              AND account_code LIKE '64%'
            GROUP BY accounting_unit_name
            LIMIT 3
        ''')
        
        results = cursor.fetchall()
        print("✅ 查询成功执行")
        for row in results:
            print(f"  {row[0]}: {row[1]}")
            
    except Exception as e:
        print(f"❌ 查询失败: {e}")
    
    conn.close()

def generate_correct_sql():
    """生成正确的SQL示例"""
    
    print("\n=== 正确的SQL示例 ===")
    
    correct_sql = '''
-- 查询2024年5月各公司的管理费用合计（正确版本）
SELECT 
    accounting_unit_name as company_name,
    SUM(CAST(debit_amount AS REAL)) AS total_management_expenses
FROM 
    financial_data
WHERE 
    year = 2024 AND month = 5
    AND account_code LIKE '64%'  -- 管理费用科目代码
GROUP BY 
    accounting_unit_name
ORDER BY 
    total_management_expenses DESC;
'''
    
    print(correct_sql)

if __name__ == "__main__":
    test_field_mappings()
    test_actual_table_structure()
    generate_correct_sql()
