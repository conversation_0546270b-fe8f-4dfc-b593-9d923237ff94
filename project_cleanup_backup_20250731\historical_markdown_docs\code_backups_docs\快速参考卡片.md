# 智能数据分析系统 - 快速参考卡片

## 🚀 快速启动

### Docker 一键启动
```bash
cd chatdb
docker-compose up -d
```

### 访问地址
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000/docs
- Neo4j浏览器: http://localhost:7474

## 🎯 核心功能快速操作

### 1. 智能查询 (Text2SQL)
```
操作路径: 主页 → 智能查询
快速示例:
- "查询2024年9月的收入情况"
- "分析管理费用的构成"
- "统计各部门的资产总额"
- "比较今年和去年的利润"
```

### 2. 连接管理
```
操作路径: 顶部菜单 → 连接管理
快速操作:
1. 点击"添加连接"
2. 选择数据库类型 (SQLite/MySQL/PostgreSQL)
3. 填写连接信息
4. 点击"测试连接"
5. 保存连接
```

### 3. 数据建模
```
操作路径: 顶部菜单 → 数据建模
快速操作:
1. 选择数据库连接
2. 点击"发现结构"
3. 拖拽表到画布
4. 定义表关系
5. 点击"发布模式"
```

### 4. 数据映射
```
操作路径: 顶部菜单 → 数据映射
快速操作:
1. 选择连接、表、字段
2. 点击"添加映射"
3. 输入自然语言术语和数据库值
4. 保存映射
```

## 📊 常用查询模板

### 收入分析
```sql
-- 月度收入趋势
"查询2024年各月的主营业务收入趋势"

-- 部门收入对比
"按部门统计2024年第三季度的收入情况"

-- 收入构成分析
"分析2024年9月各类收入的构成比例"
```

### 费用分析
```sql
-- 费用构成
"分析2024年管理费用的详细构成"

-- 费用趋势
"查看最近12个月的费用变化趋势"

-- 部门费用对比
"比较各部门的费用支出情况"
```

### 资产分析
```sql
-- 资产结构
"显示流动资产和非流动资产的分布"

-- 资产变化
"分析资产总额的月度变化情况"

-- 资产周转
"计算存货周转率和应收账款周转率"
```

## 🔧 常用配置

### 环境变量 (.env)
```bash
# 数据库配置
DATABASE_TYPE=sqlite
SQLITE_DB_PATH=resource.db
BUSINESS_DB_PATH=fin_data.db

# LLM配置
OPENAI_API_KEY=your_api_key
OPENAI_API_BASE=https://api.deepseek.com/v1
LLM_MODEL=deepseek-chat

# Neo4j配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# 功能开关
ENABLE_CACHE=true
ENABLE_HYBRID_RETRIEVAL=true
```

### 连接配置模板

#### SQLite
```
连接名称: 本地数据库
数据库类型: SQLite
数据库路径: /path/to/database.db
```

#### MySQL
```
连接名称: MySQL数据库
数据库类型: MySQL
主机地址: localhost
端口: 3306
数据库名: finance_db
用户名: root
密码: password
```

#### PostgreSQL
```
连接名称: PostgreSQL数据库
数据库类型: PostgreSQL
主机地址: localhost
端口: 5432
数据库名: finance_db
用户名: postgres
密码: password
```

## 🚨 故障排除速查

### 启动问题
```bash
# 端口被占用
netstat -tlnp | grep :3000
sudo kill -9 <PID>

# 数据库连接失败
ls -la *.db
chmod 644 *.db

# Neo4j连接超时
docker restart chatdb-neo4j
docker logs chatdb-neo4j
```

### 查询问题
```bash
# 查询结果不准确
1. 检查数据映射是否正确
2. 优化查询描述
3. 验证元数据加载

# 查询响应慢
1. 添加数据库索引
2. 启用查询缓存
3. 优化查询条件
```

### 性能问题
```bash
# 系统响应慢
top -p $(pgrep -f uvicorn)
iostat -x 1

# 内存使用高
import gc; gc.collect()
```

## 📋 API 快速参考

### 智能查询
```bash
curl -X POST "http://localhost:8000/api/text2sql-sse/stream-query" \
  -H "Content-Type: application/json" \
  -d '{
    "connection_id": 1,
    "query": "查询收入情况",
    "use_hybrid_retrieval": true
  }'
```

### 连接管理
```bash
# 获取连接列表
curl "http://localhost:8000/api/connections/"

# 创建连接
curl -X POST "http://localhost:8000/api/connections/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试连接",
    "db_type": "sqlite",
    "database_name": "test.db"
  }'
```

### 数据建模
```bash
# 发现结构
curl -X POST "http://localhost:8000/api/schema/1/discover"

# 获取图数据
curl "http://localhost:8000/api/graph-visualization/1"
```

## 💡 使用技巧

### 查询优化
- ✅ 明确时间范围: "2024年9月"
- ✅ 指定数据类型: "主营业务收入"
- ✅ 包含分组维度: "按部门"
- ❌ 避免模糊描述: "查询数据"

### 数据映射
- 建立标准财务术语映射
- 定期验证映射有效性
- 使用智能建议功能
- 批量导入常用映射

### 性能优化
- 启用查询缓存
- 添加数据库索引
- 使用连接池
- 限制结果集大小

## 🔒 安全检查清单

### 基础安全
- [ ] 修改默认密码
- [ ] 启用HTTPS
- [ ] 配置防火墙
- [ ] 定期备份数据

### 访问控制
- [ ] 配置用户角色
- [ ] 设置权限控制
- [ ] 启用访问审计
- [ ] 监控异常访问

### 数据安全
- [ ] 加密敏感数据
- [ ] 配置数据脱敏
- [ ] 防止SQL注入
- [ ] 定期安全扫描

## 📞 紧急联系

### 系统状态检查
```bash
# 健康检查
curl http://localhost:8000/health
curl http://localhost:3000

# 服务状态
docker ps
systemctl status mysql
```

### 日志查看
```bash
# 应用日志
tail -f logs/app.log

# Docker日志
docker logs chatdb-backend
docker logs chatdb-frontend
docker logs chatdb-neo4j
```

### 数据备份
```bash
# SQLite备份
cp *.db backup/

# MySQL备份
mysqldump -u root -p finance_db > backup.sql

# Neo4j备份
docker exec chatdb-neo4j neo4j-admin dump --database=neo4j --to=/backups/
```

---

**版本**: v1.0  
**更新**: 2024-01  
**支持**: 详见完整用户手册
