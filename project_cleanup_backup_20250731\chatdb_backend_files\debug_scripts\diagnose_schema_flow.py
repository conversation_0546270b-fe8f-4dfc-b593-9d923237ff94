#!/usr/bin/env python3
"""
诊断schema数据流的完整脚本
检查从Neo4j到LLM的整个数据传递过程
"""

import sqlite3
from neo4j import GraphDatabase
import json

def diagnose_complete_flow():
    """诊断完整的数据流"""
    
    print("🔍 开始诊断schema数据流...")
    print("=" * 60)
    
    # 1. 检查Neo4j中的数据
    print("1️⃣ 检查Neo4j中的表和字段数据")
    check_neo4j_data()
    
    # 2. 检查元数据库中的schema信息
    print("\n2️⃣ 检查元数据库中的schema信息")
    check_metadata_schema()
    
    # 3. 检查字段映射
    print("\n3️⃣ 检查字段映射关系")
    check_field_mappings()
    
    # 4. 模拟schema_context构建过程
    print("\n4️⃣ 模拟schema_context构建过程")
    simulate_schema_context_building()

def check_neo4j_data():
    """检查Neo4j中的数据"""
    
    NEO4J_URI = "neo4j://localhost:7687"
    NEO4J_USER = "neo4j"
    NEO4J_PASSWORD = "Di@nhua11"
    
    try:
        driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
        
        with driver.session() as session:
            # 检查表
            result = session.run("MATCH (t:Table) RETURN t.name, t.description")
            tables = list(result)
            print(f"   📊 Neo4j中的表: {[t['t.name'] for t in tables]}")
            
            # 检查字段
            result = session.run("""
                MATCH (t:Table)-[:HAS_COLUMN]->(c:Column)
                WHERE t.name = 'financial_data'
                RETURN c.name, c.type
                ORDER BY c.name
                LIMIT 10
            """)
            columns = list(result)
            print(f"   📋 financial_data表的字段（前10个）:")
            for col in columns:
                print(f"      - {col['c.name']} ({col['c.type']})")
        
        driver.close()
        
    except Exception as e:
        print(f"   ❌ Neo4j检查失败: {e}")

def check_metadata_schema():
    """检查元数据库中的schema信息"""
    
    metadata_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    
    try:
        conn = sqlite3.connect(metadata_db)
        cursor = conn.cursor()
        
        # 检查schematable
        cursor.execute("SELECT table_name FROM schematable WHERE connection_id = 1")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"   📊 元数据库中的表: {tables}")
        
        # 检查schemacolumn
        cursor.execute("""
            SELECT sc.column_name, sc.data_type 
            FROM schemacolumn sc 
            JOIN schematable st ON sc.table_id = st.id 
            WHERE st.table_name = 'financial_data' AND st.connection_id = 1
            ORDER BY sc.column_name
            LIMIT 10
        """)
        columns = cursor.fetchall()
        print(f"   📋 financial_data表的字段（前10个）:")
        for col in columns:
            print(f"      - {col[0]} ({col[1]})")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ 元数据库检查失败: {e}")

def check_field_mappings():
    """检查字段映射关系"""
    
    metadata_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    
    try:
        conn = sqlite3.connect(metadata_db)
        cursor = conn.cursor()
        
        # 检查关键映射
        key_terms = ['company_name', 'company_id', '公司', 'date', '日期', 'sales_expense', '销售费用']
        
        for term in key_terms:
            cursor.execute("""
                SELECT vm.nl_term, sc.column_name, st.table_name
                FROM valuemapping vm
                JOIN schemacolumn sc ON vm.column_id = sc.id
                JOIN schematable st ON sc.table_id = st.id
                WHERE vm.nl_term = ?
            """, (term,))
            
            results = cursor.fetchall()
            if results:
                for result in results:
                    print(f"   ✅ '{result[0]}' → {result[2]}.{result[1]}")
            else:
                print(f"   ❌ '{term}' 没有找到映射")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ 字段映射检查失败: {e}")

def simulate_schema_context_building():
    """模拟schema_context构建过程"""
    
    print("   🔄 模拟从Neo4j构建schema_context...")
    
    NEO4J_URI = "neo4j://localhost:7687"
    NEO4J_USER = "neo4j"
    NEO4J_PASSWORD = "Di@nhua11"
    
    try:
        driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
        
        with driver.session() as session:
            # 模拟retrieve_relevant_schema的查询
            result = session.run("""
                MATCH (t:Table {connection_id: 1})-[:HAS_COLUMN]->(c:Column)
                RETURN {
                    tables: collect(DISTINCT {
                        id: t.id,
                        name: t.name,
                        description: t.description
                    }),
                    columns: collect(DISTINCT {
                        id: c.id,
                        column_name: c.name,
                        data_type: c.type,
                        table_name: t.name,
                        description: c.description
                    })
                } AS schema_data
            """)
            
            schema_data = list(result)[0]['schema_data']
            
            print(f"   📊 构建的schema_context:")
            print(f"      表数量: {len(schema_data['tables'])}")
            print(f"      字段数量: {len(schema_data['columns'])}")
            
            # 显示字段详情
            print(f"   📋 字段详情（前10个）:")
            for i, col in enumerate(schema_data['columns'][:10]):
                print(f"      {i+1}. {col['column_name']} ({col['data_type']}) - 表: {col['table_name']}")
            
            # 检查关键字段是否存在
            column_names = [col['column_name'] for col in schema_data['columns']]
            key_fields = ['accounting_unit_name', 'year', 'month', 'account_code', 'debit_amount']
            
            print(f"   🔍 关键字段检查:")
            for field in key_fields:
                if field in column_names:
                    print(f"      ✅ {field} 存在")
                else:
                    print(f"      ❌ {field} 不存在")
            
            # 检查是否有错误的字段
            wrong_fields = ['company_name', 'company_id', 'date', 'sales_expense']
            print(f"   ⚠️ 错误字段检查:")
            for field in wrong_fields:
                if field in column_names:
                    print(f"      ❌ {field} 错误存在（应该不存在）")
                else:
                    print(f"      ✅ {field} 正确不存在")
        
        driver.close()
        
    except Exception as e:
        print(f"   ❌ schema_context构建模拟失败: {e}")

if __name__ == "__main__":
    diagnose_complete_flow()
    print("\n" + "=" * 60)
    print("🎯 诊断完成！")
