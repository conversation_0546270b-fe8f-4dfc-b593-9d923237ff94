#!/usr/bin/env python3
"""
分析项目中应该关联哪些数据库
"""

import sqlite3
import os

def analyze_existing_connections():
    """分析现有的数据库连接"""
    
    print("🔍 项目数据库连接分析和建议")
    print("=" * 70)
    
    # 分析resource.db中的连接配置
    resource_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(resource_path)
    cursor = conn.cursor()
    
    cursor.execute('SELECT * FROM dbconnection')
    connections = cursor.fetchall()
    
    print(f"📊 当前已配置的数据库连接 ({len(connections)}个):")
    
    for conn_data in connections:
        conn_id, name, db_type, host, port, username, password, db_name, created_at, updated_at = conn_data
        print(f"\n🔗 连接 {conn_id}: {name}")
        print(f"   类型: {db_type}")
        print(f"   路径: {db_name}")
        
        # 检查文件是否存在
        if os.path.exists(db_name):
            print(f"   状态: ✅ 文件存在")
            
            # 获取该连接下的表
            cursor.execute('SELECT table_name FROM schematable WHERE connection_id = ?', (conn_id,))
            tables = cursor.fetchall()
            if tables:
                print(f"   包含表 ({len(tables)}个): {[t[0] for t in tables[:5]]}{'...' if len(tables) > 5 else ''}")
            else:
                print(f"   包含表: 无")
        else:
            print(f"   状态: ❌ 文件不存在")
    
    conn.close()

def analyze_fin_data_database():
    """分析fin_data数据库的内容"""
    
    print(f"\n" + "=" * 70)
    print("📊 fin_data.db 数据库详细分析")
    print("=" * 70)
    
    fin_data_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/fin_data.db'
    
    if not os.path.exists(fin_data_path):
        print("❌ fin_data.db 文件不存在")
        return
    
    conn = sqlite3.connect(fin_data_path)
    cursor = conn.cursor()
    
    # 获取所有表
    cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
    tables = cursor.fetchall()
    
    print(f"📋 包含表 ({len(tables)}个):")
    
    for table in tables:
        table_name = table[0]
        
        # 获取记录数
        try:
            cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
            count = cursor.fetchone()[0]
        except:
            count = "无法获取"
        
        print(f"\n📊 {table_name}")
        print(f"   记录数: {count}")
        
        # 获取表结构
        try:
            cursor.execute(f'PRAGMA table_info("{table_name}")')
            columns = cursor.fetchall()
            
            if columns:
                print(f"   关键字段:")
                for col in columns[:5]:  # 只显示前5个字段
                    col_name = col[1]
                    col_type = col[2]
                    is_pk = '🔑' if col[5] else '  '
                    print(f"      {is_pk} {col_name} ({col_type})")
                if len(columns) > 5:
                    print(f"      ... 还有 {len(columns) - 5} 个字段")
        except Exception as e:
            print(f"   字段信息: 无法获取 ({e})")
    
    conn.close()

def analyze_resource_database():
    """分析resource数据库的内容"""
    
    print(f"\n" + "=" * 70)
    print("📊 resource.db 数据库详细分析")
    print("=" * 70)
    
    resource_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(resource_path)
    cursor = conn.cursor()
    
    # 重点分析几个核心表
    core_tables = ['chatsession', 'chatmessage', 'valuemapping', 'meta_column_descriptions']
    
    print("📋 核心业务表:")
    
    for table_name in core_tables:
        try:
            # 获取记录数
            cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
            count = cursor.fetchone()[0]
            
            print(f"\n📊 {table_name}")
            print(f"   记录数: {count}")
            
            # 获取表结构
            cursor.execute(f'PRAGMA table_info({table_name})')
            columns = cursor.fetchall()
            
            print(f"   关键字段:")
            for col in columns[:5]:  # 只显示前5个字段
                col_name = col[1]
                col_type = col[2]
                is_pk = '🔑' if col[5] else '  '
                print(f"      {is_pk} {col_name} ({col_type})")
            if len(columns) > 5:
                print(f"      ... 还有 {len(columns) - 5} 个字段")
                
        except Exception as e:
            print(f"   {table_name}: 无法分析 ({e})")
    
    conn.close()

def provide_connection_recommendations():
    """提供数据库关联建议"""
    
    print(f"\n" + "=" * 70)
    print("💡 数据库关联建议")
    print("=" * 70)
    
    print("""
🎯 **当前状况分析**:

1️⃣ **fin_data.db** (业务数据库):
   - 主要用途: 存储财务业务数据
   - 核心表: financial_data (财务数据主表)
   - 数据特点: 大量的财务交易记录
   - 用户查询: 主要针对这个数据库进行分析

2️⃣ **resource.db** (系统配置数据库):
   - 主要用途: 存储系统配置和元数据
   - 核心表: 聊天记录、字段映射、元数据描述
   - 数据特点: 系统运行时的配置和日志数据
   - 用户查询: 很少直接查询这个数据库

---

🔗 **关联建议**:

✅ **推荐关联的数据库**:

1️⃣ **fin_data.db** (必须关联) ⭐⭐⭐
   理由:
   - 这是用户主要查询的业务数据库
   - 包含所有的财务分析数据
   - 用户的90%查询都针对这个数据库
   - 已经有完善的字段映射和元数据描述

2️⃣ **resource.db** (可选关联) ⭐
   理由:
   - 主要用于系统管理和监控
   - 可以查询聊天历史、系统使用情况
   - 对普通业务用户价值不大
   - 主要供系统管理员使用

❌ **不推荐关联的数据库**:
   - 暂时没有其他数据库需要关联

---

🎯 **具体操作建议**:

1️⃣ **优先处理 fin_data.db**:
   ```
   访问: http://172.18.208.1:3000/schema
   选择: "fin_data" 连接
   操作: 建立 financial_data 表内部的关系
   重点: 如果有多个相关表，建立它们之间的关系
   ```

2️⃣ **可选处理 resource.db**:
   ```
   访问: http://172.18.208.1:3000/schema  
   选择: "resource" 连接
   操作: 完善聊天系统表之间的关系
   用途: 系统监控和管理查询
   ```

---

🔍 **关系建模重点**:

对于 **fin_data.db**:
- 检查 financial_data 表是否需要拆分
- 如果有公司信息表、科目信息表等，建立关联关系
- 重点关注主键-外键关系

对于 **resource.db**:
- 已有的关系基本完善
- 主要用于系统内部查询
- 不需要额外的复杂关联

---

🎯 **最终建议**:

**立即行动**: 重点关联 fin_data.db，这是用户查询的核心数据库
**可选行动**: 关联 resource.db，主要用于系统管理
**优先级**: fin_data.db (高) > resource.db (低)
""")

if __name__ == "__main__":
    analyze_existing_connections()
    analyze_fin_data_database()
    analyze_resource_database()
    provide_connection_recommendations()
    
    print(f"\n" + "=" * 70)
    print("🎯 总结：优先关联 fin_data.db，这是业务查询的核心数据库")
    print("=" * 70)
