#!/usr/bin/env python3
"""
修复Neo4j数据同步问题的脚本
清理错误的元数据表，重新同步正确的业务表结构
"""

import asyncio
import sqlite3
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.neo4j_pool import neo4j_pool
from app.core.config import settings

async def clean_and_resync_neo4j():
    """清理Neo4j中的错误数据并重新同步"""
    
    # 1. 清理所有现有数据
    print("🧹 清理Neo4j中的现有数据...")
    await neo4j_pool.execute_write_query(
        "MATCH (n) DETACH DELETE n"
    )
    
    # 2. 定义需要排除的元数据表
    excluded_tables = {
        'column_descriptions', 'data_quality_rules', 'field_relationships',
        'query_patterns', 'table_descriptions', 'financial_data_columns_metadata', 
        'financial_data_with_metadata', 'ai_prompt_templates', 'business_rules',
        'meta_table_descriptions', 'meta_column_descriptions', 'meta_business_rules',
        'sqlite_sequence'
    }
    
    # 3. 从业务数据库获取真实的表结构
    business_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/fin_data.db'
    conn = sqlite3.connect(business_db)
    cursor = conn.cursor()
    
    # 获取所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    all_tables = [t[0] for t in cursor.fetchall()]
    
    # 过滤出业务表
    business_tables = [t for t in all_tables if t not in excluded_tables]
    print(f"📊 发现业务表: {business_tables}")
    
    # 4. 为每个业务表创建Neo4j节点
    for table_name in business_tables:
        print(f"🔄 同步表: {table_name}")
        
        # 创建Table节点
        await neo4j_pool.execute_write_query(
            """
            CREATE (t:Table {
                id: $id,
                name: $name,
                description: $description,
                connection_id: $connection_id
            })
            """,
            id=hash(table_name) % 1000000,  # 简单的ID生成
            name=table_name,
            description=f"业务数据表: {table_name}",
            connection_id=1
        )
        
        # 获取表的字段信息
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        for col in columns:
            col_name = col[1]
            col_type = col[2]
            is_pk = bool(col[5])
            
            # 创建Column节点
            await neo4j_pool.execute_write_query(
                """
                CREATE (c:Column {
                    id: $id,
                    name: $name,
                    type: $type,
                    description: $description,
                    is_pk: $is_pk,
                    is_fk: false,
                    connection_id: $connection_id
                })
                WITH c
                MATCH (t:Table {name: $table_name, connection_id: $connection_id})
                CREATE (t)-[:HAS_COLUMN]->(c)
                """,
                id=hash(f"{table_name}.{col_name}") % 1000000,
                name=col_name,
                type=col_type,
                description=f"{table_name}表的{col_name}字段",
                is_pk=is_pk,
                connection_id=1,
                table_name=table_name
            )
    
    conn.close()
    print("✅ Neo4j数据同步完成!")

if __name__ == "__main__":
    asyncio.run(clean_and_resync_neo4j())
