#!/usr/bin/env python3
"""
添加resource.db数据库连接配置
"""

import sqlite3
from datetime import datetime

def add_resource_db_connection():
    """添加resource.db数据库连接"""
    
    metadata_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(metadata_db)
    cursor = conn.cursor()
    
    try:
        # 检查是否已存在resource连接
        cursor.execute("SELECT id FROM dbconnection WHERE name = 'resource'")
        existing = cursor.fetchone()
        
        if existing:
            print("⚠️ resource数据库连接已存在")
            return existing[0]
        
        # 添加resource.db连接
        cursor.execute('''
            INSERT INTO dbconnection (name, db_type, host, port, username, password_encrypted, database_name, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            'resource',  # name
            'sqlite',    # db_type
            'localhost', # host
            0,           # port
            '',          # username
            '',          # password_encrypted
            r'C:\Users\<USER>\PycharmProjects\智能数据分析系统\resource.db',  # database_name
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # created_at
        ))
        
        connection_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ 成功添加resource数据库连接，ID: {connection_id}")
        return connection_id
        
    except Exception as e:
        print(f"❌ 添加连接失败: {e}")
        conn.rollback()
        return None
    finally:
        conn.close()

def sync_resource_schema(connection_id):
    """同步resource.db的schema信息"""
    
    metadata_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(metadata_db)
    cursor = conn.cursor()
    
    try:
        # 获取resource.db中的所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = cursor.fetchall()
        
        print(f"🔄 开始同步resource.db的schema信息...")
        
        for table_name_tuple in tables:
            table_name = table_name_tuple[0]
            
            # 添加表信息
            cursor.execute('''
                INSERT OR REPLACE INTO schematable (connection_id, table_name, description, created_at)
                VALUES (?, ?, ?, ?)
            ''', (
                connection_id,
                table_name,
                f"Resource database table: {table_name}",
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
            
            table_id = cursor.lastrowid
            
            # 获取表的字段信息
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            for col in columns:
                col_name = col[1]
                col_type = col[2]
                is_pk = bool(col[5])
                
                # 添加字段信息
                cursor.execute('''
                    INSERT OR REPLACE INTO schemacolumn (table_id, column_name, data_type, is_primary_key, created_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    table_id,
                    col_name,
                    col_type,
                    is_pk,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ))
            
            print(f"   ✅ 同步表: {table_name} ({len(columns)}个字段)")
        
        conn.commit()
        print(f"✅ schema同步完成，共同步 {len(tables)} 个表")
        
    except Exception as e:
        print(f"❌ schema同步失败: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 开始添加resource.db连接配置...")
    
    # 添加连接
    connection_id = add_resource_db_connection()
    
    if connection_id:
        # 同步schema
        sync_resource_schema(connection_id)
        
        print(f"\n✅ 完成！现在可以在前端选择 'resource' 数据库连接")
        print(f"   连接ID: {connection_id}")
        print(f"   数据库路径: C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\resource.db")
    else:
        print("❌ 添加连接失败")
