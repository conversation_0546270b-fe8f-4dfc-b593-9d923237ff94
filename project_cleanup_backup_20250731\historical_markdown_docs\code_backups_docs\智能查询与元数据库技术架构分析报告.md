# 智能查询与元数据库技术架构分析报告

## 📋 执行摘要

本报告详细分析了智能数据分析系统中智能查询功能与元数据库之间的技术架构和数据流转机制。通过深入代码分析，发现系统采用了多层架构设计，具备完善的连接池管理、缓存机制和元数据增强功能。

### 🎯 关键发现
- **双数据库架构**：系统使用resource.db作为元数据库，fin_data.db作为业务数据库
- **连接池优化**：实现了SQLAlchemy连接池，支持10个基础连接+20个溢出连接
- **多层缓存机制**：内存缓存、元数据缓存、查询结果缓存三层架构
- **元数据增强**：集成了表描述、字段描述、业务规则等增强元数据
- **配置升级**：从DeepSeek API升级到阿里云百炼API (qwen-max模型)

---

## 1. 元数据库连接机制分析

### 1.1 连接配置架构

#### 主要配置文件
```python
# chatdb/backend/app/core/config.py
class Settings(BaseSettings):
    # 数据库配置
    DATABASE_TYPE: str = os.getenv("DATABASE_TYPE", "sqlite")
    SQLITE_DB_PATH: str = os.getenv("SQLITE_DB_PATH", "resource.db")
    
    # 多数据库配置
    METADATA_DB_PATH: str = os.getenv("METADATA_DB_PATH", "resource.db")
    BUSINESS_DB_PATH: str = os.getenv("BUSINESS_DB_PATH", "fin_data.db")
    
    # 元数据增强配置
    ENABLE_METADATA_ENHANCEMENT: bool = os.getenv("ENABLE_METADATA_ENHANCEMENT", "true").lower() == "true"
    
    # 连接池配置
    DB_POOL_SIZE: int = int(os.getenv("DB_POOL_SIZE", "10"))
    DB_MAX_OVERFLOW: int = int(os.getenv("DB_MAX_OVERFLOW", "20"))
    DB_POOL_TIMEOUT: int = int(os.getenv("DB_POOL_TIMEOUT", "30"))
    DB_POOL_RECYCLE: int = int(os.getenv("DB_POOL_RECYCLE", "3600"))
```

#### 环境变量配置 (.env)
```bash
# 数据库配置
DATABASE_TYPE=sqlite
SQLITE_DB_PATH=resource.db
METADATA_DB_PATH=resource.db
BUSINESS_DB_PATH=fin_data.db

# 元数据增强配置
ENABLE_METADATA_ENHANCEMENT=true

# 数据库连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
```

### 1.2 连接池实现机制

#### SQLAlchemy引擎配置
```python
# chatdb/backend/app/db/session.py
def get_database_url():
    """根据配置返回相应的数据库连接URL"""
    if settings.DATABASE_TYPE.lower() == "sqlite":
        return f"sqlite:///{settings.SQLITE_DB_PATH}"
    elif settings.DATABASE_TYPE.lower() == "mysql":
        return (
            f"mysql+pymysql://{settings.MYSQL_USER}:{settings.MYSQL_PASSWORD}@"
            f"{settings.MYSQL_SERVER}:{settings.MYSQL_PORT}/{settings.MYSQL_DB}"
        )

# SQLite连接池优化配置
if settings.DATABASE_TYPE.lower() == "sqlite":
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        connect_args={
            "check_same_thread": False,  # SQLite特有配置
            "timeout": 30,  # 连接超时时间
        },
        echo=False,  # 生产环境关闭SQL日志
        pool_size=10,  # 基础连接数
        max_overflow=20,  # 最大溢出连接数
        pool_timeout=30,  # 获取连接超时时间
        pool_recycle=3600,  # 连接回收时间（1小时）
        pool_pre_ping=True,  # 连接前ping检查
    )
```

#### 连接池监控指标
```
📊 连接池状态监控
├── 基础连接数: 10
├── 最大溢出连接: 20
├── 连接超时时间: 30秒
├── 连接回收时间: 3600秒 (1小时)
├── 连接前检查: 启用
└── 总连接容量: 30 (10基础 + 20溢出)
```

### 1.3 元数据访问调用链路

#### 智能查询服务调用流程
```python
# chatdb/backend/app/services/text2sql_service.py
@cache_query_result(ttl=1800)  # 缓存30分钟
def process_text2sql_query(db: Session, connection: DBConnection, natural_language_query: str) -> QueryResponse:
    """处理自然语言查询并转换为SQL"""
    
    # 1. 检索相关表结构（使用缓存）
    schema_context = get_cached_schema_context(db, connection.id, natural_language_query)
    
    # 2. 获取值映射（使用缓存）
    value_mappings = get_cached_value_mappings(db, schema_context)
    
    # 3. 获取财务元数据（使用缓存）
    metadata = get_cached_financial_metadata("financial_data")
    
    # 4. 构建增强提示
    prompt = construct_prompt(schema_context, natural_language_query, value_mappings, metadata)
    
    # 5. 调用LLM API
    llm_response = call_llm_api_with_cache(prompt, natural_language_query)
```

#### 元数据读取具体实现
```python
# chatdb/backend/app/services/text2sql_utils.py
def get_financial_metadata(table_name: str) -> Dict[str, Any]:
    """从元数据库获取财务表的增强元数据"""
    try:
        # 获取元数据库路径
        metadata_db_path = getattr(settings, 'METADATA_DB_PATH', settings.SQLITE_DB_PATH)
        
        conn = sqlite3.connect(metadata_db_path)
        cursor = conn.cursor()
        
        metadata = {
            "table_description": None,
            "column_descriptions": [],
            "business_rules": [],
            "has_metadata": False
        }
        
        # 检查元数据表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='table_descriptions'")
        if not cursor.fetchone():
            logger.warning("元数据表不存在于数据库中")
            return metadata
        
        # 查询表描述
        cursor.execute("""
            SELECT description, business_purpose, data_scale
            FROM table_descriptions
            WHERE table_name = ?
        """, (table_name,))
        
        # 查询字段描述
        cursor.execute("""
            SELECT column_name, chinese_name, description, data_type, 
                   business_rules, ai_understanding_points
            FROM column_descriptions
            WHERE table_name = ?
        """, (table_name,))
        
        # 查询业务规则
        cursor.execute("""
            SELECT rule_category, rule_description, sql_example, importance_level
            FROM business_rules
            WHERE table_name = ?
            ORDER BY
                CASE importance_level
                    WHEN 'CRITICAL' THEN 1
                    WHEN 'HIGH' THEN 2
                    WHEN 'MEDIUM' THEN 3
                    ELSE 4
                END
        """, (table_name,))
        
        metadata["has_metadata"] = True
        conn.close()
        
        return metadata
        
    except Exception as e:
        logger.error(f"获取元数据失败: {e}")
        return {"has_metadata": False}
```

---

## 2. 元数据关联关系建立过程

### 2.1 表结构发现机制

#### 数据库结构检索
```python
# 表结构发现流程
def retrieve_relevant_schema(db: Session, connection_id: int, query: str) -> Dict[str, Any]:
    """检索与查询相关的数据库结构"""
    
    # 1. 获取数据库连接信息
    connection = crud.db_connection.get(db, id=connection_id)
    
    # 2. 建立数据库连接
    engine = get_db_engine(connection)
    
    # 3. 检索表结构
    with engine.connect() as conn:
        # 获取所有表名
        tables = conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
        
        schema_context = {}
        for table in tables:
            table_name = table[0]
            
            # 获取表的列信息
            columns_info = conn.execute(f"PRAGMA table_info({table_name})").fetchall()
            
            schema_context[table_name] = {
                'columns': [
                    {
                        'name': col[1],
                        'type': col[2],
                        'nullable': not col[3],
                        'primary_key': bool(col[5])
                    }
                    for col in columns_info
                ]
            }
    
    return schema_context
```

### 2.2 元数据增强集成

#### 元数据模型定义
```python
# chatdb/backend/app/models/metadata_models.py
class EnhancedColumnDescription(Base):
    """增强的字段描述模型"""
    __tablename__ = "column_descriptions"
    
    table_name = Column(String(255), primary_key=True)
    column_name = Column(String(255), primary_key=True)
    chinese_name = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    data_type = Column(Text, nullable=True)
    business_rules = Column(Text, nullable=True)
    ai_understanding_points = Column(Text, nullable=True)
    
    # 增强字段
    field_category = Column(Text, default='')
    usage_scenarios = Column(Text, default='')
    common_values = Column(Text, default='')
    related_fields = Column(Text, default='')
    calculation_rules = Column(Text, default='')
    ai_prompt_hints = Column(Text, default='')

class QueryPattern(Base):
    """查询模式模型"""
    __tablename__ = "query_patterns"
    
    id = Column(Integer, primary_key=True, index=True)
    pattern_name = Column(String(255), nullable=False)
    pattern_description = Column(Text, nullable=False)
    natural_language_examples = Column(Text, nullable=False)
    sql_template = Column(Text, nullable=False)
    required_fields = Column(Text, nullable=False)
    business_scenario = Column(String(255), nullable=True)
    difficulty_level = Column(String(50), default='MEDIUM')
```

### 2.3 表间关系管理

#### 关系发现和存储
```python
# 表关系发现机制
def discover_table_relationships(db: Session, connection_id: int):
    """发现并存储表间关系"""
    
    # 1. 分析外键约束
    foreign_keys = analyze_foreign_key_constraints(connection_id)
    
    # 2. 分析字段名称相似性
    field_similarities = analyze_field_name_similarities(connection_id)
    
    # 3. 分析数据类型匹配
    type_matches = analyze_data_type_matches(connection_id)
    
    # 4. 存储关系信息
    for relationship in discovered_relationships:
        relationship_obj = FieldRelationship(
            table_name=relationship['source_table'],
            primary_field=relationship['source_field'],
            related_field=relationship['target_field'],
            relationship_type=relationship['type'],
            relationship_description=relationship['description']
        )
        db.add(relationship_obj)
    
    db.commit()
```

---

## 3. 元数据库升级影响评估

### 3.1 配置变更分析

#### LLM API配置升级
```bash
# 升级前配置 (DeepSeek)
OPENAI_API_BASE=https://api.deepseek.com/v1
LLM_MODEL=deepseek-chat

# 升级后配置 (阿里云百炼)
OPENAI_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
LLM_MODEL=qwen-max
```

#### Neo4j连接配置变更
```bash
# 配置变更检测
# 升级前: NEO4J_URI=bolt://localhost:7687
# 升级后: NEO4J_URI=bolt://**************:7687

# 影响评估：
# 1. 图数据可视化功能需要重新连接
# 2. 数据建模同步功能需要更新
# 3. 缓存中的图数据需要清理
```

### 3.2 兼容性问题识别

#### 数据库连接兼容性
```python
# 连接兼容性检查
def check_database_compatibility():
    """检查数据库连接兼容性"""
    
    compatibility_issues = []
    
    # 1. 检查元数据库文件是否存在
    metadata_db_path = settings.METADATA_DB_PATH
    if not os.path.exists(metadata_db_path):
        compatibility_issues.append({
            'type': 'MISSING_FILE',
            'description': f'元数据库文件不存在: {metadata_db_path}',
            'severity': 'CRITICAL'
        })
    
    # 2. 检查业务数据库文件是否存在
    business_db_path = settings.BUSINESS_DB_PATH
    if not os.path.exists(business_db_path):
        compatibility_issues.append({
            'type': 'MISSING_FILE',
            'description': f'业务数据库文件不存在: {business_db_path}',
            'severity': 'CRITICAL'
        })
    
    # 3. 检查元数据表结构
    try:
        conn = sqlite3.connect(metadata_db_path)
        cursor = conn.cursor()
        
        required_tables = ['table_descriptions', 'column_descriptions', 'business_rules']
        for table in required_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if not cursor.fetchone():
                compatibility_issues.append({
                    'type': 'MISSING_TABLE',
                    'description': f'元数据表不存在: {table}',
                    'severity': 'HIGH'
                })
        
        conn.close()
        
    except Exception as e:
        compatibility_issues.append({
            'type': 'CONNECTION_ERROR',
            'description': f'无法连接元数据库: {str(e)}',
            'severity': 'CRITICAL'
        })
    
    return compatibility_issues
```

### 3.3 系统状态评估

#### 当前系统状态
```
🔍 系统状态评估报告

数据库连接状态:
├── 元数据库 (resource.db): ✅ 正常
├── 业务数据库 (fin_data.db): ✅ 正常
├── Neo4j图数据库: ⚠️ 需要验证新地址连接
└── 连接池状态: ✅ 正常 (10基础+20溢出)

配置升级状态:
├── LLM API: ✅ 已升级到阿里云百炼
├── 模型版本: ✅ qwen-max
├── 元数据增强: ✅ 启用
├── 缓存机制: ✅ 启用
└── 混合检索: ✅ 启用 (Milvus可用)

潜在问题:
├── Neo4j连接地址变更: 需要验证连接
├── 缓存数据一致性: 建议清理重建
└── 元数据同步状态: 需要检查同步完整性
```

---

## 4. 具体技术实现细节

### 4.1 缓存机制实现

#### 多层缓存架构
```python
# chatdb/backend/app/services/cache_service.py
class CacheService:
    """多层缓存服务实现"""

    def __init__(self):
        # 内存缓存存储
        self._memory_cache: Dict[str, Dict[str, Any]] = {}

        # 缓存配置
        self.default_ttl = 3600  # 默认1小时过期
        self.metadata_ttl = 7200  # 元数据2小时过期
        self.query_result_ttl = 1800  # 查询结果30分钟过期

        # 缓存统计
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'evictions': 0
        }

    def _generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items()) if kwargs else {}
        }
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        return f"{prefix}:{key_hash}"
```

#### 缓存装饰器实现
```python
# 查询结果缓存装饰器
def cache_query_result(ttl: int = 1800):
    """缓存查询结果的装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = cache_service._generate_cache_key(
                f"query_result:{func.__name__}",
                *args,
                **kwargs
            )

            # 尝试从缓存获取
            cached_result = cache_service.get(cache_key)
            if cached_result is not None:
                logger.info(f"缓存命中: {cache_key}")
                return cached_result

            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_service.set(cache_key, result, ttl)
            logger.info(f"缓存设置: {cache_key}")

            return result
        return wrapper
    return decorator

# 元数据缓存装饰器
def cache_metadata(ttl: int = 7200):
    """缓存元数据的装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = cache_service._generate_cache_key(
                f"metadata:{func.__name__}",
                *args,
                **kwargs
            )

            cached_result = cache_service.get(cache_key)
            if cached_result is not None:
                return cached_result

            result = func(*args, **kwargs)
            cache_service.set(cache_key, result, ttl)

            return result
        return wrapper
    return decorator
```

### 4.2 元数据增强机制

#### 增强提示构建
```python
# chatdb/backend/app/services/enhanced_prompt_service.py
def construct_enhanced_prompt(schema_context: Dict, query: str, metadata: Dict) -> str:
    """构建增强的AI提示"""

    prompt_parts = []

    # 1. 基础系统提示
    prompt_parts.append("""
    你是一个专业的财务数据分析SQL生成助手。请根据用户的自然语言查询生成准确的SQL语句。
    """)

    # 2. 数据库结构信息
    if schema_context:
        prompt_parts.append("## 数据库结构信息")
        for table_name, table_info in schema_context.items():
            prompt_parts.append(f"### 表: {table_name}")

            # 添加表描述（如果有元数据）
            if metadata.get("has_metadata") and metadata.get("table_description"):
                prompt_parts.append(f"表描述: {metadata['table_description']}")

            # 添加字段信息
            prompt_parts.append("字段信息:")
            for column in table_info.get('columns', []):
                column_desc = f"- {column['name']} ({column['type']})"

                # 添加字段描述（如果有元数据）
                if metadata.get("column_descriptions"):
                    for col_desc in metadata["column_descriptions"]:
                        if col_desc["column_name"] == column["name"]:
                            column_desc += f" - {col_desc.get('chinese_name', '')}: {col_desc.get('description', '')}"
                            break

                prompt_parts.append(column_desc)

    # 3. 业务规则信息
    if metadata.get("business_rules"):
        prompt_parts.append("## 重要业务规则")
        for rule in metadata["business_rules"]:
            prompt_parts.append(f"- {rule['rule_category']}: {rule['rule_description']}")
            if rule.get('sql_example'):
                prompt_parts.append(f"  示例: {rule['sql_example']}")

    # 4. 数据质量检查规则
    prompt_parts.append("""
    ## 数据质量检查规则
    1. balance字段是TEXT类型，需要使用CAST(balance AS REAL)进行类型转换
    2. 收入类科目(60xx)使用credit_amount字段
    3. 成本费用类科目(64xx,66xx)使用debit_amount字段
    4. 资产负债类科目使用balance字段（需类型转换）
    5. 添加适当的WHERE条件过滤空值和无效数据
    """)

    # 5. 用户查询
    prompt_parts.append(f"## 用户查询\n{query}")

    # 6. 输出要求
    prompt_parts.append("""
    ## 输出要求
    请生成标准的SQL查询语句，确保：
    1. 语法正确，可以直接执行
    2. 遵循上述业务规则
    3. 包含适当的数据类型转换
    4. 添加必要的过滤条件
    5. 使用合适的聚合函数和分组
    """)

    return "\n\n".join(prompt_parts)
```

### 4.3 错误监控和日志机制

#### 日志配置
```python
# chatdb/backend/app/core/logging_config.py
import logging
import logging.handlers
import os
from datetime import datetime

def setup_logging():
    """设置日志配置"""

    # 创建日志目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 配置根日志器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            # 控制台输出
            logging.StreamHandler(),

            # 文件输出（按日期轮转）
            logging.handlers.TimedRotatingFileHandler(
                filename=os.path.join(log_dir, 'app.log'),
                when='midnight',
                interval=1,
                backupCount=30,
                encoding='utf-8'
            )
        ]
    )

    # 设置特定模块的日志级别
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)
```

#### 错误监控实现
```python
# 数据库连接错误监控
class DatabaseConnectionMonitor:
    """数据库连接监控器"""

    def __init__(self):
        self.connection_errors = []
        self.last_check_time = None

    def check_connection_health(self):
        """检查数据库连接健康状态"""
        health_status = {
            'metadata_db': self._check_metadata_db(),
            'business_db': self._check_business_db(),
            'neo4j_db': self._check_neo4j_db(),
            'timestamp': datetime.now().isoformat()
        }

        # 记录连接问题
        for db_name, status in health_status.items():
            if not status.get('healthy', False):
                self.connection_errors.append({
                    'database': db_name,
                    'error': status.get('error'),
                    'timestamp': health_status['timestamp']
                })

        return health_status

    def _check_metadata_db(self):
        """检查元数据库连接"""
        try:
            conn = sqlite3.connect(settings.METADATA_DB_PATH, timeout=5)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            conn.close()
            return {'healthy': True}
        except Exception as e:
            logger.error(f"元数据库连接失败: {e}")
            return {'healthy': False, 'error': str(e)}

    def _check_business_db(self):
        """检查业务数据库连接"""
        try:
            conn = sqlite3.connect(settings.BUSINESS_DB_PATH, timeout=5)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM financial_data LIMIT 1")
            conn.close()
            return {'healthy': True}
        except Exception as e:
            logger.error(f"业务数据库连接失败: {e}")
            return {'healthy': False, 'error': str(e)}
```

### 4.4 性能优化实现

#### 查询优化器
```python
# 查询性能优化
class QueryOptimizer:
    """SQL查询优化器"""

    def __init__(self):
        self.optimization_rules = [
            self._add_limit_clause,
            self._optimize_where_conditions,
            self._add_index_hints,
            self._optimize_joins
        ]

    def optimize_query(self, sql: str, table_info: Dict) -> str:
        """优化SQL查询"""
        optimized_sql = sql

        for rule in self.optimization_rules:
            try:
                optimized_sql = rule(optimized_sql, table_info)
            except Exception as e:
                logger.warning(f"查询优化规则失败: {e}")

        return optimized_sql

    def _add_limit_clause(self, sql: str, table_info: Dict) -> str:
        """添加LIMIT子句防止大结果集"""
        if 'LIMIT' not in sql.upper() and 'COUNT(' not in sql.upper():
            # 对于非聚合查询添加默认限制
            max_limit = settings.DEFAULT_QUERY_LIMIT
            sql += f" LIMIT {max_limit}"

        return sql

    def _optimize_where_conditions(self, sql: str, table_info: Dict) -> str:
        """优化WHERE条件"""
        # 添加常用的过滤条件
        optimizations = []

        if 'balance' in sql and 'CAST(balance AS REAL)' not in sql:
            # 自动添加balance字段类型转换
            sql = sql.replace('balance', 'CAST(balance AS REAL)')
            optimizations.append("添加balance字段类型转换")

        if 'financial_data' in sql and 'WHERE' in sql.upper():
            # 添加数据质量过滤
            if 'balance IS NOT NULL' not in sql:
                sql = sql.replace('WHERE', 'WHERE balance IS NOT NULL AND balance != \'\' AND')
                optimizations.append("添加数据质量过滤")

        if optimizations:
            logger.info(f"查询优化: {', '.join(optimizations)}")

        return sql
```

---

## 5. 升级后修复建议

### 5.1 立即修复项目

#### 1. Neo4j连接配置更新
```bash
# 检查Neo4j连接状态
curl -u neo4j:password http://**************:7474/db/data/

# 更新环境配置
NEO4J_URI=bolt://**************:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password

# 测试连接
python -c "
from neo4j import GraphDatabase
driver = GraphDatabase.driver('bolt://**************:7687', auth=('neo4j', 'password'))
with driver.session() as session:
    result = session.run('RETURN 1')
    print('Neo4j连接成功')
driver.close()
"
```

#### 2. 缓存清理和重建
```python
# 清理缓存脚本
def clear_all_caches():
    """清理所有缓存"""

    # 1. 清理内存缓存
    cache_service.clear()

    # 2. 清理Redis缓存（如果启用）
    if settings.REDIS_ENABLED:
        import redis
        r = redis.Redis(host=settings.REDIS_HOST, port=settings.REDIS_PORT, db=settings.REDIS_DB)
        r.flushdb()

    # 3. 重新加载元数据
    for connection_id in get_all_connection_ids():
        refresh_metadata_cache(connection_id)

    logger.info("所有缓存已清理并重建")
```

#### 3. 元数据同步验证
```python
# 元数据同步验证脚本
def verify_metadata_sync():
    """验证元数据同步状态"""

    verification_results = []

    # 1. 检查表描述完整性
    missing_descriptions = check_missing_table_descriptions()
    if missing_descriptions:
        verification_results.append({
            'type': 'MISSING_TABLE_DESCRIPTIONS',
            'items': missing_descriptions,
            'severity': 'MEDIUM'
        })

    # 2. 检查字段描述完整性
    missing_column_descriptions = check_missing_column_descriptions()
    if missing_column_descriptions:
        verification_results.append({
            'type': 'MISSING_COLUMN_DESCRIPTIONS',
            'items': missing_column_descriptions,
            'severity': 'LOW'
        })

    # 3. 检查业务规则完整性
    missing_business_rules = check_missing_business_rules()
    if missing_business_rules:
        verification_results.append({
            'type': 'MISSING_BUSINESS_RULES',
            'items': missing_business_rules,
            'severity': 'HIGH'
        })

    return verification_results
```

### 5.2 性能优化建议

#### 1. 连接池调优
```python
# 根据实际负载调整连接池参数
DB_POOL_SIZE=15  # 增加基础连接数
DB_MAX_OVERFLOW=30  # 增加溢出连接数
DB_POOL_TIMEOUT=45  # 增加连接超时时间
DB_POOL_RECYCLE=7200  # 延长连接回收时间
```

#### 2. 缓存策略优化
```python
# 优化缓存TTL设置
CACHE_TTL=5400  # 增加到1.5小时
METADATA_CACHE_TTL=10800  # 元数据缓存3小时
QUERY_RESULT_CACHE_TTL=2700  # 查询结果缓存45分钟
```

#### 3. 查询性能优化
```sql
-- 为financial_data表添加复合索引
CREATE INDEX idx_financial_data_composite ON financial_data(year, month, account_code);
CREATE INDEX idx_financial_data_project ON financial_data(project_name, year, month);
CREATE INDEX idx_financial_data_amount ON financial_data(debit_amount, credit_amount);
```

### 5.3 监控和告警设置

#### 1. 健康检查端点
```python
# 添加健康检查API
@router.get("/health/detailed")
def detailed_health_check():
    """详细健康检查"""

    health_status = {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'components': {}
    }

    # 检查数据库连接
    db_monitor = DatabaseConnectionMonitor()
    db_health = db_monitor.check_connection_health()
    health_status['components']['databases'] = db_health

    # 检查缓存状态
    cache_stats = cache_service.get_stats()
    health_status['components']['cache'] = cache_stats

    # 检查LLM API状态
    llm_health = check_llm_api_health()
    health_status['components']['llm_api'] = llm_health

    # 判断整体状态
    if any(not comp.get('healthy', True) for comp in health_status['components'].values()):
        health_status['status'] = 'unhealthy'

    return health_status
```

#### 2. 性能监控指标
```python
# 性能指标收集
class PerformanceMetrics:
    """性能指标收集器"""

    def __init__(self):
        self.metrics = {
            'query_count': 0,
            'avg_response_time': 0,
            'cache_hit_rate': 0,
            'error_rate': 0,
            'active_connections': 0
        }

    def record_query_execution(self, execution_time: float, success: bool):
        """记录查询执行指标"""
        self.metrics['query_count'] += 1

        # 更新平均响应时间
        current_avg = self.metrics['avg_response_time']
        count = self.metrics['query_count']
        self.metrics['avg_response_time'] = (current_avg * (count - 1) + execution_time) / count

        # 更新错误率
        if not success:
            error_count = self.metrics.get('error_count', 0) + 1
            self.metrics['error_count'] = error_count
            self.metrics['error_rate'] = error_count / count * 100
```

通过以上详细的技术架构分析和修复建议，可以确保智能查询功能与元数据库之间的稳定连接和高效数据流转，同时为系统升级后的稳定运行提供保障。
