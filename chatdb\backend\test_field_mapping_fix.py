#!/usr/bin/env python3
"""
测试字段映射优先级修复效果
验证在公司名称和科目名称查询场景下是否正确选择了优先字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.text2sql_utils import (
    select_preferred_field, 
    apply_field_priority_corrections,
    process_sql_with_value_mappings,
    SEMANTIC_FIELD_GROUPS
)

def test_field_priority_selection():
    """测试字段优先级选择逻辑"""
    print("🧪 测试字段优先级选择逻辑")
    print("=" * 60)
    
    # 测试用例1: 公司相关字段选择
    test_cases_company = [
        {
            "term": "公司",
            "available_fields": ["accounting_unit_name", "accounting_organization"],
            "expected": "accounting_unit_name"
        },
        {
            "term": "企业",
            "available_fields": ["accounting_organization", "accounting_unit_name"],
            "expected": "accounting_unit_name"
        },
        {
            "term": "company_name",
            "available_fields": ["accounting_unit_name", "accounting_organization"],
            "expected": "accounting_unit_name"
        }
    ]
    
    print("📋 公司字段选择测试:")
    for i, case in enumerate(test_cases_company, 1):
        result = select_preferred_field(case["term"], case["available_fields"])
        status = "✅" if result == case["expected"] else "❌"
        print(f"  {i}. 术语: '{case['term']}' → 选择: '{result}' (期望: '{case['expected']}') {status}")
    
    # 测试用例2: 科目相关字段选择
    test_cases_account = [
        {
            "term": "科目名称",
            "available_fields": ["account_full_name", "account_name"],
            "expected": "account_full_name"
        },
        {
            "term": "会计科目名称",
            "available_fields": ["account_name", "account_full_name"],
            "expected": "account_full_name"
        },
        {
            "term": "account_name",
            "available_fields": ["account_full_name", "account_name"],
            "expected": "account_full_name"
        }
    ]
    
    print("\n📋 科目字段选择测试:")
    for i, case in enumerate(test_cases_account, 1):
        result = select_preferred_field(case["term"], case["available_fields"])
        status = "✅" if result == case["expected"] else "❌"
        print(f"  {i}. 术语: '{case['term']}' → 选择: '{result}' (期望: '{case['expected']}') {status}")


def test_sql_field_corrections():
    """测试SQL字段修正逻辑"""
    print("\n🔧 测试SQL字段修正逻辑")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "公司字段替换",
            "input_sql": "SELECT accounting_organization, debit_amount FROM financial_data WHERE year = 2024",
            "expected_field": "accounting_unit_name",
            "description": "将 accounting_organization 替换为 accounting_unit_name"
        },
        {
            "name": "科目名称字段替换",
            "input_sql": "SELECT account_name FROM financial_data WHERE account_name LIKE '%管理费用%'",
            "expected_field": "account_full_name",
            "description": "在查询科目内容时将 account_name 替换为 account_full_name"
        },
        {
            "name": "禁用字段替换",
            "input_sql": "SELECT company_name, company_id FROM financial_data",
            "expected_field": "accounting_unit_name",
            "description": "将不存在的字段替换为正确字段"
        },
        {
            "name": "复合查询",
            "input_sql": "SELECT accounting_organization, account_name FROM financial_data WHERE account_name = '销售费用' AND company_name = '中石化'",
            "expected_fields": ["accounting_unit_name", "account_full_name"],
            "description": "复合查询中的多字段替换"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['name']}:")
        print(f"   描述: {case['description']}")
        print(f"   原始SQL: {case['input_sql']}")
        
        corrected_sql = apply_field_priority_corrections(case['input_sql'])
        print(f"   修正SQL: {corrected_sql}")
        
        # 检查是否包含期望的字段
        if 'expected_field' in case:
            contains_expected = case['expected_field'] in corrected_sql
            status = "✅" if contains_expected else "❌"
            print(f"   包含期望字段 '{case['expected_field']}': {status}")
        
        if 'expected_fields' in case:
            all_present = all(field in corrected_sql for field in case['expected_fields'])
            status = "✅" if all_present else "❌"
            print(f"   包含所有期望字段 {case['expected_fields']}: {status}")


def test_value_mappings_integration():
    """测试值映射集成效果"""
    print("\n🔗 测试值映射集成效果")
    print("=" * 60)
    
    # 模拟值映射数据
    mock_value_mappings = {
        "financial_data.accounting_unit_name": {
            "公司": "accounting_unit_name",
            "企业": "accounting_unit_name",
            "company_name": "accounting_unit_name"
        },
        "financial_data.account_full_name": {
            "科目名称": "account_full_name",
            "会计科目名称": "account_full_name",
            "account_name": "account_full_name"
        }
    }
    
    test_cases = [
        {
            "name": "公司查询映射",
            "input_sql": "SELECT company_name, debit_amount FROM financial_data WHERE company_name = '中石化'",
            "description": "测试公司相关的字段和值映射"
        },
        {
            "name": "科目查询映射", 
            "input_sql": "SELECT account_name FROM financial_data WHERE account_name LIKE '%管理费用%'",
            "description": "测试科目相关的字段和值映射"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['name']}:")
        print(f"   描述: {case['description']}")
        print(f"   原始SQL: {case['input_sql']}")
        
        processed_sql = process_sql_with_value_mappings(case['input_sql'], mock_value_mappings)
        print(f"   处理后SQL: {processed_sql}")
        
        # 检查是否使用了正确的优先字段
        uses_preferred_company = "accounting_unit_name" in processed_sql
        uses_preferred_account = "account_full_name" in processed_sql
        
        if "company" in case['input_sql'].lower():
            status = "✅" if uses_preferred_company else "❌"
            print(f"   使用优先公司字段: {status}")
        
        if "account" in case['input_sql'].lower():
            status = "✅" if uses_preferred_account else "❌"
            print(f"   使用优先科目字段: {status}")


def test_semantic_groups_config():
    """测试语义分组配置"""
    print("\n📚 测试语义分组配置")
    print("=" * 60)
    
    print("当前语义分组配置:")
    for group_name, config in SEMANTIC_FIELD_GROUPS.items():
        print(f"\n🔸 {group_name}:")
        print(f"   首选字段: {config['preferred_field']}")
        print(f"   备选字段: {config['alternative_fields']}")
        print(f"   自然语言术语: {config['natural_language_terms']}")


def main():
    """主测试函数"""
    print("🚀 字段映射优先级修复测试")
    print("=" * 80)
    
    try:
        # 运行所有测试
        test_semantic_groups_config()
        test_field_priority_selection()
        test_sql_field_corrections()
        test_value_mappings_integration()
        
        print("\n" + "=" * 80)
        print("✅ 所有测试完成！请检查上述结果确认修复效果。")
        print("\n💡 如果看到 ❌ 标记，说明该测试用例未通过，需要进一步调试。")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
