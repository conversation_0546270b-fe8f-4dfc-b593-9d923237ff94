#!/usr/bin/env python3
"""
数据库表结构分析工具
分析resource.db和fin_data.db的表结构和重复性
"""

import sqlite3
import json
from pathlib import Path
from datetime import datetime

class DatabaseAnalyzer:
    """数据库分析器"""
    
    def __init__(self):
        self.resource_db = "resource.db"
        self.business_db = "fin_data.db"
        self.analysis_results = {
            'timestamp': datetime.now().isoformat(),
            'resource_db': {},
            'business_db': {},
            'duplicate_tables': [],
            'structure_differences': {},
            'recommendations': []
        }
    
    def analyze_databases(self):
        """分析两个数据库"""
        print("🔍 开始数据库表结构分析...")
        print("=" * 60)
        
        # 1. 分析resource.db
        self._analyze_database(self.resource_db, 'resource_db')
        
        # 2. 分析fin_data.db
        self._analyze_database(self.business_db, 'business_db')
        
        # 3. 查找重复表
        self._find_duplicate_tables()
        
        # 4. 分析结构差异
        self._analyze_structure_differences()
        
        # 5. 生成建议
        self._generate_recommendations()
        
        # 6. 生成报告
        self._generate_report()
        
        return self.analysis_results
    
    def _analyze_database(self, db_path: str, db_key: str):
        """分析单个数据库"""
        print(f"\n📊 分析数据库: {db_path}")
        
        if not Path(db_path).exists():
            print(f"  ❌ 数据库文件不存在: {db_path}")
            self.analysis_results[db_key] = {'error': f'文件不存在: {db_path}'}
            return
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            
            print(f"  📋 发现 {len(tables)} 个表")
            
            db_info = {
                'table_count': len(tables),
                'tables': {},
                'file_size': Path(db_path).stat().st_size
            }
            
            # 分析每个表的结构
            for table_name in tables:
                print(f"    🔍 分析表: {table_name}")
                table_info = self._analyze_table_structure(cursor, table_name)
                db_info['tables'][table_name] = table_info
            
            conn.close()
            self.analysis_results[db_key] = db_info
            
        except Exception as e:
            print(f"  ❌ 分析数据库失败: {e}")
            self.analysis_results[db_key] = {'error': str(e)}
    
    def _analyze_table_structure(self, cursor, table_name: str):
        """分析表结构"""
        try:
            # 获取表结构信息
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            # 获取表的行数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            
            # 获取索引信息
            cursor.execute(f"PRAGMA index_list({table_name})")
            indexes = cursor.fetchall()
            
            # 获取外键信息
            cursor.execute(f"PRAGMA foreign_key_list({table_name})")
            foreign_keys = cursor.fetchall()
            
            table_info = {
                'row_count': row_count,
                'column_count': len(columns),
                'columns': [],
                'indexes': [],
                'foreign_keys': [],
                'primary_keys': []
            }
            
            # 处理列信息
            for col in columns:
                column_info = {
                    'name': col[1],
                    'type': col[2],
                    'not_null': bool(col[3]),
                    'default_value': col[4],
                    'primary_key': bool(col[5])
                }
                table_info['columns'].append(column_info)
                
                if column_info['primary_key']:
                    table_info['primary_keys'].append(column_info['name'])
            
            # 处理索引信息
            for idx in indexes:
                table_info['indexes'].append({
                    'name': idx[1],
                    'unique': bool(idx[2])
                })
            
            # 处理外键信息
            for fk in foreign_keys:
                table_info['foreign_keys'].append({
                    'column': fk[3],
                    'referenced_table': fk[2],
                    'referenced_column': fk[4]
                })
            
            return table_info
            
        except Exception as e:
            return {'error': str(e)}
    
    def _find_duplicate_tables(self):
        """查找重复表名"""
        print(f"\n🔍 查找重复表名...")
        
        resource_tables = set()
        business_tables = set()
        
        if 'tables' in self.analysis_results['resource_db']:
            resource_tables = set(self.analysis_results['resource_db']['tables'].keys())
        
        if 'tables' in self.analysis_results['business_db']:
            business_tables = set(self.analysis_results['business_db']['tables'].keys())
        
        # 找到重复的表名
        duplicate_tables = resource_tables.intersection(business_tables)
        
        print(f"  📊 resource.db表数: {len(resource_tables)}")
        print(f"  📊 fin_data.db表数: {len(business_tables)}")
        print(f"  🔄 重复表数: {len(duplicate_tables)}")
        
        if duplicate_tables:
            print(f"  📋 重复表清单:")
            for table in sorted(duplicate_tables):
                print(f"    - {table}")
        
        self.analysis_results['duplicate_tables'] = list(duplicate_tables)
        self.analysis_results['resource_table_names'] = list(resource_tables)
        self.analysis_results['business_table_names'] = list(business_tables)
    
    def _analyze_structure_differences(self):
        """分析重复表的结构差异"""
        print(f"\n🔍 分析重复表结构差异...")
        
        duplicate_tables = self.analysis_results['duplicate_tables']
        
        if not duplicate_tables:
            print("  ✅ 没有重复表，无需分析结构差异")
            return
        
        for table_name in duplicate_tables:
            print(f"  🔍 分析表: {table_name}")
            
            resource_table = self.analysis_results['resource_db']['tables'].get(table_name, {})
            business_table = self.analysis_results['business_db']['tables'].get(table_name, {})
            
            differences = self._compare_table_structures(resource_table, business_table)
            
            if differences:
                self.analysis_results['structure_differences'][table_name] = differences
                print(f"    ⚠️ 发现结构差异")
                for diff_type, diff_details in differences.items():
                    if diff_details:
                        print(f"      - {diff_type}: {len(diff_details)} 项差异")
            else:
                print(f"    ✅ 结构一致")
    
    def _compare_table_structures(self, table1, table2):
        """比较两个表的结构"""
        differences = {
            'column_differences': [],
            'row_count_difference': None,
            'index_differences': [],
            'foreign_key_differences': []
        }
        
        # 比较行数
        if 'row_count' in table1 and 'row_count' in table2:
            if table1['row_count'] != table2['row_count']:
                differences['row_count_difference'] = {
                    'resource_db': table1['row_count'],
                    'business_db': table2['row_count']
                }
        
        # 比较列结构
        if 'columns' in table1 and 'columns' in table2:
            resource_columns = {col['name']: col for col in table1['columns']}
            business_columns = {col['name']: col for col in table2['columns']}
            
            all_columns = set(resource_columns.keys()) | set(business_columns.keys())
            
            for col_name in all_columns:
                resource_col = resource_columns.get(col_name)
                business_col = business_columns.get(col_name)
                
                if not resource_col:
                    differences['column_differences'].append({
                        'type': 'missing_in_resource',
                        'column': col_name,
                        'business_db_definition': business_col
                    })
                elif not business_col:
                    differences['column_differences'].append({
                        'type': 'missing_in_business',
                        'column': col_name,
                        'resource_db_definition': resource_col
                    })
                elif resource_col != business_col:
                    differences['column_differences'].append({
                        'type': 'definition_different',
                        'column': col_name,
                        'resource_db_definition': resource_col,
                        'business_db_definition': business_col
                    })
        
        # 移除空的差异项
        differences = {k: v for k, v in differences.items() if v}
        
        return differences
    
    def _generate_recommendations(self):
        """生成建议"""
        print(f"\n💡 生成优化建议...")
        
        recommendations = []
        
        # 检查重复表
        if self.analysis_results['duplicate_tables']:
            recommendations.append({
                'type': 'DUPLICATE_TABLES',
                'priority': 'HIGH',
                'description': f"发现 {len(self.analysis_results['duplicate_tables'])} 个重复表名",
                'action': "建议重新设计数据库架构，确保元数据库和业务数据库的表名不重复",
                'tables': self.analysis_results['duplicate_tables']
            })
        
        # 检查结构差异
        if self.analysis_results['structure_differences']:
            for table_name, differences in self.analysis_results['structure_differences'].items():
                recommendations.append({
                    'type': 'STRUCTURE_DIFFERENCE',
                    'priority': 'MEDIUM',
                    'description': f"表 {table_name} 在两个数据库中结构不一致",
                    'action': f"统一表 {table_name} 的结构定义",
                    'differences': differences
                })
        
        # 检查数据量差异
        for table_name in self.analysis_results['duplicate_tables']:
            resource_table = self.analysis_results['resource_db']['tables'].get(table_name, {})
            business_table = self.analysis_results['business_db']['tables'].get(table_name, {})
            
            resource_count = resource_table.get('row_count', 0)
            business_count = business_table.get('row_count', 0)
            
            if abs(resource_count - business_count) > 1000:  # 差异超过1000行
                recommendations.append({
                    'type': 'DATA_INCONSISTENCY',
                    'priority': 'MEDIUM',
                    'description': f"表 {table_name} 的数据量差异较大",
                    'action': f"检查数据同步机制，确保数据一致性",
                    'resource_count': resource_count,
                    'business_count': business_count
                })
        
        self.analysis_results['recommendations'] = recommendations
        
        if recommendations:
            print(f"  📋 生成了 {len(recommendations)} 条建议")
            for rec in recommendations:
                print(f"    - {rec['priority']}: {rec['description']}")
        else:
            print(f"  ✅ 未发现需要优化的问题")
    
    def _generate_report(self):
        """生成分析报告"""
        print(f"\n" + "=" * 60)
        print(f"📋 数据库表结构分析报告")
        print(f"=" * 60)
        
        # 基本统计
        resource_count = self.analysis_results['resource_db'].get('table_count', 0)
        business_count = self.analysis_results['business_db'].get('table_count', 0)
        duplicate_count = len(self.analysis_results['duplicate_tables'])
        
        print(f"📊 基本统计:")
        print(f"  - resource.db表数: {resource_count}")
        print(f"  - fin_data.db表数: {business_count}")
        print(f"  - 重复表数: {duplicate_count}")
        print(f"  - 结构差异表数: {len(self.analysis_results['structure_differences'])}")
        
        # 重复表清单
        if self.analysis_results['duplicate_tables']:
            print(f"\n🔄 重复表清单:")
            for table in sorted(self.analysis_results['duplicate_tables']):
                print(f"  - {table}")
        
        # 建议摘要
        if self.analysis_results['recommendations']:
            print(f"\n💡 优化建议摘要:")
            high_priority = [r for r in self.analysis_results['recommendations'] if r['priority'] == 'HIGH']
            medium_priority = [r for r in self.analysis_results['recommendations'] if r['priority'] == 'MEDIUM']
            
            if high_priority:
                print(f"  🔴 高优先级: {len(high_priority)} 项")
            if medium_priority:
                print(f"  🟡 中优先级: {len(medium_priority)} 项")
        
        # 保存详细报告
        report_file = f"database_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")

def main():
    """主函数"""
    print("🔍 数据库表结构分析工具")
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    analyzer = DatabaseAnalyzer()
    results = analyzer.analyze_databases()
    
    return results

if __name__ == "__main__":
    main()
