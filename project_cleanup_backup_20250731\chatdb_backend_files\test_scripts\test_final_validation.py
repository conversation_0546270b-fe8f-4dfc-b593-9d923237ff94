#!/usr/bin/env python3
"""
最终验证测试脚本
测试SQL字段验证功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.text2sql_service import validate_sql_with_schema

def test_sql_validation():
    """测试SQL验证功能"""
    
    print("🧪 测试SQL字段验证功能...")
    print("=" * 50)
    
    # 模拟schema_context
    schema_context = {
        "columns": [
            {"column_name": "accounting_unit_name", "data_type": "TEXT"},
            {"column_name": "year", "data_type": "INTEGER"},
            {"column_name": "month", "data_type": "INTEGER"},
            {"column_name": "account_code", "data_type": "INTEGER"},
            {"column_name": "debit_amount", "data_type": "REAL"},
            {"column_name": "credit_amount", "data_type": "REAL"},
            {"column_name": "account_full_name", "data_type": "TEXT"},
        ]
    }
    
    # 测试用例
    test_cases = [
        {
            "name": "正确的SQL",
            "sql": """
                SELECT accounting_unit_name, 
                       SUM(CAST(debit_amount AS REAL)) as total_amount
                FROM financial_data 
                WHERE year = 2024 AND month = 2
                GROUP BY accounting_unit_name
            """,
            "should_pass": True
        },
        {
            "name": "使用禁止字段company_name",
            "sql": """
                SELECT company_name, 
                       SUM(CAST(debit_amount AS REAL)) as total_amount
                FROM financial_data 
                WHERE year = 2024 AND month = 2
                GROUP BY company_name
            """,
            "should_pass": False
        },
        {
            "name": "使用禁止字段date",
            "sql": """
                SELECT accounting_unit_name, 
                       SUM(CAST(debit_amount AS REAL)) as total_amount
                FROM financial_data 
                WHERE date BETWEEN '2024-02-01' AND '2024-02-28'
                GROUP BY accounting_unit_name
            """,
            "should_pass": False
        },
        {
            "name": "使用禁止字段sales_expense",
            "sql": """
                SELECT accounting_unit_name, 
                       SUM(CAST(sales_expense AS REAL)) as total_amount
                FROM financial_data 
                WHERE year = 2024 AND month = 2
                GROUP BY accounting_unit_name
            """,
            "should_pass": False
        },
        {
            "name": "使用不存在的字段",
            "sql": """
                SELECT accounting_unit_name, 
                       SUM(CAST(unknown_field AS REAL)) as total_amount
                FROM financial_data 
                WHERE year = 2024 AND month = 2
                GROUP BY accounting_unit_name
            """,
            "should_pass": False
        }
    ]
    
    # 执行测试
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试: {test_case['name']}")
        
        error = validate_sql_with_schema(test_case['sql'], schema_context)
        
        if test_case['should_pass']:
            if error is None:
                print("   ✅ 通过 - SQL验证成功")
            else:
                print(f"   ❌ 失败 - 预期通过但验证失败: {error}")
        else:
            if error is not None:
                print(f"   ✅ 通过 - 正确拦截: {error}")
            else:
                print("   ❌ 失败 - 预期拦截但验证通过")

def test_field_mappings():
    """测试字段映射"""
    
    print("\n" + "=" * 50)
    print("🧪 测试字段映射...")
    
    import sqlite3
    
    metadata_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(metadata_db)
    cursor = conn.cursor()
    
    # 测试关键映射
    test_mappings = [
        'company_name', 'company_id', '公司',
        'sales_expense', '销售费用', 'management_expense', '管理费用',
        'expense', 'expenses', '费用'
    ]
    
    for term in test_mappings:
        cursor.execute("""
            SELECT vm.nl_term, sc.column_name
            FROM valuemapping vm
            JOIN schemacolumn sc ON vm.column_id = sc.id
            WHERE vm.nl_term = ?
        """, (term,))
        
        results = cursor.fetchall()
        if results:
            print(f"   ✅ '{term}' → {results[0][1]}")
        else:
            print(f"   ❌ '{term}' 没有找到映射")
    
    conn.close()

if __name__ == "__main__":
    test_sql_validation()
    test_field_mappings()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")
    print("\n💡 建议:")
    print("1. 重启后端服务以确保所有修复生效")
    print("2. 使用相同的查询再次测试")
    print("3. 观察是否还会出现禁止字段的错误")
