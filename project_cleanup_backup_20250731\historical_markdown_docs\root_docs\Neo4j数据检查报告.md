# Neo4j数据检查报告

**生成时间**: 2025-07-31 08:32:43

## 📊 总体数据统计

- **表总数**: 206
- **列总数**: 1833
- **关系总数**: 33
- **连接IDs**: [1, 2, 15, 3, 16, 6, 7, 8, 5, 9]

## 🔗 按连接分组统计

### 连接ID 1
- 表数量: 17
- 列数量: 238
- 关系数量: 14

### 连接ID 2
- 表数量: 6
- 列数量: 42
- 关系数量: 3

### 连接ID 3
- 表数量: 4
- 列数量: 111
- 关系数量: 3

### 连接ID 5
- 表数量: 43
- 列数量: 387
- 关系数量: 0

### 连接ID 6
- 表数量: 16
- 列数量: 89
- 关系数量: 0

### 连接ID 7
- 表数量: 40
- 列数量: 352
- 关系数量: 0

### 连接ID 8
- 表数量: 46
- 列数量: 400
- 关系数量: 0

### 连接ID 9
- 表数量: 20
- 列数量: 138
- 关系数量: 0

### 连接ID 15
- 表数量: 11
- 列数量: 64
- 关系数量: 11

### 连接ID 16
- 表数量: 3
- 列数量: 12
- 关系数量: 2

## 📋 连接ID 1 详细表结构

### 表: aerich
- **表ID**: 1
- **描述**: Auto-discovered table: aerich

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| app | VARCHAR(100) | ❌ | ❌ | Auto-discovered column: app |
| content | JSON | ❌ | ❌ | Auto-discovered column: content |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| version | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: version |

**列总数**: 4

---

### 表: defects
- **表ID**: 2
- **描述**: Auto-discovered table: defects

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| actual_result | LONGTEXT | ❌ | ❌ | Auto-discovered column: actual_result |
| assignee_id | INTEGER | ✅ | ✅ | Auto-discovered column: assignee_id |
| attachments | JSON | ❌ | ❌ | Auto-discovered column: attachments |
| browser | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: browser |
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| defect_type | VARCHAR(13) | ❌ | ❌ | Auto-discovered column: defect_type |
| description | LONGTEXT | ❌ | ❌ | Auto-discovered column: description |
| environment | VARCHAR(100) | ❌ | ❌ | Auto-discovered column: environment |
| expected_result | LONGTEXT | ❌ | ❌ | Auto-discovered column: expected_result |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| priority | VARCHAR(6) | ❌ | ❌ | Auto-discovered column: priority |
| project_id | INTEGER | ✅ | ✅ | Auto-discovered column: project_id |
| reporter_id | INTEGER | ✅ | ✅ | Auto-discovered column: reporter_id |
| resolution | LONGTEXT | ❌ | ❌ | Auto-discovered column: resolution |
| resolution_date | DATETIME | ❌ | ❌ | Auto-discovered column: resolution_date |
| severity | VARCHAR(8) | ❌ | ❌ | Auto-discovered column: severity |
| status | VARCHAR(11) | ❌ | ❌ | Auto-discovered column: status |
| steps_to_reproduce | LONGTEXT | ❌ | ❌ | Auto-discovered column: steps_to_reproduce |
| test_case_id | INTEGER | ✅ | ✅ | Auto-discovered column: test_case_id |
| test_plan_id | INTEGER | ✅ | ✅ | Auto-discovered column: test_plan_id |
| title | VARCHAR(200) | ❌ | ❌ | Auto-discovered column: title |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |
| version | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: version |

**列总数**: 23

---

### 表: demands
- **表ID**: 3
- **描述**: Auto-discovered table: demands

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| actual_hours | DOUBLE | ❌ | ❌ | Auto-discovered column: actual_hours |
| ai_analysis_result | JSON | ❌ | ❌ | Auto-discovered column: ai_analysis_result |
| ai_analyzed | TINYINT | ❌ | ❌ | Auto-discovered column: ai_analyzed |
| assignee_id | INTEGER | ✅ | ✅ | Auto-discovered column: assignee_id |
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| creator_id | INTEGER | ✅ | ✅ | Auto-discovered column: creator_id |
| demand_name | VARCHAR(200) | ❌ | ❌ | Auto-discovered column: demand_name |
| demand_type | VARCHAR(14) | ❌ | ❌ | Auto-discovered column: demand_type |
| description | LONGTEXT | ❌ | ❌ | Auto-discovered column: description |
| document_name | VARCHAR(200) | ❌ | ❌ | Auto-discovered column: document_name |
| document_path | VARCHAR(500) | ❌ | ❌ | Auto-discovered column: document_path |
| estimated_hours | DOUBLE | ❌ | ❌ | Auto-discovered column: estimated_hours |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| link_url | VARCHAR(500) | ❌ | ❌ | Auto-discovered column: link_url |
| priority | VARCHAR(6) | ❌ | ❌ | Auto-discovered column: priority |
| project_id | INTEGER | ✅ | ✅ | Auto-discovered column: project_id |
| status | VARCHAR(10) | ❌ | ❌ | Auto-discovered column: status |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |
| version | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: version |

**列总数**: 19

---

### 表: interfaces
- **表ID**: 4
- **描述**: Auto-discovered table: interfaces

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| ai_tested | TINYINT | ❌ | ❌ | Auto-discovered column: ai_tested |
| body | LONGTEXT | ❌ | ❌ | Auto-discovered column: body |
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| creator_id | INTEGER | ✅ | ✅ | Auto-discovered column: creator_id |
| description | LONGTEXT | ❌ | ❌ | Auto-discovered column: description |
| headers | JSON | ❌ | ❌ | Auto-discovered column: headers |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| interface_name | VARCHAR(200) | ❌ | ❌ | Auto-discovered column: interface_name |
| last_test_time | DATETIME | ❌ | ❌ | Auto-discovered column: last_test_time |
| method | VARCHAR(7) | ❌ | ❌ | Auto-discovered column: method |
| project_id | INTEGER | ✅ | ✅ | Auto-discovered column: project_id |
| response_example | LONGTEXT | ❌ | ❌ | Auto-discovered column: response_example |
| status | VARCHAR(10) | ❌ | ❌ | Auto-discovered column: status |
| test_result | JSON | ❌ | ❌ | Auto-discovered column: test_result |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |
| url | VARCHAR(500) | ❌ | ❌ | Auto-discovered column: url |

**列总数**: 16

---

### 表: page_analysis_results
- **表ID**: 5
- **描述**: Auto-discovered table: page_analysis_results

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| analysis_id | CHAR(36) | ❌ | ❌ | Auto-discovered column: analysis_id |
| analysis_metadata | JSON | ❌ | ❌ | Auto-discovered column: analysis_metadata |
| analysis_summary | LONGTEXT | ❌ | ❌ | Auto-discovered column: analysis_summary |
| confidence_score | DECIMAL(5, 2) | ❌ | ❌ | Auto-discovered column: confidence_score |
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| elements_count | INTEGER | ❌ | ❌ | Auto-discovered column: elements_count |
| id | CHAR(36) | ❌ | ❌ | Auto-discovered column: id |
| page_description | LONGTEXT | ❌ | ❌ | Auto-discovered column: page_description |
| page_name | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: page_name |
| page_type | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: page_type |
| page_url | VARCHAR(1000) | ❌ | ❌ | Auto-discovered column: page_url |
| parsed_ui_elements | JSON | ❌ | ❌ | Auto-discovered column: parsed_ui_elements |
| processing_time | DECIMAL(10, 3) | ❌ | ❌ | Auto-discovered column: processing_time |
| raw_analysis_json | JSON | ❌ | ❌ | Auto-discovered column: raw_analysis_json |
| screenshot_urls | JSON | ❌ | ❌ | Auto-discovered column: screenshot_urls |
| session_id | VARCHAR(100) | ❌ | ❌ | Auto-discovered column: session_id |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |

**列总数**: 17

---

### 表: page_elements
- **表ID**: 6
- **描述**: Auto-discovered table: page_elements

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| confidence_score | DECIMAL(5, 2) | ❌ | ❌ | Auto-discovered column: confidence_score |
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| element_data | JSON | ❌ | ❌ | Auto-discovered column: element_data |
| element_description | LONGTEXT | ❌ | ❌ | Auto-discovered column: element_description |
| element_name | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: element_name |
| element_type | VARCHAR(100) | ❌ | ❌ | Auto-discovered column: element_type |
| id | CHAR(36) | ❌ | ❌ | Auto-discovered column: id |
| is_testable | TINYINT | ❌ | ❌ | Auto-discovered column: is_testable |
| page_analysis_id | CHAR(36) | ❌ | ✅ | Auto-discovered column: page_analysis_id |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |

**列总数**: 10

---

### 表: projects
- **表ID**: 7
- **描述**: Auto-discovered table: projects

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| description | LONGTEXT | ❌ | ❌ | Auto-discovered column: description |
| end_date | DATE | ❌ | ❌ | Auto-discovered column: end_date |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| owner_id | INTEGER | ✅ | ✅ | Auto-discovered column: owner_id |
| project_name | VARCHAR(200) | ❌ | ❌ | Auto-discovered column: project_name |
| start_date | DATE | ❌ | ❌ | Auto-discovered column: start_date |
| status | VARCHAR(9) | ❌ | ❌ | Auto-discovered column: status |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |

**列总数**: 9

---

### 表: script_executions
- **表ID**: 8
- **描述**: Auto-discovered table: script_executions

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| duration_seconds | INTEGER | ❌ | ❌ | Auto-discovered column: duration_seconds |
| end_time | DATETIME | ❌ | ❌ | Auto-discovered column: end_time |
| environment_info | JSON | ❌ | ❌ | Auto-discovered column: environment_info |
| error_message | LONGTEXT | ❌ | ❌ | Auto-discovered column: error_message |
| execution_config | JSON | ❌ | ❌ | Auto-discovered column: execution_config |
| execution_id | VARCHAR(100) | ❌ | ❌ | Auto-discovered column: execution_id |
| exit_code | INTEGER | ❌ | ❌ | Auto-discovered column: exit_code |
| id | CHAR(36) | ❌ | ❌ | Auto-discovered column: id |
| logs | LONGTEXT | ❌ | ❌ | Auto-discovered column: logs |
| script_id | CHAR(36) | ❌ | ✅ | Auto-discovered column: script_id |
| start_time | DATETIME | ❌ | ❌ | Auto-discovered column: start_time |
| status | VARCHAR(9) | ❌ | ❌ | Auto-discovered column: status |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |

**列总数**: 14

---

### 表: script_relationships
- **表ID**: 9
- **描述**: Auto-discovered table: script_relationships

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| relationship_type | VARCHAR(12) | ❌ | ❌ | Auto-discovered column: relationship_type |
| source_script_id | CHAR(36) | ❌ | ✅ | Auto-discovered column: source_script_id |
| target_script_id | CHAR(36) | ❌ | ✅ | Auto-discovered column: target_script_id |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |

**列总数**: 6

---

### 表: script_tags
- **表ID**: 10
- **描述**: Auto-discovered table: script_tags

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| script_id | CHAR(36) | ❌ | ✅ | Auto-discovered column: script_id |
| tag_name | VARCHAR(100) | ❌ | ❌ | Auto-discovered column: tag_name |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |

**列总数**: 5

---

### 表: sessions
- **表ID**: 11
- **描述**: Auto-discovered table: sessions

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| agent_type | VARCHAR(100) | ❌ | ❌ | Auto-discovered column: agent_type |
| analysis_result | JSON | ❌ | ❌ | Auto-discovered column: analysis_result |
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| duration_seconds | INTEGER | ❌ | ❌ | Auto-discovered column: duration_seconds |
| error_message | LONGTEXT | ❌ | ❌ | Auto-discovered column: error_message |
| id | CHAR(36) | ❌ | ❌ | Auto-discovered column: id |
| platform | VARCHAR(7) | ❌ | ❌ | Auto-discovered column: platform |
| request_data | JSON | ❌ | ❌ | Auto-discovered column: request_data |
| session_type | VARCHAR(14) | ❌ | ❌ | Auto-discovered column: session_type |
| status | VARCHAR(10) | ❌ | ❌ | Auto-discovered column: status |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |

**列总数**: 11

---

### 表: test_cases
- **表ID**: 12
- **描述**: Auto-discovered table: test_cases

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| ai_generated | TINYINT | ❌ | ❌ | Auto-discovered column: ai_generated |
| ai_generation_prompt | LONGTEXT | ❌ | ❌ | Auto-discovered column: ai_generation_prompt |
| assignee_id | INTEGER | ✅ | ✅ | Auto-discovered column: assignee_id |
| case_name | VARCHAR(200) | ❌ | ❌ | Auto-discovered column: case_name |
| case_type | VARCHAR(13) | ❌ | ❌ | Auto-discovered column: case_type |
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| creator_id | INTEGER | ✅ | ✅ | Auto-discovered column: creator_id |
| demand_id | INTEGER | ✅ | ✅ | Auto-discovered column: demand_id |
| description | LONGTEXT | ❌ | ❌ | Auto-discovered column: description |
| estimated_time | INTEGER | ❌ | ❌ | Auto-discovered column: estimated_time |
| expected_result | LONGTEXT | ❌ | ❌ | Auto-discovered column: expected_result |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| preconditions | LONGTEXT | ❌ | ❌ | Auto-discovered column: preconditions |
| priority | VARCHAR(8) | ❌ | ❌ | Auto-discovered column: priority |
| status | VARCHAR(9) | ❌ | ❌ | Auto-discovered column: status |
| steps | JSON | ❌ | ❌ | Auto-discovered column: steps |
| tags | JSON | ❌ | ❌ | Auto-discovered column: tags |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |

**列总数**: 18

---

### 表: test_executions
- **表ID**: 13
- **描述**: Auto-discovered table: test_executions

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| actual_result | LONGTEXT | ❌ | ❌ | Auto-discovered column: actual_result |
| browser | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: browser |
| environment | VARCHAR(100) | ❌ | ❌ | Auto-discovered column: environment |
| executed_at | DATETIME | ❌ | ❌ | Auto-discovered column: executed_at |
| execution_steps | JSON | ❌ | ❌ | Auto-discovered column: execution_steps |
| execution_time | INTEGER | ❌ | ❌ | Auto-discovered column: execution_time |
| executor_id | INTEGER | ✅ | ✅ | Auto-discovered column: executor_id |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| notes | LONGTEXT | ❌ | ❌ | Auto-discovered column: notes |
| result | VARCHAR(7) | ❌ | ❌ | Auto-discovered column: result |
| screenshots | JSON | ❌ | ❌ | Auto-discovered column: screenshots |
| test_case_id | INTEGER | ✅ | ✅ | Auto-discovered column: test_case_id |
| test_plan_id | INTEGER | ✅ | ✅ | Auto-discovered column: test_plan_id |
| version | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: version |

**列总数**: 14

---

### 表: test_plans
- **表ID**: 14
- **描述**: Auto-discovered table: test_plans

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| creator_id | INTEGER | ✅ | ✅ | Auto-discovered column: creator_id |
| description | LONGTEXT | ❌ | ❌ | Auto-discovered column: description |
| end_date | DATE | ❌ | ❌ | Auto-discovered column: end_date |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| pass_rate | DOUBLE | ❌ | ❌ | Auto-discovered column: pass_rate |
| plan_name | VARCHAR(200) | ❌ | ❌ | Auto-discovered column: plan_name |
| progress | DOUBLE | ❌ | ❌ | Auto-discovered column: progress |
| project_id | INTEGER | ✅ | ✅ | Auto-discovered column: project_id |
| start_date | DATE | ❌ | ❌ | Auto-discovered column: start_date |
| status | VARCHAR(9) | ❌ | ❌ | Auto-discovered column: status |
| test_cases | JSON | ❌ | ❌ | Auto-discovered column: test_cases |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |

**列总数**: 13

---

### 表: test_reports
- **表ID**: 15
- **描述**: Auto-discovered table: test_reports

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| artifacts | JSON | ❌ | ❌ | Auto-discovered column: artifacts |
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| duration | DOUBLE | ❌ | ❌ | Auto-discovered column: duration |
| end_time | DATETIME | ❌ | ❌ | Auto-discovered column: end_time |
| environment_variables | JSON | ❌ | ❌ | Auto-discovered column: environment_variables |
| error_message | LONGTEXT | ❌ | ❌ | Auto-discovered column: error_message |
| execution_config | JSON | ❌ | ❌ | Auto-discovered column: execution_config |
| execution_id | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: execution_id |
| failed_tests | INTEGER | ❌ | ❌ | Auto-discovered column: failed_tests |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| logs | JSON | ❌ | ❌ | Auto-discovered column: logs |
| passed_tests | INTEGER | ❌ | ❌ | Auto-discovered column: passed_tests |
| report_path | LONGTEXT | ❌ | ❌ | Auto-discovered column: report_path |
| report_size | INTEGER | ❌ | ❌ | Auto-discovered column: report_size |
| report_url | LONGTEXT | ❌ | ❌ | Auto-discovered column: report_url |
| return_code | INTEGER | ❌ | ❌ | Auto-discovered column: return_code |
| screenshots | JSON | ❌ | ❌ | Auto-discovered column: screenshots |
| script_id | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: script_id |
| script_name | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: script_name |
| session_id | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: session_id |
| skipped_tests | INTEGER | ❌ | ❌ | Auto-discovered column: skipped_tests |
| start_time | DATETIME | ❌ | ❌ | Auto-discovered column: start_time |
| status | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: status |
| success_rate | DOUBLE | ❌ | ❌ | Auto-discovered column: success_rate |
| total_tests | INTEGER | ❌ | ❌ | Auto-discovered column: total_tests |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |
| videos | JSON | ❌ | ❌ | Auto-discovered column: videos |

**列总数**: 27

---

### 表: test_scripts
- **表ID**: 16
- **描述**: Auto-discovered table: test_scripts

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| additional_context | LONGTEXT | ❌ | ❌ | Auto-discovered column: additional_context |
| analysis_result_id | VARCHAR(36) | ❌ | ❌ | Auto-discovered column: analysis_result_id |
| category | VARCHAR(100) | ❌ | ❌ | Auto-discovered column: category |
| content | LONGTEXT | ❌ | ❌ | Auto-discovered column: content |
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| description | LONGTEXT | ❌ | ❌ | Auto-discovered column: description |
| execution_count | INTEGER | ❌ | ❌ | Auto-discovered column: execution_count |
| file_path | VARCHAR(500) | ❌ | ❌ | Auto-discovered column: file_path |
| id | CHAR(36) | ❌ | ❌ | Auto-discovered column: id |
| last_execution_status | VARCHAR(9) | ❌ | ❌ | Auto-discovered column: last_execution_status |
| last_execution_time | DATETIME | ❌ | ❌ | Auto-discovered column: last_execution_time |
| name | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: name |
| priority | INTEGER | ❌ | ❌ | Auto-discovered column: priority |
| project_id | VARCHAR(36) | ❌ | ❌ | Auto-discovered column: project_id |
| script_format | VARCHAR(10) | ❌ | ❌ | Auto-discovered column: script_format |
| script_type | VARCHAR(15) | ❌ | ❌ | Auto-discovered column: script_type |
| session_id | VARCHAR(36) | ❌ | ❌ | Auto-discovered column: session_id |
| test_case_content | LONGTEXT | ❌ | ❌ | Auto-discovered column: test_case_content |
| test_description | LONGTEXT | ❌ | ❌ | Auto-discovered column: test_description |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |

**列总数**: 20

---

### 表: users
- **表ID**: 17
- **描述**: Auto-discovered table: users

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| avatar | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: avatar |
| created_at | DATETIME | ❌ | ❌ | Auto-discovered column: created_at |
| email | VARCHAR(100) | ❌ | ❌ | Auto-discovered column: email |
| full_name | VARCHAR(100) | ❌ | ❌ | Auto-discovered column: full_name |
| hashed_password | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: hashed_password |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| is_active | TINYINT | ❌ | ❌ | Auto-discovered column: is_active |
| is_superuser | TINYINT | ❌ | ❌ | Auto-discovered column: is_superuser |
| last_login | DATETIME | ❌ | ❌ | Auto-discovered column: last_login |
| role | VARCHAR(9) | ❌ | ❌ | Auto-discovered column: role |
| updated_at | DATETIME | ❌ | ❌ | Auto-discovered column: updated_at |
| username | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: username |

**列总数**: 12

---

## 📋 连接ID 2 详细表结构

### 表: conversation
- **表ID**: 85
- **描述**: Auto-discovered table: conversation

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| chat_type | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: chat_type |
| create_time | DATETIME | ❌ | ❌ | Auto-discovered column: create_time |
| id | CHAR(36) | ❌ | ❌ | Auto-discovered column: id |
| name | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: name |
| user_id | VARCHAR(32) | ❌ | ✅ | Auto-discovered column: user_id |

**列总数**: 5

---

### 表: file_doc
- **表ID**: 86
- **描述**: Auto-discovered table: file_doc

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| doc_id | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: doc_id |
| file_name | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: file_name |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| kb_name | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: kb_name |
| meta_data | LONGTEXT | ❌ | ❌ | Auto-discovered column: meta_data |

**列总数**: 5

---

### 表: knowledge_base
- **表ID**: 87
- **描述**: Auto-discovered table: knowledge_base

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| create_time | DATETIME | ❌ | ❌ | Auto-discovered column: create_time |
| embed_model | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: embed_model |
| file_count | INTEGER | ❌ | ❌ | Auto-discovered column: file_count |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| kb_info | VARCHAR(200) | ❌ | ❌ | Auto-discovered column: kb_info |
| kb_name | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: kb_name |
| user_id | VARCHAR(32) | ❌ | ✅ | Auto-discovered column: user_id |
| vs_type | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: vs_type |

**列总数**: 8

---

### 表: knowledge_file
- **表ID**: 88
- **描述**: Auto-discovered table: knowledge_file

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| create_time | DATETIME | ❌ | ❌ | Auto-discovered column: create_time |
| custom_docs | TINYINT | ❌ | ❌ | Auto-discovered column: custom_docs |
| docs_count | INTEGER | ❌ | ❌ | Auto-discovered column: docs_count |
| document_loader_name | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: document_loader_name |
| file_ext | VARCHAR(10) | ❌ | ❌ | Auto-discovered column: file_ext |
| file_mtime | FLOAT | ❌ | ❌ | Auto-discovered column: file_mtime |
| file_name | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: file_name |
| file_size | INTEGER | ❌ | ❌ | Auto-discovered column: file_size |
| file_version | INTEGER | ❌ | ❌ | Auto-discovered column: file_version |
| id | INTEGER | ✅ | ❌ | Auto-discovered column: id |
| kb_name | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: kb_name |
| text_splitter_name | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: text_splitter_name |

**列总数**: 12

---

### 表: message
- **表ID**: 89
- **描述**: Auto-discovered table: message

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| chat_type | VARCHAR(50) | ❌ | ❌ | Auto-discovered column: chat_type |
| conversation_id | CHAR(36) | ❌ | ✅ | Auto-discovered column: conversation_id |
| create_time | DATETIME | ❌ | ❌ | Auto-discovered column: create_time |
| feedback_reason | VARCHAR(255) | ❌ | ❌ | Auto-discovered column: feedback_reason |
| feedback_score | INTEGER | ❌ | ❌ | Auto-discovered column: feedback_score |
| id | CHAR(36) | ❌ | ❌ | Auto-discovered column: id |
| meta_data | LONGTEXT | ❌ | ❌ | Auto-discovered column: meta_data |
| query | VARCHAR(4096) | ❌ | ❌ | Auto-discovered column: query |
| response | VARCHAR(4096) | ❌ | ❌ | Auto-discovered column: response |

**列总数**: 9

---

### 表: user
- **表ID**: 90
- **描述**: Auto-discovered table: user

#### 列信息

| 列名 | 数据类型 | 主键 | 外键 | 描述 |
|------|----------|------|------|------|
| id | VARCHAR(32) | ❌ | ❌ | Auto-discovered column: id |
| password_hash | VARCHAR(110) | ❌ | ❌ | Auto-discovered column: password_hash |
| username | VARCHAR(110) | ❌ | ❌ | Auto-discovered column: username |

**列总数**: 3

---

## 🔗 表关系信息

### 连接ID 1 的表关系

| 源表 | 源列 | 目标表 | 目标列 | 关系类型 |
|------|------|--------|--------|----------|
| defects | project_id | projects | id | REFERENCES |
| defects | test_case_id | test_cases | id | REFERENCES |
| defects | test_plan_id | test_plans | id | REFERENCES |
| demands | project_id | projects | id | REFERENCES |
| interfaces | project_id | projects | id | REFERENCES |
| page_elements | page_analysis_id | page_analysis_results | id | REFERENCES |
| script_executions | script_id | test_scripts | id | REFERENCES |
| script_relationships | source_script_id | test_scripts | id | REFERENCES |
| script_relationships | target_script_id | test_scripts | id | REFERENCES |
| script_tags | script_id | test_scripts | id | REFERENCES |
| test_cases | demand_id | demands | id | REFERENCES |
| test_executions | test_case_id | test_cases | id | REFERENCES |
| test_executions | test_plan_id | test_plans | id | REFERENCES |
| test_plans | project_id | projects | id | REFERENCES |

**关系总数**: 14

### 连接ID 2 的表关系

| 源表 | 源列 | 目标表 | 目标列 | 关系类型 |
|------|------|--------|--------|----------|
| conversation | user_id | user | id | REFERENCES |
| knowledge_base | user_id | user | id | REFERENCES |
| message | conversation_id | conversation | id | REFERENCES |

**关系总数**: 3

## ✅ 数据一致性检查

### 连接ID 1 一致性检查

| 数据类型 | SQLite | Neo4j | 状态 |
|----------|--------|-------|------|
| 表数量 | 10 | 17 | ❌ 不一致 |
| 列数量 | 101 | 238 | ❌ 不一致 |
| 关系数量 | 0 | 14 | ❌ 不一致 |

### 连接ID 2 一致性检查

| 数据类型 | SQLite | Neo4j | 状态 |
|----------|--------|-------|------|
| 表数量 | 13 | 6 | ❌ 不一致 |
| 列数量 | 104 | 42 | ❌ 不一致 |
| 关系数量 | 11 | 3 | ❌ 不一致 |

## 💡 建议和注意事项

1. **定期同步检查**: 建议定期运行数据同步检查，确保SQLite和Neo4j数据一致
2. **缓存管理**: 当数据结构发生变化时，及时清理缓存
3. **性能监控**: 关注Schema检索的性能，特别是大表的查询效率
4. **数据验证**: 定期验证表结构数据的准确性和完整性

---

*此报告由Neo4j数据检查工具自动生成*