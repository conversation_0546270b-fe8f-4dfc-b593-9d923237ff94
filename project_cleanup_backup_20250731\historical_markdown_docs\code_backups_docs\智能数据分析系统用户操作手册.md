# 智能数据分析系统用户操作手册

## 📚 目录

1. [系统概述](#系统概述)
2. [安装部署指南](#安装部署指南)
3. [快速开始](#快速开始)
4. [核心功能详解](#核心功能详解)
   - [4.1 智能查询 (Text2SQL)](#41-智能查询-text2sql)
   - [4.2 智能问答 (HybridQA)](#42-智能问答-hybridqa)
   - [4.3 数据建模 (Schema Management)](#43-数据建模-schema-management)
   - [4.4 图数据可视化](#44-图数据可视化)
   - [4.5 连接管理](#45-连接管理)
   - [4.6 数据映射](#46-数据映射)
5. [用户角色指南](#用户角色指南)
6. [业务场景示例](#业务场景示例)
7. [API接口文档](#api接口文档)
8. [性能优化指南](#性能优化指南)
9. [安全与权限管理](#安全与权限管理)
10. [常见问题解答](#常见问题解答)
11. [故障排除指南](#故障排除指南)
12. [最佳实践建议](#最佳实践建议)

---

## 系统概述

### 🎯 产品定位
智能数据分析系统是一个基于Text2SQL技术的企业级数据分析平台，专门为财务数据分析场景设计。系统集成了大语言模型(LLM)、图数据库(Neo4j)、向量数据库(Milvus)等先进技术，为用户提供自然语言查询、智能问答、数据建模等全方位的数据分析能力。

### 🏗️ 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   数据存储      │
│                 │    │                 │    │                 │
│ React + TS      │◄──►│ FastAPI + Python│◄──►│ SQLite/MySQL    │
│ Ant Design      │    │ SQLAlchemy      │    │ Neo4j + Milvus  │
│ React Flow      │    │ Pydantic        │    │ Redis Cache     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 🔧 技术栈
- **前端**: React 18 + TypeScript + Ant Design + React Flow
- **后端**: Python FastAPI + SQLAlchemy + Pydantic
- **数据库**: SQLite/MySQL/PostgreSQL + Neo4j + Milvus + Redis
- **AI集成**: DeepSeek/OpenAI + 向量搜索 + 混合检索
- **部署**: Docker + Docker Compose

### 🎨 核心特性
- ✅ **零SQL门槛**: 自然语言直接查询数据
- ✅ **智能推荐**: 基于历史学习的查询建议  
- ✅ **可视化建模**: 直观的数据结构管理
- ✅ **多数据源支持**: 统一的数据访问接口
- ✅ **企业级特性**: 缓存、连接池、性能优化
- ✅ **中文优化**: 专门针对中文自然语言查询优化
- ✅ **财务专业**: 内置财务数据分析的专业知识

---

## 安装部署指南

### 📋 环境要求

#### 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **内存**: 最低 8GB，推荐 16GB
- **存储**: 最低 10GB 可用空间
- **网络**: 稳定的互联网连接（用于AI模型调用）

#### 软件依赖
- **Python**: 3.8+ (推荐 3.11)
- **Node.js**: 16+ (推荐 18 LTS)
- **Docker**: 20.10+ (可选，用于容器化部署)
- **Git**: 2.30+ (用于代码管理)

### 🚀 快速安装

#### 方法一：Docker 部署（推荐）

1. **克隆项目**
```bash
git clone https://github.com/your-repo/智能数据分析系统.git
cd 智能数据分析系统
```

2. **配置环境变量**
```bash
# 复制环境配置模板
cp chatdb/.env.example chatdb/.env

# 编辑配置文件
nano chatdb/.env
```

3. **启动服务**
```bash
cd chatdb
docker-compose up -d
```

4. **初始化数据库**
```bash
docker-compose exec backend python init_db.py
```

5. **访问系统**
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000/docs
- Neo4j浏览器: http://localhost:7474

#### 方法二：本地开发部署

1. **后端部署**
```bash
cd chatdb/backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python init_db.py

# 启动服务
python main.py
```

2. **前端部署**
```bash
cd chatdb/frontend

# 安装依赖
npm install

# 启动开发服务器
npm start
```

### ⚙️ 配置说明

#### 环境变量配置 (.env)
```bash
# 数据库配置
DATABASE_TYPE=sqlite
SQLITE_DB_PATH=resource.db
BUSINESS_DB_PATH=fin_data.db

# LLM配置
OPENAI_API_KEY=your_api_key_here
OPENAI_API_BASE=https://api.deepseek.com/v1
LLM_MODEL=deepseek-chat
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=2000

# Neo4j配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# Redis配置（可选）
REDIS_URL=redis://localhost:6379

# 功能开关
ENABLE_METADATA_ENHANCEMENT=true
ENABLE_HYBRID_RETRIEVAL=true
ENABLE_CACHE=true
```

### 🔍 安装验证

#### 健康检查
```bash
# 检查后端服务
curl http://localhost:8000/health

# 检查前端服务
curl http://localhost:3000

# 检查数据库连接
python -c "
from chatdb.backend.app.db.session import SessionLocal
db = SessionLocal()
print('数据库连接成功')
db.close()
"
```

#### 功能测试
```bash
# 运行集成测试
python simple_integration_test.py

# 测试API接口
curl -X POST "http://localhost:8000/api/query/" \
  -H "Content-Type: application/json" \
  -d '{"connection_id": 1, "natural_language_query": "查询收入情况"}'
```

---

## 快速开始

### 🎯 5分钟快速体验

#### 步骤1：创建数据库连接
1. 访问 http://localhost:3000
2. 点击顶部菜单 "连接管理"
3. 点击 "添加连接" 按钮
4. 填写连接信息：
   - 连接名称: `财务数据库`
   - 数据库类型: `SQLite`
   - 数据库路径: `fin_data.db`
5. 点击 "测试连接" 确认连接正常
6. 点击 "保存" 完成创建

#### 步骤2：发现数据结构
1. 在连接列表中找到刚创建的连接
2. 点击 "发现结构" 按钮
3. 系统自动分析数据库结构
4. 等待结构发现完成

#### 步骤3：执行智能查询
1. 点击顶部菜单 "智能查询"
2. 在连接选择器中选择 "财务数据库"
3. 在输入框中输入：`查询2024年9月的收入情况`
4. 点击发送按钮
5. 观察系统实时生成SQL并返回结果

#### 步骤4：查看数据可视化
1. 点击顶部菜单 "图数据可视化"
2. 选择数据库连接
3. 查看数据库结构的图形化展示
4. 使用鼠标拖拽和缩放探索数据关系

### 📊 示例查询
以下是一些可以立即尝试的查询示例：

```
# 收入分析
查询2024年各月的主营业务收入趋势

# 费用分析  
分析管理费用的构成情况

# 资产分析
显示流动资产和非流动资产的分布

# 对比分析
比较2023年和2024年同期的收入情况

# 部门分析
查询各部门的费用支出排名
```

---

## 核心功能详解

### 4.1 智能查询 (Text2SQL)

#### 🎯 功能概述
智能查询是系统的核心功能，能够将用户的中文自然语言查询转换为标准SQL语句并执行。系统集成了增强提示服务，专门针对财务数据分析场景进行了优化。

#### 🖥️ 界面操作指南

##### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 财务智能分析系统                                            │
├─────────────────────────────────────────────────────────────┤
│ [智能查询] [智能问答] [数据建模] [图数据可视化] [连接管理] [数据映射] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  连接选择: [财务数据库 ▼]                    [智能推荐 💡]    │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ 向智能助手提问                                          │ │
│  │ 例如：查询2024年9月的主营业务收入情况                   │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                              [发送] [清空]   │
│                                                             │
│  ┌─── 查询分析 ────┬─── SQL生成 ────┬─── 查询结果 ────┐    │
│  │                 │                │                 │    │
│  │ 正在分析查询... │ 生成SQL中...   │ 执行查询中...   │    │
│  │                 │                │                 │    │
│  └─────────────────┴────────────────┴─────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

##### 操作步骤详解

**步骤1：选择数据库连接**
1. 点击页面顶部的连接选择下拉框
2. 从列表中选择要查询的数据库连接
3. 系统会自动加载该连接的元数据信息

**步骤2：输入自然语言查询**
1. 在输入框中输入您的问题
2. 支持的查询类型：
   - 收入分析：`查询2024年9月的主营业务收入`
   - 费用分析：`分析管理费用的支出情况`
   - 资产分析：`显示流动资产余额`
   - 对比分析：`比较今年和去年的收入`
   - 趋势分析：`查看收入的月度变化趋势`

**步骤3：启用智能推荐（可选）**
1. 点击输入框右侧的智能推荐按钮 💡
2. 系统会基于历史查询提供相似问题建议
3. 点击建议问题可直接填入输入框

**步骤4：发送查询**
1. 点击"发送"按钮或按Enter键
2. 系统开始实时流式处理：
   - **查询分析阶段**：理解查询意图，识别关键实体
   - **SQL生成阶段**：基于元数据生成优化的SQL语句
   - **查询执行阶段**：执行SQL并返回结果
   - **结果解释阶段**：提供查询结果的业务解释

#### 📊 输出结果详解

##### 查询分析区域
显示系统对用户查询的理解：
```
🔍 查询分析结果：
- 查询类型：收入分析
- 时间范围：2024年9月
- 涉及科目：主营业务收入 (60xx)
- 分组维度：无
- 聚合方式：求和
```

##### SQL生成区域
显示系统生成的SQL语句：
```sql
-- 生成的SQL查询
SELECT
    SUM(credit_amount) as 主营业务收入
FROM financial_data
WHERE account_code LIKE '60%'
    AND year = 2024
    AND month = 9
    AND credit_amount > 0
```

##### 查询结果区域
以表格形式展示查询结果：
```
┌─────────────────┬─────────────────┐
│ 指标            │ 金额(元)        │
├─────────────────┼─────────────────┤
│ 主营业务收入    │ 1,250,000.00    │
└─────────────────┴─────────────────┘
```

##### 结果解释区域
提供业务层面的结果解释：
```
📈 查询结果解释：
根据财务数据分析，2024年9月的主营业务收入为125万元。
这个数据来源于科目编号60xx系列的贷方发生额汇总。
建议与去年同期进行对比分析，了解收入增长趋势。
```

#### ⚙️ 高级功能

##### 智能字段选择
系统会根据查询类型自动选择正确的字段：
- **收入查询** → 使用 `credit_amount` 字段
- **费用查询** → 使用 `debit_amount` 字段
- **余额查询** → 使用 `balance` 字段（自动类型转换）

##### 业务规则应用
系统内置财务专业知识：
- 自动应用科目分类规则（1xxx=资产，60xx=收入等）
- 强制数据类型转换（balance字段TEXT→REAL）
- 防止常见的财务逻辑错误

##### 查询优化
- 自动添加必要的过滤条件
- 优化JOIN操作减少查询时间
- 智能索引建议

#### 🚨 错误处理

##### 常见错误类型
1. **查询意图不明确**
   - 错误示例：`查询数据`
   - 解决方案：提供更具体的查询描述
   - 正确示例：`查询2024年9月的收入数据`

2. **时间范围缺失**
   - 错误示例：`查询收入情况`
   - 解决方案：指定具体的时间范围
   - 正确示例：`查询2024年第三季度的收入情况`

3. **科目类型混淆**
   - 错误示例：`查询收入的借方金额`
   - 解决方案：系统会自动纠正为贷方金额
   - 提示：收入类科目通常查看贷方发生额

##### 错误提示示例
```
❌ 查询处理失败
原因：无法识别查询中的时间范围
建议：请指定具体的年份、月份或季度
示例：查询2024年9月的收入情况
```

#### 💡 使用技巧

##### 查询优化技巧
1. **明确时间范围**：总是包含具体的时间信息
2. **使用标准术语**：使用财务专业术语提高识别准确性
3. **分步查询**：复杂分析可以分解为多个简单查询
4. **利用智能推荐**：查看系统推荐的相似查询

##### 高效查询模式
```
# 单维度分析
查询2024年各月的收入情况

# 多维度分析
按部门和月份统计2024年的费用支出

# 对比分析
比较2023年和2024年同期的利润情况

# 趋势分析
显示最近12个月的收入变化趋势

# 排名分析
查询收入最高的前10个科目
```

### 4.2 智能问答 (HybridQA)

#### 🎯 功能概述
智能问答功能基于混合检索技术，结合向量搜索、图数据库查询和模式匹配，为用户提供更精准的问答体验。系统能够学习用户的查询历史，提供个性化的查询建议。

#### 🖥️ 界面操作指南

##### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 智能问答管理                                                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ 连接选择: [财务数据库 ▼]     [统计信息] [导入] [导出]       │
│                                                             │
│ ┌─── 问答对管理 ────┬─── 智能搜索 ────┬─── 统计分析 ────┐  │
│ │                   │                 │                 │  │
│ │ [创建问答对]      │ 搜索框          │ 成功率: 95%     │  │
│ │                   │ [智能搜索]      │ 总查询: 1,234   │  │
│ │ 问答对列表        │                 │ 学习数量: 567   │  │
│ │ ┌─────────────┐   │ 搜索结果列表    │                 │  │
│ │ │问题: 查询...│   │ ┌─────────────┐ │ 热门查询类型    │  │
│ │ │SQL: SELECT..│   │ │相似度: 0.95 │ │ 1. 收入分析     │  │
│ │ │状态: ✅     │   │ │问题: ...    │ │ 2. 费用分析     │  │
│ │ └─────────────┘   │ │SQL: ...     │ │ 3. 资产分析     │  │
│ │                   │ └─────────────┘ │                 │  │
│ └───────────────────┴─────────────────┴─────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

##### 操作步骤详解

**步骤1：创建问答对**
1. 点击"创建问答对"按钮
2. 填写问答对信息：
   ```
   问题: 查询2024年第三季度的主营业务收入
   SQL: SELECT SUM(credit_amount) FROM financial_data
        WHERE account_code LIKE '60%'
        AND year = 2024 AND month BETWEEN 7 AND 9
   连接: 财务数据库
   难度: 中等
   查询类型: 收入分析
   ```
3. 点击"保存"完成创建

**步骤2：智能搜索相似问答**
1. 在搜索框中输入问题
2. 点击"智能搜索"按钮
3. 系统返回相似度排序的结果：
   ```
   🔍 搜索结果 (共找到3个相似问答)

   1. 相似度: 0.95 ⭐⭐⭐⭐⭐
      问题: 查询2024年第二季度的主营业务收入
      SQL: SELECT SUM(credit_amount) FROM financial_data...

   2. 相似度: 0.87 ⭐⭐⭐⭐
      问题: 统计2024年各季度收入情况
      SQL: SELECT quarter, SUM(credit_amount) FROM...
   ```

**步骤3：问答对管理**
1. 查看问答对列表
2. 支持的操作：
   - 📝 编辑：修改问题或SQL
   - 👁️ 查看：查看详细信息
   - 🗑️ 删除：删除问答对
   - ⭐ 评分：对问答质量评分
   - 🔄 验证：验证SQL正确性

#### 📊 混合检索技术

##### 检索方式说明
1. **语义检索**：基于向量相似度匹配
2. **结构检索**：基于数据库结构匹配
3. **模式检索**：基于查询模式匹配
4. **融合排序**：综合多种检索结果

##### 检索流程图
```
用户查询 → 向量化 → 并行检索 → 融合排序 → 返回结果
    ↓         ↓         ↓         ↓         ↓
  意图理解   语义检索   结构检索   模式检索   Top-K结果
```

#### 🎯 学习机制

##### 自动学习
系统会自动学习用户的查询模式：
1. 记录成功的查询
2. 分析查询特征
3. 更新推荐模型
4. 优化检索算法

##### 反馈机制
用户可以对查询结果进行反馈：
- 👍 有用：提升该问答对的权重
- 👎 无用：降低该问答对的权重
- 📝 建议：提供改进建议

#### 💡 使用技巧

##### 创建高质量问答对
1. **问题描述清晰**：使用具体、明确的描述
2. **SQL语句正确**：确保SQL语法正确且能执行
3. **添加标签**：为问答对添加适当的标签
4. **定期维护**：定期检查和更新问答对

##### 搜索优化技巧
1. **使用关键词**：在搜索中包含关键业务术语
2. **指定连接**：限定特定数据库连接的搜索范围
3. **设置阈值**：调整相似度阈值过滤结果
4. **多次尝试**：尝试不同的搜索表达方式

### 4.3 数据建模 (Schema Management)

#### 🎯 功能概述
数据建模功能提供可视化的数据库结构管理，支持表关系定义、元数据管理和结构同步。用户可以通过拖拽式界面直观地管理数据库结构，定义表间关系，并将结构信息同步到图数据库中。

#### 🖥️ 界面操作指南

##### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 数据建模                                                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ 连接: [财务数据库 ▼] [发现结构] [发布模式] [自动布局]       │
│                                                             │
│ ┌─── 表列表 ────┬─────────── 建模画布 ──────────────────┐   │
│ │               │                                       │   │
│ │ 📊 数据表     │  ┌─────────────┐    ┌─────────────┐   │   │
│ │               │  │financial_data│    │ departments │   │   │
│ │ ☐ financial_data│  │id          │────│id          │   │   │
│ │ ☐ departments │  │account_code │    │dept_name    │   │   │
│ │ ☐ employees   │  │account_name │    │manager_id   │   │   │
│ │ ☐ projects    │  │debit_amount │    └─────────────┘   │   │
│ │               │  │credit_amount│                      │   │
│ │ 🔗 关系       │  │balance      │    ┌─────────────┐   │   │
│ │               │  │year         │    │ employees   │   │   │
│ │ financial_data│  │month        │    │id          │   │   │
│ │ ──→ departments│  │dept_id      │────│name        │   │   │
│ │               │  └─────────────┘    │dept_id     │   │   │
│ │ employees     │                     │salary      │   │   │
│ │ ──→ departments│                     └─────────────┘   │   │
│ │               │                                       │   │
│ └───────────────┴───────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

##### 操作步骤详解

**步骤1：选择数据库连接**
1. 在页面顶部选择要建模的数据库连接
2. 系统会加载该连接的表结构信息

**步骤2：发现数据库结构**
1. 点击"发现结构"按钮
2. 系统自动分析数据库：
   ```
   🔍 正在发现数据库结构...
   ✅ 发现表: financial_data (31个字段)
   ✅ 发现表: departments (5个字段)
   ✅ 发现表: employees (8个字段)
   ✅ 检测关系: 3个外键关系
   ✅ 同步到Neo4j: 完成
   ```

**步骤3：拖拽表到画布**
1. 从左侧表列表中选择要建模的表
2. 拖拽表到右侧画布中
3. 表会以卡片形式显示，包含：
   - 表名
   - 主要字段列表
   - 主键标识 🔑
   - 外键标识 🔗

**步骤4：定义表关系**
1. 从源表的字段拖拽到目标表的字段
2. 系统会弹出关系配置对话框：
   ```
   ┌─── 定义表关系 ───┐
   │                  │
   │ 源表: financial_data │
   │ 源字段: dept_id      │
   │ 目标表: departments  │
   │ 目标字段: id         │
   │                  │
   │ 关系类型:        │
   │ ○ 一对一 (1:1)   │
   │ ● 多对一 (N:1)   │
   │ ○ 一对多 (1:N)   │
   │ ○ 多对多 (N:M)   │
   │                  │
   │ 关系描述:        │
   │ 财务数据属于部门 │
   │                  │
   │ [取消] [确定]    │
   └──────────────────┘
   ```

**步骤5：编辑表属性**
1. 双击表卡片打开编辑对话框
2. 可以编辑的属性：
   - 表名显示名称
   - 表描述信息
   - 字段显示名称
   - 字段描述信息
   - 字段数据类型
   - 主键/外键标识

**步骤6：发布模式**
1. 完成建模后点击"发布模式"
2. 系统会：
   - 验证模式完整性
   - 保存到元数据库
   - 同步到Neo4j图数据库
   - 更新缓存信息

#### 🎨 可视化功能

##### 自动布局算法
系统提供多种布局算法：
1. **层次布局**：按照数据流向分层排列
2. **力导向布局**：基于关系强度自动排列
3. **网格布局**：规整的网格排列
4. **圆形布局**：围绕中心表排列

##### 视觉元素说明
- **表卡片颜色**：
  - 🔵 蓝色：事实表（如financial_data）
  - 🟢 绿色：维度表（如departments）
  - 🟡 黄色：桥接表（如关联表）
- **关系线条**：
  - 实线：强关系（外键约束）
  - 虚线：弱关系（逻辑关系）
  - 箭头：关系方向

##### 交互功能
- **缩放**：鼠标滚轮缩放画布
- **平移**：拖拽空白区域平移视图
- **选择**：点击选择表或关系
- **多选**：Ctrl+点击多选对象
- **删除**：选中后按Delete键删除

#### 🔧 高级功能

##### 批量操作
1. **批量导入表**：
   ```
   选择多个表 → 右键菜单 → 批量添加到画布
   ```

2. **批量定义关系**：
   ```
   选择源表 → Shift+选择目标表 → 右键 → 快速关联
   ```

3. **批量编辑属性**：
   ```
   多选表 → 右键 → 批量编辑 → 统一设置属性
   ```

##### 模式验证
系统会自动验证模式的完整性：
- ✅ 检查孤立表（无关系的表）
- ✅ 检查循环依赖
- ✅ 检查关系类型合理性
- ✅ 检查字段类型匹配

##### 版本管理
支持模式的版本管理：
- 📝 保存模式快照
- 🔄 回滚到历史版本
- 📊 比较版本差异
- 📤 导出模式定义

#### 💾 数据同步

##### 同步到Neo4j
模式信息会自动同步到Neo4j图数据库：
```cypher
-- 创建表节点
CREATE (t:Table {
  id: 1,
  name: "financial_data",
  description: "财务数据表",
  connection_id: 1
})

-- 创建字段节点
CREATE (c:Column {
  id: 1,
  name: "account_code",
  type: "VARCHAR(20)",
  description: "科目编号"
})

-- 创建关系
CREATE (t)-[:HAS_COLUMN]->(c)
```

##### 同步状态监控
```
🔄 同步状态监控
├── 表同步: ✅ 5/5 完成
├── 字段同步: ✅ 31/31 完成
├── 关系同步: ✅ 3/3 完成
└── 索引同步: ⏳ 进行中...
```

#### 🚨 错误处理

##### 常见错误类型
1. **关系定义错误**
   ```
   ❌ 错误：尝试在不兼容的字段间创建关系
   原因：dept_id (INT) 与 dept_name (VARCHAR) 类型不匹配
   解决：选择正确的关联字段
   ```

2. **循环依赖错误**
   ```
   ❌ 错误：检测到循环依赖
   路径：A → B → C → A
   解决：重新设计表关系，消除循环
   ```

3. **同步失败错误**
   ```
   ❌ 错误：Neo4j同步失败
   原因：连接超时
   解决：检查Neo4j服务状态，重试同步
   ```

#### 💡 最佳实践

##### 建模原则
1. **遵循范式**：尽量遵循数据库范式设计
2. **明确关系**：清晰定义表间关系类型
3. **完整描述**：为表和字段添加详细描述
4. **定期维护**：定期检查和更新模式

##### 性能优化
1. **合理分组**：将相关表分组管理
2. **适度详细**：不要在画布上显示过多表
3. **定期清理**：删除不再使用的表和关系
4. **索引建议**：根据关系定义建议索引

### 4.4 图数据可视化

#### 🎯 功能概述
图数据可视化功能基于Neo4j图数据库，提供数据库结构和关系的图形化展示。用户可以通过交互式图形界面探索数据关系，理解复杂的数据结构。

#### 🖥️ 界面操作指南

##### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 图数据可视化                                                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ 连接: [财务数据库 ▼] [刷新] [发现并同步] 搜索: [________]   │
│                                                             │
│ ┌─────────────────── 图形展示区域 ──────────────────────┐   │
│ │                                                       │   │
│ │     ┌─────────────┐                                   │   │
│ │     │financial_data│                                   │   │
│ │     │   (表)      │                                   │   │
│ │     └──────┬──────┘                                   │   │
│ │            │                                          │   │
│ │            │ HAS_COLUMN                               │   │
│ │            ▼                                          │   │
│ │     ┌─────────────┐    ┌─────────────┐               │   │
│ │     │account_code │    │account_name │               │   │
│ │     │   (列)      │    │   (列)      │               │   │
│ │     └─────────────┘    └─────────────┘               │   │
│ │                                                       │   │
│ │            ┌─────────────┐                           │   │
│ │            │departments  │                           │   │
│ │            │   (表)      │                           │   │
│ │            └──────┬──────┘                           │   │
│ │                   │                                  │   │
│ │                   │ REFERENCES                       │   │
│ │                   ▼                                  │   │
│ │            ┌─────────────┐                           │   │
│ │            │dept_id      │                           │   │
│ │            │   (列)      │                           │   │
│ │            └─────────────┘                           │   │
│ │                                                       │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                             │
│ [🔍] [🔄] [📐] [📊] [⚙️]                                   │
│ 缩放  重置  布局  统计  设置                                │
└─────────────────────────────────────────────────────────────┘
```

##### 操作步骤详解

**步骤1：选择数据库连接**
1. 在连接下拉框中选择要可视化的数据库
2. 如果是首次使用，点击"发现并同步"按钮
3. 系统会将数据库结构同步到Neo4j

**步骤2：探索图形结构**
1. 使用鼠标拖拽节点调整位置
2. 滚轮缩放查看不同层次的细节
3. 点击节点查看详细信息
4. 双击节点展开/折叠相关节点

**步骤3：搜索和过滤**
1. 在搜索框中输入表名或字段名
2. 系统会高亮匹配的节点
3. 支持的搜索语法：
   ```
   表名搜索: financial_data
   字段搜索: account_code
   类型过滤: type:table
   关系过滤: rel:HAS_COLUMN
   ```

**步骤4：使用控制工具**
- 🔍 **缩放工具**：放大/缩小视图
- 🔄 **重置视图**：恢复默认视图
- 📐 **自动布局**：重新排列节点
- 📊 **统计信息**：显示图形统计
- ⚙️ **显示设置**：调整显示选项

#### 🎨 可视化元素

##### 节点类型
- **表节点** 🔵：
  - 颜色：蓝色
  - 形状：圆角矩形
  - 大小：根据字段数量调整

- **列节点** 🟢：
  - 颜色：绿色
  - 形状：椭圆
  - 标识：主键🔑，外键🔗

##### 关系类型
- **HAS_COLUMN** ──：表包含列
- **REFERENCES** ──→：外键引用
- **RELATES_TO** ⟷：逻辑关系

##### 交互功能
1. **悬停显示**：鼠标悬停显示详细信息
2. **右键菜单**：提供快捷操作选项
3. **多选操作**：Ctrl+点击多选节点
4. **路径高亮**：点击节点高亮相关路径

#### 📊 图形分析功能

##### 统计信息面板
```
📊 图形统计信息
├── 节点总数: 45
│   ├── 表节点: 5
│   └── 列节点: 40
├── 关系总数: 43
│   ├── HAS_COLUMN: 40
│   └── REFERENCES: 3
├── 连通组件: 1
└── 最大路径长度: 4
```

##### 中心性分析
系统会自动计算节点的重要性：
- **度中心性**：连接数最多的节点
- **介数中心性**：最重要的桥接节点
- **接近中心性**：到其他节点距离最短的节点

##### 社区发现
自动识别紧密相关的表群组：
```
🏘️ 发现的数据社区
├── 财务核心模块
│   ├── financial_data
│   ├── accounts
│   └── transactions
├── 组织结构模块
│   ├── departments
│   ├── employees
│   └── positions
└── 项目管理模块
    ├── projects
    ├── tasks
    └── resources
```

#### 🔧 高级功能

##### 路径查询
查找两个节点之间的路径：
1. 选择起始节点
2. 选择目标节点
3. 系统显示所有可能路径
4. 高亮最短路径

##### 子图提取
提取特定区域的子图：
1. 框选感兴趣的区域
2. 右键选择"提取子图"
3. 在新窗口中显示子图
4. 支持子图的独立分析

##### 图形导出
支持多种格式的图形导出：
- **PNG/JPG**：静态图片
- **SVG**：矢量图形
- **GraphML**：图形数据格式
- **JSON**：节点关系数据

### 4.5 连接管理

#### 🎯 功能概述
连接管理功能支持多种数据库类型的连接配置和管理，包括SQLite、MySQL、PostgreSQL等。提供统一的数据源管理界面，支持连接测试、结构发现和连接池优化。

#### 🖥️ 界面操作指南

##### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 数据库连接管理                                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ [添加连接] [批量导入] [导出配置] [测试所有连接]             │
│                                                             │
│ ┌─────────────────── 连接列表 ──────────────────────────┐   │
│ │                                                       │   │
│ │ ┌─────────────────────────────────────────────────┐   │   │
│ │ │ 📊 财务数据库                    [✅ 已连接]    │   │   │
│ │ │ 类型: SQLite                                    │   │   │
│ │ │ 路径: /data/fin_data.db                         │   │   │
│ │ │ 表数: 5 | 最后同步: 2024-01-15 10:30           │   │   │
│ │ │                                                 │   │   │
│ │ │ [测试] [编辑] [发现结构] [删除] [详情]          │   │   │
│ │ └─────────────────────────────────────────────────┘   │   │
│ │                                                       │   │
│ │ ┌─────────────────────────────────────────────────┐   │   │
│ │ │ 🏢 人事数据库                    [❌ 连接失败]  │   │   │
│ │ │ 类型: MySQL                                     │   │   │
│ │ │ 主机: 192.168.1.100:3306                       │   │   │
│ │ │ 数据库: hr_system                               │   │   │
│ │ │                                                 │   │   │
│ │ │ [测试] [编辑] [发现结构] [删除] [详情]          │   │   │
│ │ └─────────────────────────────────────────────────┘   │   │
│ │                                                       │   │
│ └───────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

##### 操作步骤详解

**步骤1：添加新连接**
1. 点击"添加连接"按钮
2. 填写连接配置表单：
   ```
   ┌─── 添加数据库连接 ───┐
   │                      │
   │ 连接名称: [财务数据库] │
   │                      │
   │ 数据库类型:          │
   │ ○ SQLite             │
   │ ● MySQL              │
   │ ○ PostgreSQL         │
   │                      │
   │ 主机地址: [localhost] │
   │ 端口: [3306]         │
   │ 数据库名: [finance]   │
   │ 用户名: [root]       │
   │ 密码: [********]     │
   │                      │
   │ 高级选项:            │
   │ ☑ 启用SSL           │
   │ ☑ 连接池            │
   │ 超时时间: [30]秒     │
   │                      │
   │ [测试连接] [取消] [保存] │
   └──────────────────────┘
   ```

**步骤2：测试连接**
1. 填写完连接信息后点击"测试连接"
2. 系统会验证连接参数：
   ```
   🔄 正在测试连接...
   ✅ 网络连接正常
   ✅ 认证信息正确
   ✅ 数据库访问正常
   ✅ 权限验证通过

   连接测试成功！
   发现 12 个表，156 个字段
   ```

**步骤3：发现数据库结构**
1. 连接成功后点击"发现结构"
2. 系统自动分析数据库结构：
   ```
   🔍 正在发现数据库结构...

   发现的表:
   ├── financial_data (31个字段)
   ├── departments (5个字段)
   ├── employees (8个字段)
   ├── projects (6个字段)
   └── accounts (12个字段)

   发现的关系:
   ├── financial_data.dept_id → departments.id
   ├── employees.dept_id → departments.id
   └── financial_data.emp_id → employees.id

   ✅ 结构发现完成，已同步到元数据库
   ```

#### 🔧 支持的数据库类型

##### SQLite 配置
```
连接名称: 本地SQLite数据库
数据库类型: SQLite
数据库路径: /path/to/database.db
```

##### MySQL 配置
```
连接名称: MySQL生产数据库
数据库类型: MySQL
主机地址: mysql.company.com
端口: 3306
数据库名: production_db
用户名: app_user
密码: secure_password
字符集: utf8mb4
```

##### PostgreSQL 配置
```
连接名称: PostgreSQL数据仓库
数据库类型: PostgreSQL
主机地址: postgres.company.com
端口: 5432
数据库名: datawarehouse
用户名: analyst
密码: complex_password
模式: public
```

#### 🚀 连接池优化

##### 连接池配置
```python
# 连接池参数说明
pool_size=10,          # 基础连接数
max_overflow=20,       # 最大溢出连接数
pool_timeout=30,       # 获取连接超时时间
pool_recycle=3600,     # 连接回收时间(1小时)
pool_pre_ping=True,    # 连接前ping检查
```

##### 性能监控
```
📊 连接池状态监控
├── 活跃连接: 5/10
├── 空闲连接: 5/10
├── 溢出连接: 2/20
├── 等待队列: 0
├── 连接成功率: 99.8%
└── 平均响应时间: 45ms
```

#### 🔒 安全配置

##### 密码加密
系统会自动加密存储的密码：
```python
# 密码加密存储
password_encrypted = encrypt_password(plain_password)
# 使用时自动解密
plain_password = decrypt_password(password_encrypted)
```

##### SSL连接
支持SSL加密连接：
```
SSL配置:
☑ 启用SSL
☑ 验证服务器证书
☑ 客户端证书认证
证书路径: /path/to/client.crt
私钥路径: /path/to/client.key
CA证书路径: /path/to/ca.crt
```

#### 🚨 错误处理

##### 常见连接错误
1. **网络连接失败**
   ```
   ❌ 错误: 无法连接到主机
   原因: 网络不通或主机地址错误
   解决: 检查网络连接和主机地址
   ```

2. **认证失败**
   ```
   ❌ 错误: 用户名或密码错误
   原因: 认证信息不正确
   解决: 验证用户名和密码
   ```

3. **权限不足**
   ```
   ❌ 错误: 访问被拒绝
   原因: 用户权限不足
   解决: 联系管理员分配适当权限
   ```

##### 自动重连机制
```python
# 连接断开自动重连
retry_attempts = 3
retry_delay = 5  # 秒
exponential_backoff = True
```

### 4.6 数据映射

#### 🎯 功能概述
数据映射功能建立自然语言术语与数据库实际值之间的映射关系，提高智能查询的准确性。支持批量导入、智能建议和映射验证等功能。

#### 🖥️ 界面操作指南

##### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 数据映射管理                                                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ 连接: [财务数据库 ▼] 表: [financial_data ▼] 列: [account_name ▼] │
│                                                             │
│ [添加映射] [批量导入] [智能建议] [导出映射] [验证映射]       │
│                                                             │
│ ┌─────────────────── 映射列表 ──────────────────────────┐   │
│ │                                                       │   │
│ │ ┌─────────────────────────────────────────────────┐   │   │
│ │ │ 自然语言术语: 主营业务收入                      │   │   │
│ │ │ 数据库值: 主营业务收入                          │   │   │
│ │ │ 字段: account_name                              │   │   │
│ │ │ 使用次数: 156 | 成功率: 98%                     │   │   │
│ │ │                                                 │   │   │
│ │ │ [编辑] [删除] [测试] [查看使用记录]             │   │   │
│ │ └─────────────────────────────────────────────────┘   │   │
│ │                                                       │   │
│ │ ┌─────────────────────────────────────────────────┐   │   │
│ │ │ 自然语言术语: 管理费用                          │   │   │
│ │ │ 数据库值: 管理费用                              │   │   │
│ │ │ 字段: account_name                              │   │   │
│ │ │ 使用次数: 89 | 成功率: 95%                      │   │   │
│ │ │                                                 │   │   │
│ │ │ [编辑] [删除] [测试] [查看使用记录]             │   │   │
│ │ └─────────────────────────────────────────────────┘   │   │
│ │                                                       │   │
│ └───────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

##### 操作步骤详解

**步骤1：选择映射目标**
1. 选择数据库连接
2. 选择要映射的表
3. 选择要映射的字段
4. 系统会显示该字段的现有映射

**步骤2：添加新映射**
1. 点击"添加映射"按钮
2. 填写映射信息：
   ```
   ┌─── 添加数据映射 ───┐
   │                    │
   │ 自然语言术语:      │
   │ [收入]             │
   │                    │
   │ 数据库实际值:      │
   │ [主营业务收入]     │
   │                    │
   │ 映射类型:          │
   │ ● 精确匹配         │
   │ ○ 模糊匹配         │
   │ ○ 正则表达式       │
   │                    │
   │ 优先级: [高 ▼]     │
   │                    │
   │ 描述:              │
   │ [用户常用的收入简称] │
   │                    │
   │ [取消] [保存]      │
   └────────────────────┘
   ```

**步骤3：智能建议**
1. 点击"智能建议"按钮
2. 系统分析现有数据提供建议：
   ```
   🤖 智能映射建议

   基于数据分析，建议添加以下映射:

   ✨ 高置信度建议:
   ├── "费用" → "管理费用" (置信度: 95%)
   ├── "成本" → "主营业务成本" (置信度: 92%)
   └── "资产" → "流动资产" (置信度: 88%)

   💡 中等置信度建议:
   ├── "支出" → "销售费用" (置信度: 75%)
   ├── "投资" → "长期投资" (置信度: 72%)
   └── "负债" → "流动负债" (置信度: 68%)

   [全部采用] [选择采用] [忽略建议]
   ```

**步骤4：批量导入映射**
1. 点击"批量导入"按钮
2. 选择导入格式（CSV/Excel/JSON）
3. 上传映射文件：
   ```csv
   自然语言术语,数据库值,字段名,优先级,描述
   收入,主营业务收入,account_name,高,主要收入来源
   费用,管理费用,account_name,高,管理相关费用
   成本,主营业务成本,account_name,中,业务直接成本
   ```

#### 🎯 映射类型说明

##### 精确匹配
完全匹配的映射关系：
```
自然语言: "收入"
数据库值: "主营业务收入"
匹配规则: 完全相等
```

##### 模糊匹配
支持相似度匹配：
```
自然语言: "收入"
数据库值: ["主营业务收入", "其他业务收入", "营业外收入"]
匹配规则: 包含关键词
相似度阈值: 0.8
```

##### 正则表达式
支持复杂的模式匹配：
```
自然语言: "费用"
数据库值: 正则表达式 ".*费用.*"
匹配结果: ["管理费用", "销售费用", "财务费用"]
```

#### 📊 映射统计分析

##### 使用统计
```
📊 映射使用统计
├── 总映射数: 156
├── 活跃映射: 89 (57%)
├── 平均成功率: 94%
├── 最常用映射: "收入" → "主营业务收入" (234次)
└── 最近更新: 2024-01-15 14:30
```

##### 效果分析
```
📈 映射效果分析
├── 查询准确率提升: +23%
├── 用户满意度: 4.8/5.0
├── 查询时间减少: -15%
└── 错误查询减少: -67%
```

#### 🔧 高级功能

##### 映射验证
自动验证映射的有效性：
```python
def validate_mapping(nl_term, db_value, field_name):
    """验证映射有效性"""
    # 检查数据库值是否存在
    if not value_exists_in_db(db_value, field_name):
        return False, "数据库中不存在该值"

    # 检查映射冲突
    if has_conflicting_mapping(nl_term):
        return False, "存在冲突的映射"

    # 检查使用频率
    usage_count = get_usage_count(nl_term)
    if usage_count == 0:
        return True, "新映射，建议观察使用效果"

    return True, "映射验证通过"
```

##### 智能优化
系统会自动优化映射：
- 合并相似映射
- 删除无效映射
- 调整映射优先级
- 更新映射权重

##### 版本控制
支持映射的版本管理：
- 记录映射变更历史
- 支持回滚操作
- 比较不同版本差异
- 导出版本快照

---

## 用户角色指南

### 👨‍💼 业务分析师使用指南

#### 🎯 角色特点
- 熟悉业务逻辑，但SQL技能有限
- 需要频繁进行数据分析和报告
- 关注数据的业务含义和趋势

#### 🚀 推荐使用流程

**日常分析工作流程**
1. **智能查询** → 快速获取业务数据
2. **智能问答** → 查找历史相似分析
3. **图数据可视化** → 理解数据关系

**典型使用场景**
```
场景1: 月度收入分析
├── 查询: "分析本月各部门的收入情况"
├── 操作: 智能查询 → 查看结果表格 → 导出报告
└── 输出: 部门收入对比表和趋势图

场景2: 费用异常分析
├── 查询: "查找费用异常增长的科目"
├── 操作: 智能查询 → 对比历史数据 → 深入分析
└── 输出: 异常费用清单和原因分析

场景3: 预算执行监控
├── 查询: "对比预算与实际执行情况"
├── 操作: 智能查询 → 计算差异率 → 生成监控报告
└── 输出: 预算执行偏差分析报告
```

#### 💡 使用技巧
1. **善用自然语言**：用业务术语描述需求
2. **建立查询模板**：保存常用查询到智能问答
3. **关注数据趋势**：多使用时间对比分析
4. **验证数据逻辑**：理解SQL生成逻辑确保准确性

### 👩‍💰 财务人员使用指南

#### 🎯 角色特点
- 具备专业财务知识
- 需要精确的财务数据分析
- 关注合规性和准确性

#### 🚀 推荐使用流程

**财务报表分析流程**
1. **连接管理** → 确保数据源准确
2. **数据映射** → 建立标准财务术语映射
3. **智能查询** → 执行财务分析查询
4. **数据建模** → 维护财务数据结构

**专业分析场景**
```
场景1: 三大报表分析
├── 资产负债表: "生成2024年12月资产负债表"
├── 利润表: "分析2024年度利润构成情况"
└── 现金流量表: "查看经营活动现金流量"

场景2: 财务比率分析
├── 盈利能力: "计算毛利率和净利率"
├── 偿债能力: "分析流动比率和速动比率"
└── 运营能力: "计算存货周转率和应收账款周转率"

场景3: 成本费用分析
├── 成本结构: "分析主营业务成本构成"
├── 费用控制: "监控三大费用变化趋势"
└── 成本核算: "按项目分摊间接费用"
```

#### 🔍 专业功能使用

**财务科目映射管理**
```
收入类科目 (60xx):
├── "主营收入" → "主营业务收入"
├── "其他收入" → "其他业务收入"
└── "营业外收入" → "营业外收入"

成本费用类科目 (64xx-68xx):
├── "主营成本" → "主营业务成本"
├── "管理费用" → "管理费用"
├── "销售费用" → "销售费用"
└── "财务费用" → "财务费用"
```

**数据质量控制**
1. **余额校验**：定期检查借贷平衡
2. **科目核对**：确保科目分类正确
3. **期间匹配**：验证会计期间一致性
4. **异常监控**：识别异常数据波动

### 👨‍💻 数据管理员使用指南

#### 🎯 角色特点
- 具备技术背景和数据库知识
- 负责系统维护和数据管理
- 关注系统性能和数据质量

#### 🚀 推荐使用流程

**系统管理工作流程**
1. **连接管理** → 配置和维护数据源
2. **数据建模** → 设计和优化数据结构
3. **图数据可视化** → 监控数据关系
4. **性能优化** → 调优系统性能

**技术管理场景**
```
场景1: 数据源管理
├── 添加新数据源连接
├── 配置连接池参数
├── 监控连接状态
└── 优化查询性能

场景2: 元数据管理
├── 发现数据库结构
├── 定义表关系
├── 同步到图数据库
└── 维护数据字典

场景3: 系统监控
├── 监控查询性能
├── 分析用户使用模式
├── 优化缓存策略
└── 处理系统异常
```

#### 🔧 技术功能使用

**数据库连接优化**
```python
# 连接池配置优化
POOL_SIZE = 20              # 基础连接数
MAX_OVERFLOW = 50           # 最大溢出连接
POOL_TIMEOUT = 60           # 连接超时时间
POOL_RECYCLE = 7200         # 连接回收时间
POOL_PRE_PING = True        # 连接前检查
```

**性能监控指标**
```
📊 系统性能监控
├── 查询响应时间: < 2s (目标)
├── 并发用户数: 50+ (支持)
├── 缓存命中率: > 80% (目标)
├── 错误率: < 1% (目标)
└── 系统可用性: > 99.9% (目标)
```

**数据质量管理**
1. **数据完整性检查**：定期验证数据完整性
2. **数据一致性校验**：检查跨表数据一致性
3. **数据准确性验证**：验证计算结果准确性
4. **数据时效性监控**：监控数据更新及时性

---

## 业务场景示例

### 📊 财务月报生成场景

#### 场景描述
财务部门需要生成2024年10月的财务月报，包括收入、成本、费用和利润分析。

#### 操作步骤

**步骤1：收入分析**
```
查询: "分析2024年10月各类收入的构成情况"

生成SQL:
SELECT
    CASE
        WHEN account_code LIKE '60%' THEN '主营业务收入'
        WHEN account_code LIKE '61%' THEN '其他业务收入'
        WHEN account_code LIKE '63%' THEN '营业外收入'
        ELSE '其他收入'
    END as 收入类型,
    SUM(credit_amount) as 金额
FROM financial_data
WHERE year = 2024 AND month = 10
    AND account_code LIKE '6%'
    AND credit_amount > 0
GROUP BY
    CASE
        WHEN account_code LIKE '60%' THEN '主营业务收入'
        WHEN account_code LIKE '61%' THEN '其他业务收入'
        WHEN account_code LIKE '63%' THEN '营业外收入'
        ELSE '其他收入'
    END
ORDER BY 金额 DESC;

结果:
┌─────────────────┬─────────────────┐
│ 收入类型        │ 金额(元)        │
├─────────────────┼─────────────────┤
│ 主营业务收入    │ 2,850,000.00    │
│ 其他业务收入    │ 156,000.00      │
│ 营业外收入      │ 23,500.00       │
└─────────────────┴─────────────────┘
```

**步骤2：成本费用分析**
```
查询: "统计2024年10月的成本费用构成"

生成SQL:
SELECT
    CASE
        WHEN account_code LIKE '64%' THEN '主营业务成本'
        WHEN account_code LIKE '66%' THEN '管理费用'
        WHEN account_code LIKE '67%' THEN '销售费用'
        WHEN account_code LIKE '68%' THEN '财务费用'
        ELSE '其他费用'
    END as 费用类型,
    SUM(debit_amount) as 金额
FROM financial_data
WHERE year = 2024 AND month = 10
    AND account_code LIKE '6%'
    AND debit_amount > 0
GROUP BY
    CASE
        WHEN account_code LIKE '64%' THEN '主营业务成本'
        WHEN account_code LIKE '66%' THEN '管理费用'
        WHEN account_code LIKE '67%' THEN '销售费用'
        WHEN account_code LIKE '68%' THEN '财务费用'
        ELSE '其他费用'
    END
ORDER BY 金额 DESC;
```

**步骤3：利润计算**
```
查询: "计算2024年10月的营业利润和净利润"

生成SQL:
WITH revenue AS (
    SELECT SUM(credit_amount) as total_revenue
    FROM financial_data
    WHERE year = 2024 AND month = 10
        AND account_code LIKE '60%'
),
costs AS (
    SELECT SUM(debit_amount) as total_costs
    FROM financial_data
    WHERE year = 2024 AND month = 10
        AND account_code IN ('64%', '66%', '67%', '68%')
)
SELECT
    r.total_revenue as 营业收入,
    c.total_costs as 营业成本费用,
    (r.total_revenue - c.total_costs) as 营业利润
FROM revenue r, costs c;
```

#### 预期输出
```
📊 2024年10月财务月报

收入分析:
├── 主营业务收入: 2,850,000.00元 (94.1%)
├── 其他业务收入: 156,000.00元 (5.1%)
└── 营业外收入: 23,500.00元 (0.8%)
总收入: 3,029,500.00元

成本费用分析:
├── 主营业务成本: 1,710,000.00元 (75.2%)
├── 管理费用: 285,000.00元 (12.5%)
├── 销售费用: 198,000.00元 (8.7%)
└── 财务费用: 82,000.00元 (3.6%)
总成本费用: 2,275,000.00元

利润分析:
├── 营业利润: 754,500.00元
├── 利润率: 24.9%
└── 同比增长: +12.3%
```

### 🏢 部门绩效分析场景

#### 场景描述
人力资源部门需要分析各部门2024年第三季度的绩效表现，包括收入贡献、成本控制和效率指标。

#### 操作步骤

**步骤1：部门收入贡献分析**
```
查询: "分析2024年第三季度各部门的收入贡献"

生成SQL:
SELECT
    d.dept_name as 部门名称,
    SUM(f.credit_amount) as 收入贡献,
    COUNT(*) as 业务笔数,
    AVG(f.credit_amount) as 平均单笔收入
FROM financial_data f
JOIN departments d ON f.dept_id = d.id
WHERE f.year = 2024
    AND f.month BETWEEN 7 AND 9
    AND f.account_code LIKE '60%'
    AND f.credit_amount > 0
GROUP BY d.dept_name
ORDER BY 收入贡献 DESC;
```

**步骤2：部门成本控制分析**
```
查询: "对比各部门的费用支出情况"

生成SQL:
SELECT
    d.dept_name as 部门名称,
    SUM(CASE WHEN f.account_code LIKE '66%' THEN f.debit_amount ELSE 0 END) as 管理费用,
    SUM(CASE WHEN f.account_code LIKE '67%' THEN f.debit_amount ELSE 0 END) as 销售费用,
    SUM(f.debit_amount) as 总费用支出
FROM financial_data f
JOIN departments d ON f.dept_id = d.id
WHERE f.year = 2024
    AND f.month BETWEEN 7 AND 9
    AND f.account_code LIKE '6%'
    AND f.debit_amount > 0
GROUP BY d.dept_name
ORDER BY 总费用支出 DESC;
```

**步骤3：部门效率指标计算**
```
查询: "计算各部门的收入成本比和人均产出"

生成SQL:
WITH dept_performance AS (
    SELECT
        d.dept_name,
        SUM(CASE WHEN f.credit_amount > 0 THEN f.credit_amount ELSE 0 END) as revenue,
        SUM(CASE WHEN f.debit_amount > 0 THEN f.debit_amount ELSE 0 END) as costs,
        COUNT(DISTINCT e.id) as employee_count
    FROM financial_data f
    JOIN departments d ON f.dept_id = d.id
    LEFT JOIN employees e ON e.dept_id = d.id
    WHERE f.year = 2024 AND f.month BETWEEN 7 AND 9
    GROUP BY d.dept_name
)
SELECT
    dept_name as 部门名称,
    revenue as 总收入,
    costs as 总成本,
    ROUND(revenue / NULLIF(costs, 0), 2) as 收入成本比,
    ROUND(revenue / NULLIF(employee_count, 0), 2) as 人均产出,
    employee_count as 部门人数
FROM dept_performance
WHERE revenue > 0 OR costs > 0
ORDER BY 收入成本比 DESC;
```

### 📈 投资项目ROI分析场景

#### 场景描述
投资部门需要分析2024年各投资项目的投资回报率(ROI)，评估投资效果。

#### 操作步骤

**步骤1：项目投资成本统计**
```
查询: "统计2024年各项目的投资成本"

生成SQL:
SELECT
    p.project_name as 项目名称,
    p.project_code as 项目编号,
    SUM(f.debit_amount) as 投资成本,
    MIN(f.transaction_date) as 投资开始日期,
    MAX(f.transaction_date) as 最近投资日期
FROM financial_data f
JOIN projects p ON f.project_id = p.id
WHERE f.year = 2024
    AND f.account_code LIKE '1%'  -- 资产类科目
    AND f.debit_amount > 0
GROUP BY p.project_name, p.project_code
ORDER BY 投资成本 DESC;
```

**步骤2：项目收益统计**
```
查询: "计算各项目产生的收益"

生成SQL:
SELECT
    p.project_name as 项目名称,
    SUM(f.credit_amount) as 项目收益,
    COUNT(*) as 收益笔数,
    AVG(f.credit_amount) as 平均单笔收益
FROM financial_data f
JOIN projects p ON f.project_id = p.id
WHERE f.year = 2024
    AND f.account_code LIKE '60%'  -- 收入类科目
    AND f.credit_amount > 0
GROUP BY p.project_name
ORDER BY 项目收益 DESC;
```

**步骤3：ROI计算和排名**
```
查询: "计算各项目的投资回报率并排名"

生成SQL:
WITH project_investment AS (
    SELECT
        p.project_name,
        SUM(f.debit_amount) as total_investment
    FROM financial_data f
    JOIN projects p ON f.project_id = p.id
    WHERE f.year = 2024 AND f.account_code LIKE '1%'
    GROUP BY p.project_name
),
project_revenue AS (
    SELECT
        p.project_name,
        SUM(f.credit_amount) as total_revenue
    FROM financial_data f
    JOIN projects p ON f.project_id = p.id
    WHERE f.year = 2024 AND f.account_code LIKE '60%'
    GROUP BY p.project_name
)
SELECT
    COALESCE(i.project_name, r.project_name) as 项目名称,
    COALESCE(i.total_investment, 0) as 投资成本,
    COALESCE(r.total_revenue, 0) as 项目收益,
    CASE
        WHEN i.total_investment > 0 THEN
            ROUND(((r.total_revenue - i.total_investment) / i.total_investment) * 100, 2)
        ELSE NULL
    END as ROI百分比,
    CASE
        WHEN i.total_investment > 0 THEN
            ROUND(r.total_revenue / i.total_investment, 2)
        ELSE NULL
    END as 收益倍数
FROM project_investment i
FULL OUTER JOIN project_revenue r ON i.project_name = r.project_name
ORDER BY ROI百分比 DESC NULLS LAST;
```

#### 预期输出
```
📊 2024年投资项目ROI分析报告

项目投资回报排名:
┌─────────────────┬─────────────┬─────────────┬─────────┬─────────┐
│ 项目名称        │ 投资成本    │ 项目收益    │ ROI(%)  │ 收益倍数│
├─────────────────┼─────────────┼─────────────┼─────────┼─────────┤
│ 数字化转型项目  │ 2,000,000   │ 3,200,000   │ 60.00   │ 1.60    │
│ 市场拓展项目    │ 1,500,000   │ 2,100,000   │ 40.00   │ 1.40    │
│ 产品研发项目    │ 3,000,000   │ 3,900,000   │ 30.00   │ 1.30    │
│ 设备升级项目    │ 1,200,000   │ 1,320,000   │ 10.00   │ 1.10    │
│ 办公楼装修项目  │ 800,000     │ 800,000     │ 0.00    │ 1.00    │
└─────────────────┴─────────────┴─────────────┴─────────┴─────────┘

投资效果总结:
├── 总投资金额: 8,500,000元
├── 总收益金额: 11,320,000元
├── 整体ROI: 33.18%
├── 盈利项目数: 4/5
└── 建议: 继续投资数字化和市场拓展项目
```

---

## API接口文档

### 🔌 接口概览

#### 基础信息
- **Base URL**: `http://localhost:8000/api`
- **认证方式**: Bearer Token (可选)
- **数据格式**: JSON
- **字符编码**: UTF-8

#### 接口分类
```
📡 API接口分类
├── 🔗 连接管理 (/connections)
├── 📊 数据建模 (/schema)
├── 🤖 智能查询 (/text2sql-sse)
├── 💡 智能问答 (/hybrid-qa)
├── 🎯 数据映射 (/value-mappings)
├── 📈 图数据可视化 (/graph-visualization)
└── 💬 聊天历史 (/chat-history)
```

### 🔗 连接管理接口

#### 获取连接列表
```http
GET /api/connections/
```

**响应示例**:
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "财务数据库",
      "db_type": "sqlite",
      "host": "localhost",
      "port": 0,
      "database_name": "fin_data.db",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### 创建新连接
```http
POST /api/connections/
Content-Type: application/json

{
  "name": "新数据库连接",
  "db_type": "mysql",
  "host": "localhost",
  "port": 3306,
  "username": "root",
  "password": "password",
  "database_name": "test_db"
}
```

**响应示例**:
```json
{
  "status": "success",
  "message": "连接创建成功",
  "data": {
    "id": 2,
    "name": "新数据库连接",
    "db_type": "mysql",
    "host": "localhost",
    "port": 3306,
    "database_name": "test_db"
  }
}
```

#### 测试连接
```http
POST /api/connections/{connection_id}/test
```

**响应示例**:
```json
{
  "status": "success",
  "message": "连接测试成功",
  "details": {
    "connection_time": "45ms",
    "database_version": "MySQL 8.0.25",
    "tables_count": 12,
    "accessible": true
  }
}
```

### 🤖 智能查询接口

#### 流式Text2SQL查询
```http
POST /api/text2sql-sse/stream-query
Content-Type: application/json

{
  "connection_id": 1,
  "query": "查询2024年9月的收入情况",
  "use_hybrid_retrieval": true,
  "include_explanation": true
}
```

**流式响应示例**:
```
data: {"source": "query_analyzer", "content": "正在分析查询意图...", "region": "analysis"}

data: {"source": "schema_retriever", "content": "检索相关表结构...", "region": "analysis"}

data: {"source": "sql_generator", "content": "SELECT SUM(credit_amount) as 收入...", "region": "sql"}

data: {"source": "sql_executor", "content": "执行SQL查询...", "region": "execution"}

data: {"source": "final_result", "content": {"results": [...], "sql": "...", "explanation": "..."}, "is_final": true}
```

#### 批量查询
```http
POST /api/text2sql-sse/batch-query
Content-Type: application/json

{
  "connection_id": 1,
  "queries": [
    "查询2024年9月的收入",
    "分析管理费用构成",
    "统计资产总额"
  ],
  "parallel": true
}
```

### 💡 智能问答接口

#### 搜索相似问答对
```http
POST /api/hybrid-qa/qa-pairs/search
Content-Type: application/json

{
  "question": "查询收入情况",
  "connection_id": 1,
  "top_k": 5,
  "similarity_threshold": 0.7
}
```

**响应示例**:
```json
{
  "status": "success",
  "data": [
    {
      "id": "qa_001",
      "question": "查询2024年第三季度的主营业务收入",
      "sql": "SELECT SUM(credit_amount) FROM financial_data...",
      "similarity_score": 0.95,
      "explanation": "语义相似度: 0.95",
      "usage_count": 156,
      "success_rate": 0.98
    }
  ]
}
```

#### 创建问答对
```http
POST /api/hybrid-qa/qa-pairs
Content-Type: application/json

{
  "question": "查询2024年各月收入趋势",
  "sql": "SELECT month, SUM(credit_amount) FROM financial_data WHERE year = 2024 GROUP BY month",
  "connection_id": 1,
  "difficulty_level": "medium",
  "query_type": "trend_analysis",
  "description": "月度收入趋势分析查询"
}
```

### 📊 数据建模接口

#### 发现数据库结构
```http
POST /api/schema/{connection_id}/discover
```

**响应示例**:
```json
{
  "status": "success",
  "message": "结构发现完成",
  "data": {
    "tables": [
      {
        "table_name": "financial_data",
        "columns": [
          {
            "column_name": "id",
            "data_type": "INTEGER",
            "is_primary_key": true,
            "is_foreign_key": false
          }
        ]
      }
    ],
    "relationships": [
      {
        "source_table": "financial_data",
        "source_column": "dept_id",
        "target_table": "departments",
        "target_column": "id",
        "relationship_type": "N-to-1"
      }
    ]
  }
}
```

#### 发布数据模式
```http
POST /api/schema/{connection_id}/publish
Content-Type: application/json

{
  "tables": [...],
  "relationships": [...],
  "sync_to_neo4j": true
}
```

### 🎯 数据映射接口

#### 获取映射列表
```http
GET /api/value-mappings/?column_id=1&skip=0&limit=100
```

#### 创建映射
```http
POST /api/value-mappings/
Content-Type: application/json

{
  "column_id": 1,
  "nl_term": "收入",
  "db_value": "主营业务收入"
}
```

#### 批量导入映射
```http
POST /api/value-mappings/batch-import
Content-Type: multipart/form-data

file: mappings.csv
format: csv
```

### 📈 图数据可视化接口

#### 获取图数据
```http
GET /api/graph-visualization/{connection_id}
```

**响应示例**:
```json
{
  "status": "success",
  "data": {
    "nodes": [
      {
        "id": "table-1",
        "type": "table",
        "data": {
          "label": "financial_data",
          "description": "财务数据表",
          "nodeType": "table"
        }
      }
    ],
    "edges": [
      {
        "id": "edge-1",
        "source": "table-1",
        "target": "column-1",
        "type": "HAS_COLUMN"
      }
    ]
  }
}
```

### 🔧 错误处理

#### 标准错误响应格式
```json
{
  "status": "error",
  "error_code": "VALIDATION_ERROR",
  "message": "请求参数验证失败",
  "details": {
    "field": "connection_id",
    "reason": "连接ID不能为空"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 常见错误码
```
📋 错误码说明
├── 400 BAD_REQUEST - 请求参数错误
├── 401 UNAUTHORIZED - 认证失败
├── 403 FORBIDDEN - 权限不足
├── 404 NOT_FOUND - 资源不存在
├── 429 RATE_LIMITED - 请求频率超限
├── 500 INTERNAL_ERROR - 服务器内部错误
└── 503 SERVICE_UNAVAILABLE - 服务不可用
```

### 📝 SDK示例

#### Python SDK
```python
import requests
import json

class ChatDBClient:
    def __init__(self, base_url="http://localhost:8000/api"):
        self.base_url = base_url
        self.session = requests.Session()

    def query_text2sql(self, connection_id, query):
        """执行Text2SQL查询"""
        url = f"{self.base_url}/text2sql-sse/stream-query"
        data = {
            "connection_id": connection_id,
            "query": query,
            "use_hybrid_retrieval": True
        }

        response = self.session.post(url, json=data, stream=True)

        for line in response.iter_lines():
            if line.startswith(b'data: '):
                yield json.loads(line[6:])

    def get_connections(self):
        """获取连接列表"""
        url = f"{self.base_url}/connections/"
        response = self.session.get(url)
        return response.json()

# 使用示例
client = ChatDBClient()
connections = client.get_connections()

for message in client.query_text2sql(1, "查询收入情况"):
    print(message)
```

#### JavaScript SDK
```javascript
class ChatDBClient {
    constructor(baseURL = 'http://localhost:8000/api') {
        this.baseURL = baseURL;
    }

    async queryText2SQL(connectionId, query) {
        const response = await fetch(`${this.baseURL}/text2sql-sse/stream-query`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                connection_id: connectionId,
                query: query,
                use_hybrid_retrieval: true
            })
        });

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = JSON.parse(line.slice(6));
                    console.log(data);
                }
            }
        }
    }

    async getConnections() {
        const response = await fetch(`${this.baseURL}/connections/`);
        return await response.json();
    }
}

// 使用示例
const client = new ChatDBClient();
client.queryText2SQL(1, '查询收入情况');
```

---

## 性能优化指南

### 🚀 系统性能优化

#### 数据库连接优化

**连接池配置**
```python
# 推荐的连接池配置
DATABASE_CONFIG = {
    'pool_size': 20,           # 基础连接数
    'max_overflow': 50,        # 最大溢出连接
    'pool_timeout': 60,        # 获取连接超时
    'pool_recycle': 7200,      # 连接回收时间(2小时)
    'pool_pre_ping': True,     # 连接前检查
    'echo': False,             # 生产环境关闭SQL日志
}
```

**查询优化策略**
```sql
-- 1. 使用索引优化
CREATE INDEX idx_financial_data_date ON financial_data(year, month);
CREATE INDEX idx_financial_data_account ON financial_data(account_code);
CREATE INDEX idx_financial_data_dept ON financial_data(dept_id);

-- 2. 分区表优化（适用于大数据量）
CREATE TABLE financial_data_2024 PARTITION OF financial_data
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- 3. 物化视图优化（常用聚合查询）
CREATE MATERIALIZED VIEW monthly_revenue AS
SELECT year, month, dept_id, SUM(credit_amount) as total_revenue
FROM financial_data
WHERE account_code LIKE '60%'
GROUP BY year, month, dept_id;
```

#### 缓存策略优化

**Redis缓存配置**
```python
CACHE_CONFIG = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://localhost:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 100,
                'retry_on_timeout': True,
            }
        },
        'TIMEOUT': 3600,  # 1小时默认过期时间
    }
}
```

**缓存策略分层**
```
📊 缓存策略分层
├── L1 内存缓存 (应用内)
│   ├── 元数据缓存: 5分钟
│   ├── 连接信息: 10分钟
│   └── 用户会话: 30分钟
├── L2 Redis缓存 (分布式)
│   ├── 查询结果: 1小时
│   ├── 统计数据: 6小时
│   └── 配置信息: 24小时
└── L3 数据库缓存 (持久化)
    ├── 物化视图: 自动刷新
    ├── 索引缓存: 数据库管理
    └── 查询计划: 数据库优化
```

#### 前端性能优化

**React组件优化**
```javascript
// 1. 使用React.memo避免不必要的重渲染
const QueryResult = React.memo(({ data, loading }) => {
    return (
        <div>
            {loading ? <Spinner /> : <Table data={data} />}
        </div>
    );
});

// 2. 使用useMemo缓存计算结果
const ProcessedData = ({ rawData }) => {
    const processedData = useMemo(() => {
        return rawData.map(item => ({
            ...item,
            formattedAmount: formatCurrency(item.amount)
        }));
    }, [rawData]);

    return <DataTable data={processedData} />;
};

// 3. 使用useCallback缓存事件处理函数
const QueryForm = ({ onSubmit }) => {
    const handleSubmit = useCallback((formData) => {
        onSubmit(formData);
    }, [onSubmit]);

    return <Form onSubmit={handleSubmit} />;
};
```

**虚拟滚动优化**
```javascript
// 大数据量表格虚拟滚动
import { FixedSizeList as List } from 'react-window';

const VirtualizedTable = ({ data }) => {
    const Row = ({ index, style }) => (
        <div style={style}>
            <TableRow data={data[index]} />
        </div>
    );

    return (
        <List
            height={600}
            itemCount={data.length}
            itemSize={50}
            width="100%"
        >
            {Row}
        </List>
    );
};
```

### 📊 监控和诊断

#### 性能监控指标

**系统级指标**
```python
PERFORMANCE_METRICS = {
    'response_time': {
        'target': '<2s',
        'warning': '2-5s',
        'critical': '>5s'
    },
    'throughput': {
        'target': '>100 req/s',
        'warning': '50-100 req/s',
        'critical': '<50 req/s'
    },
    'error_rate': {
        'target': '<1%',
        'warning': '1-5%',
        'critical': '>5%'
    },
    'cpu_usage': {
        'target': '<70%',
        'warning': '70-85%',
        'critical': '>85%'
    },
    'memory_usage': {
        'target': '<80%',
        'warning': '80-90%',
        'critical': '>90%'
    }
}
```

**业务级指标**
```python
BUSINESS_METRICS = {
    'query_success_rate': {
        'target': '>95%',
        'current': '97.8%'
    },
    'user_satisfaction': {
        'target': '>4.5/5',
        'current': '4.7/5'
    },
    'cache_hit_rate': {
        'target': '>80%',
        'current': '85.2%'
    },
    'concurrent_users': {
        'max_supported': 200,
        'current_peak': 156
    }
}
```

#### 性能诊断工具

**SQL查询分析**
```python
def analyze_slow_queries():
    """分析慢查询"""
    slow_queries = get_slow_queries(threshold=2.0)  # 2秒以上

    for query in slow_queries:
        print(f"""
        查询: {query.sql[:100]}...
        执行时间: {query.duration}s
        执行次数: {query.count}
        平均时间: {query.avg_duration}s
        建议: {suggest_optimization(query)}
        """)

def suggest_optimization(query):
    """查询优化建议"""
    suggestions = []

    if 'WHERE' not in query.sql.upper():
        suggestions.append("添加WHERE条件限制结果集")

    if 'ORDER BY' in query.sql.upper() and 'LIMIT' not in query.sql.upper():
        suggestions.append("添加LIMIT限制排序结果数量")

    if query.table_scan_count > 0:
        suggestions.append("考虑添加索引避免全表扫描")

    return "; ".join(suggestions)
```

**内存使用分析**
```python
import psutil
import gc

def analyze_memory_usage():
    """分析内存使用情况"""
    process = psutil.Process()
    memory_info = process.memory_info()

    print(f"""
    📊 内存使用分析
    ├── 物理内存: {memory_info.rss / 1024 / 1024:.2f} MB
    ├── 虚拟内存: {memory_info.vms / 1024 / 1024:.2f} MB
    ├── 内存占用率: {process.memory_percent():.2f}%
    └── 垃圾回收: {len(gc.get_objects())} 对象
    """)

    # 强制垃圾回收
    collected = gc.collect()
    print(f"垃圾回收释放: {collected} 对象")
```

### 🔧 优化最佳实践

#### 查询优化最佳实践

1. **使用合适的索引**
```sql
-- 复合索引优化多条件查询
CREATE INDEX idx_financial_complex ON financial_data(year, month, account_code, dept_id);

-- 部分索引优化特定条件
CREATE INDEX idx_financial_revenue ON financial_data(account_code, credit_amount)
WHERE account_code LIKE '60%' AND credit_amount > 0;
```

2. **查询重写优化**
```sql
-- 避免使用SELECT *
-- ❌ 不推荐
SELECT * FROM financial_data WHERE year = 2024;

-- ✅ 推荐
SELECT account_code, account_name, credit_amount
FROM financial_data WHERE year = 2024;

-- 使用EXISTS替代IN
-- ❌ 不推荐
SELECT * FROM departments WHERE id IN (
    SELECT dept_id FROM financial_data WHERE year = 2024
);

-- ✅ 推荐
SELECT * FROM departments d WHERE EXISTS (
    SELECT 1 FROM financial_data f
    WHERE f.dept_id = d.id AND f.year = 2024
);
```

3. **分页查询优化**
```sql
-- 使用游标分页替代OFFSET
-- ❌ 不推荐（大偏移量性能差）
SELECT * FROM financial_data ORDER BY id LIMIT 1000 OFFSET 50000;

-- ✅ 推荐（游标分页）
SELECT * FROM financial_data WHERE id > 50000 ORDER BY id LIMIT 1000;
```

#### 缓存优化最佳实践

1. **缓存键设计**
```python
def generate_cache_key(query_type, params):
    """生成缓存键"""
    key_parts = [
        'chatdb',
        query_type,
        hashlib.md5(json.dumps(params, sort_keys=True).encode()).hexdigest()[:8]
    ]
    return ':'.join(key_parts)

# 示例
cache_key = generate_cache_key('text2sql', {
    'connection_id': 1,
    'query': '查询收入情况',
    'year': 2024
})
# 结果: chatdb:text2sql:a1b2c3d4
```

2. **缓存失效策略**
```python
class CacheManager:
    def __init__(self):
        self.cache = redis.Redis()

    def set_with_tags(self, key, value, ttl, tags):
        """设置带标签的缓存"""
        self.cache.setex(key, ttl, value)

        # 为每个标签添加键引用
        for tag in tags:
            self.cache.sadd(f"tag:{tag}", key)
            self.cache.expire(f"tag:{tag}", ttl)

    def invalidate_by_tag(self, tag):
        """根据标签失效缓存"""
        keys = self.cache.smembers(f"tag:{tag}")
        if keys:
            self.cache.delete(*keys)
            self.cache.delete(f"tag:{tag}")

# 使用示例
cache_manager = CacheManager()
cache_manager.set_with_tags(
    key="query:revenue:2024",
    value=query_result,
    ttl=3600,
    tags=["revenue", "2024", "financial_data"]
)

# 当financial_data表更新时，失效相关缓存
cache_manager.invalidate_by_tag("financial_data")
```

---

## 安全与权限管理

### 🔒 安全架构

#### 多层安全防护
```
🛡️ 安全防护层次
├── 网络层安全
│   ├── HTTPS/TLS加密
│   ├── 防火墙配置
│   └── DDoS防护
├── 应用层安全
│   ├── 身份认证
│   ├── 权限控制
│   └── 输入验证
├── 数据层安全
│   ├── 数据加密
│   ├── 访问审计
│   └── 备份恢复
└── 运维层安全
    ├── 日志监控
    ├── 漏洞扫描
    └── 安全更新
```

#### 身份认证与授权

**JWT Token认证**
```python
from jose import JWTError, jwt
from datetime import datetime, timedelta

class AuthManager:
    def __init__(self, secret_key, algorithm="HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm

    def create_access_token(self, data: dict, expires_delta: timedelta = None):
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(hours=24)

        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt

    def verify_token(self, token: str):
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            username: str = payload.get("sub")
            if username is None:
                return None
            return username
        except JWTError:
            return None
```

**基于角色的访问控制(RBAC)**
```python
class RoleBasedAccessControl:
    def __init__(self):
        self.permissions = {
            'admin': [
                'connection:create', 'connection:read', 'connection:update', 'connection:delete',
                'schema:create', 'schema:read', 'schema:update', 'schema:delete',
                'query:execute', 'mapping:manage', 'user:manage'
            ],
            'analyst': [
                'connection:read', 'schema:read', 'query:execute', 'mapping:read'
            ],
            'viewer': [
                'connection:read', 'schema:read', 'query:execute'
            ]
        }

    def check_permission(self, user_role: str, required_permission: str) -> bool:
        """检查权限"""
        return required_permission in self.permissions.get(user_role, [])

    def get_user_permissions(self, user_role: str) -> list:
        """获取用户权限列表"""
        return self.permissions.get(user_role, [])
```

#### 数据安全保护

**敏感数据加密**
```python
from cryptography.fernet import Fernet
import base64

class DataEncryption:
    def __init__(self, key: bytes = None):
        if key is None:
            key = Fernet.generate_key()
        self.cipher_suite = Fernet(key)
        self.key = key

    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        encrypted_data = self.cipher_suite.encrypt(data.encode())
        return base64.b64encode(encrypted_data).decode()

    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        decrypted_data = self.cipher_suite.decrypt(encrypted_bytes)
        return decrypted_data.decode()

# 数据库密码加密存储
encryption = DataEncryption()
encrypted_password = encryption.encrypt_sensitive_data("database_password")
```

**SQL注入防护**
```python
from sqlalchemy import text
import re

class SQLInjectionProtection:
    def __init__(self):
        # 危险SQL关键词
        self.dangerous_keywords = [
            'DROP', 'DELETE', 'INSERT', 'UPDATE', 'ALTER', 'CREATE',
            'EXEC', 'EXECUTE', 'UNION', 'SCRIPT', '--', ';'
        ]

    def validate_query(self, query: str) -> bool:
        """验证查询安全性"""
        query_upper = query.upper()

        # 检查危险关键词
        for keyword in self.dangerous_keywords:
            if keyword in query_upper:
                return False

        # 只允许SELECT查询
        if not query_upper.strip().startswith('SELECT'):
            return False

        return True

    def sanitize_input(self, user_input: str) -> str:
        """清理用户输入"""
        # 移除潜在危险字符
        sanitized = re.sub(r'[;\'\"\\]', '', user_input)
        return sanitized.strip()
```

#### 访问审计日志

**审计日志记录**
```python
import logging
from datetime import datetime
from sqlalchemy.orm import Session

class AuditLogger:
    def __init__(self, db: Session):
        self.db = db
        self.logger = logging.getLogger('audit')

    def log_query_execution(self, user_id: int, query: str, connection_id: int,
                          success: bool, execution_time: float):
        """记录查询执行日志"""
        audit_log = AuditLog(
            user_id=user_id,
            action='QUERY_EXECUTION',
            resource_type='DATABASE',
            resource_id=connection_id,
            details={
                'query': query[:500],  # 限制长度
                'success': success,
                'execution_time': execution_time
            },
            timestamp=datetime.utcnow(),
            ip_address=self.get_client_ip()
        )

        self.db.add(audit_log)
        self.db.commit()

        # 同时记录到日志文件
        self.logger.info(f"Query executed by user {user_id}: {success}")

    def log_data_access(self, user_id: int, table_name: str, operation: str):
        """记录数据访问日志"""
        audit_log = AuditLog(
            user_id=user_id,
            action='DATA_ACCESS',
            resource_type='TABLE',
            resource_id=table_name,
            details={'operation': operation},
            timestamp=datetime.utcnow()
        )

        self.db.add(audit_log)
        self.db.commit()
```

---

## 常见问题解答

### ❓ 安装部署问题

#### Q1: Docker启动失败，提示端口被占用
**问题描述**: 执行`docker-compose up`时提示端口3000或8000被占用

**解决方案**:
```bash
# 1. 检查端口占用情况
netstat -tlnp | grep :3000
netstat -tlnp | grep :8000

# 2. 停止占用端口的进程
sudo kill -9 <PID>

# 3. 或者修改docker-compose.yml中的端口映射
ports:
  - "3001:3000"  # 前端改为3001
  - "8001:8000"  # 后端改为8001
```

#### Q2: 数据库连接失败
**问题描述**: 系统提示无法连接到数据库

**解决方案**:
```bash
# 1. 检查数据库文件是否存在
ls -la fin_data.db resource.db

# 2. 检查文件权限
chmod 644 *.db

# 3. 验证数据库完整性
sqlite3 fin_data.db "PRAGMA integrity_check;"

# 4. 检查环境变量配置
cat chatdb/.env | grep DB_PATH
```

#### Q3: Neo4j连接超时
**问题描述**: 图数据可视化功能无法加载，提示Neo4j连接超时

**解决方案**:
```bash
# 1. 检查Neo4j服务状态
docker ps | grep neo4j

# 2. 重启Neo4j容器
docker restart chatdb-neo4j

# 3. 检查Neo4j日志
docker logs chatdb-neo4j

# 4. 验证连接配置
curl http://localhost:7474/browser/
```

### 🤖 智能查询问题

#### Q4: 查询结果不准确
**问题描述**: 自然语言查询生成的SQL不符合预期

**解决方案**:
1. **检查数据映射**:
   ```
   进入"数据映射"页面，确认关键术语的映射是否正确
   例如："收入" → "主营业务收入"
   ```

2. **优化查询描述**:
   ```
   ❌ 模糊描述: "查询数据"
   ✅ 具体描述: "查询2024年9月的主营业务收入"
   ```

3. **检查元数据**:
   ```python
   # 验证元数据是否正确加载
   python -c "
   from app.services.text2sql_utils import get_financial_metadata
   metadata = get_financial_metadata()
   print(f'元数据状态: {metadata.get(\"has_metadata\", False)}')
   "
   ```

#### Q5: 查询响应时间过长
**问题描述**: 复杂查询执行时间超过10秒

**解决方案**:
1. **添加数据库索引**:
   ```sql
   CREATE INDEX idx_financial_date ON financial_data(year, month);
   CREATE INDEX idx_financial_account ON financial_data(account_code);
   ```

2. **启用查询缓存**:
   ```bash
   # 在.env文件中启用缓存
   ENABLE_CACHE=true
   CACHE_TTL=3600
   ```

3. **优化查询条件**:
   ```
   添加时间范围限制，避免全表扫描
   使用具体的科目编号而不是模糊匹配
   ```

### 📊 数据建模问题

#### Q6: 表关系检测不准确
**问题描述**: 自动发现的表关系类型不正确

**解决方案**:
1. **手动调整关系**:
   ```
   在数据建模页面，双击关系线
   手动选择正确的关系类型（1:1, 1:N, N:1, N:M）
   ```

2. **检查外键约束**:
   ```sql
   -- 查看表的外键约束
   PRAGMA foreign_key_list(financial_data);
   ```

3. **更新关系描述**:
   ```
   为每个关系添加清晰的业务描述
   例如："财务数据属于部门"
   ```

#### Q7: Neo4j同步失败
**问题描述**: 数据模式无法同步到Neo4j图数据库

**解决方案**:
```bash
# 1. 检查Neo4j服务状态
docker exec chatdb-neo4j cypher-shell -u neo4j -p password "RETURN 1"

# 2. 清理Neo4j数据重新同步
docker exec chatdb-neo4j cypher-shell -u neo4j -p password "MATCH (n) DETACH DELETE n"

# 3. 重新发现并同步结构
curl -X POST "http://localhost:8000/api/schema/1/discover-and-sync"
```

### 🔧 系统性能问题

#### Q8: 系统响应缓慢
**问题描述**: 整体系统响应时间较长

**解决方案**:
1. **检查系统资源**:
   ```bash
   # 检查CPU和内存使用
   top -p $(pgrep -f uvicorn)

   # 检查磁盘I/O
   iostat -x 1
   ```

2. **优化数据库连接池**:
   ```python
   # 调整连接池参数
   POOL_SIZE = 20
   MAX_OVERFLOW = 50
   POOL_TIMEOUT = 60
   ```

3. **启用缓存**:
   ```bash
   # 启用Redis缓存
   REDIS_URL=redis://localhost:6379
   ENABLE_CACHE=true
   ```

#### Q9: 内存使用过高
**问题描述**: 系统内存占用持续增长

**解决方案**:
```python
# 1. 定期清理缓存
import gc
gc.collect()

# 2. 限制查询结果集大小
MAX_QUERY_RESULTS = 10000

# 3. 使用流式处理大数据集
def stream_large_results(query):
    for batch in execute_query_in_batches(query, batch_size=1000):
        yield batch
```

### 🔒 安全相关问题

#### Q10: 忘记管理员密码
**问题描述**: 无法登录管理员账户

**解决方案**:
```python
# 重置管理员密码
python -c "
from app.core.security import get_password_hash
from app.db.session import SessionLocal
from app.models.user import User

db = SessionLocal()
admin_user = db.query(User).filter(User.username == 'admin').first()
if admin_user:
    admin_user.hashed_password = get_password_hash('new_password')
    db.commit()
    print('管理员密码已重置为: new_password')
else:
    print('未找到管理员用户')
db.close()
"
```

---

## 故障排除指南

### 🚨 系统启动故障

#### 后端服务启动失败

**故障现象**:
```
ERROR: Could not start server
ModuleNotFoundError: No module named 'app'
```

**排查步骤**:
```bash
# 1. 检查Python环境
python --version
which python

# 2. 检查依赖安装
pip list | grep fastapi
pip list | grep sqlalchemy

# 3. 检查工作目录
pwd
ls -la app/

# 4. 重新安装依赖
pip install -r requirements.txt

# 5. 检查Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

**解决方案**:
```bash
# 完整的启动脚本
#!/bin/bash
cd chatdb/backend
source venv/bin/activate
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端服务启动失败

**故障现象**:
```
npm ERR! code ELIFECYCLE
npm ERR! errno 1
```

**排查步骤**:
```bash
# 1. 检查Node.js版本
node --version
npm --version

# 2. 清理缓存和依赖
rm -rf node_modules package-lock.json
npm cache clean --force

# 3. 重新安装依赖
npm install

# 4. 检查端口占用
lsof -i :3000

# 5. 使用不同端口启动
PORT=3001 npm start
```

### 🔍 数据库连接故障

#### SQLite数据库锁定

**故障现象**:
```
sqlite3.OperationalError: database is locked
```

**排查步骤**:
```bash
# 1. 检查数据库文件权限
ls -la *.db

# 2. 查找占用数据库的进程
lsof fin_data.db

# 3. 检查数据库完整性
sqlite3 fin_data.db "PRAGMA integrity_check;"

# 4. 检查WAL文件
ls -la *.db-wal *.db-shm
```

**解决方案**:
```bash
# 1. 停止所有相关进程
pkill -f "python.*main.py"

# 2. 清理WAL文件
rm -f *.db-wal *.db-shm

# 3. 修复数据库
sqlite3 fin_data.db "PRAGMA wal_checkpoint(FULL);"

# 4. 重新启动服务
python main.py
```

#### MySQL连接超时

**故障现象**:
```
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server")
```

**排查步骤**:
```bash
# 1. 检查MySQL服务状态
systemctl status mysql
docker ps | grep mysql

# 2. 测试网络连接
telnet mysql_host 3306
ping mysql_host

# 3. 检查防火墙设置
sudo ufw status
iptables -L

# 4. 验证认证信息
mysql -h mysql_host -u username -p
```

### 🤖 AI服务故障

#### LLM API调用失败

**故障现象**:
```
OpenAI API Error: Rate limit exceeded
```

**排查步骤**:
```bash
# 1. 检查API密钥
echo $OPENAI_API_KEY

# 2. 测试API连接
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models

# 3. 检查请求频率
grep "API调用" logs/app.log | tail -20

# 4. 检查余额和限制
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/usage
```

**解决方案**:
```python
# 实现API调用重试机制
import time
import random
from functools import wraps

def retry_with_backoff(max_retries=3, base_delay=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e

                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    time.sleep(delay)

        return wrapper
    return decorator

@retry_with_backoff(max_retries=3, base_delay=2)
def call_llm_api(prompt):
    # LLM API调用逻辑
    pass
```

### 📊 性能故障诊断

#### 查询性能问题

**故障现象**:
查询执行时间超过30秒

**诊断工具**:
```python
import time
import logging
from functools import wraps

def performance_monitor(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time

            if execution_time > 5:  # 超过5秒记录警告
                logging.warning(f"Slow query detected: {func.__name__} took {execution_time:.2f}s")

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logging.error(f"Query failed after {execution_time:.2f}s: {str(e)}")
            raise

    return wrapper

@performance_monitor
def execute_text2sql_query(query, connection):
    # 查询执行逻辑
    pass
```

**优化方案**:
```sql
-- 1. 分析查询执行计划
EXPLAIN QUERY PLAN
SELECT SUM(credit_amount)
FROM financial_data
WHERE year = 2024 AND account_code LIKE '60%';

-- 2. 添加合适的索引
CREATE INDEX idx_financial_year_account
ON financial_data(year, account_code);

-- 3. 使用分区表（大数据量场景）
CREATE TABLE financial_data_2024
PARTITION OF financial_data
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

#### 内存泄漏问题

**故障现象**:
系统内存使用持续增长，最终导致OOM

**诊断工具**:
```python
import psutil
import gc
import tracemalloc

class MemoryMonitor:
    def __init__(self):
        tracemalloc.start()
        self.baseline = None

    def take_snapshot(self):
        """获取内存快照"""
        snapshot = tracemalloc.take_snapshot()
        if self.baseline is None:
            self.baseline = snapshot
            return

        top_stats = snapshot.compare_to(self.baseline, 'lineno')

        print("内存增长最多的10个位置:")
        for stat in top_stats[:10]:
            print(f"{stat.traceback.format()[-1]}: +{stat.size_diff / 1024 / 1024:.2f} MB")

    def get_memory_usage(self):
        """获取当前内存使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()

        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
            'percent': process.memory_percent(),
            'objects': len(gc.get_objects())
        }

# 使用示例
monitor = MemoryMonitor()

# 在关键位置检查内存
def check_memory():
    usage = monitor.get_memory_usage()
    if usage['percent'] > 80:  # 内存使用超过80%
        logging.warning(f"High memory usage: {usage}")
        gc.collect()  # 强制垃圾回收
```

---

## 最佳实践建议

### 💡 系统使用最佳实践

#### 查询优化最佳实践

**1. 查询描述规范**
```
✅ 推荐的查询格式:
- 明确时间范围: "查询2024年9月的收入情况"
- 指定数据类型: "统计主营业务收入"
- 包含分组维度: "按部门分析费用支出"
- 使用标准术语: "计算资产负债率"

❌ 避免的查询格式:
- 过于模糊: "查询数据"
- 缺少时间: "收入情况"
- 术语不准: "钱的情况"
- 逻辑矛盾: "收入的支出"
```

**2. 数据映射管理**
```python
# 建立标准的财务术语映射
STANDARD_MAPPINGS = {
    # 收入类
    "收入": "主营业务收入",
    "营收": "主营业务收入",
    "销售收入": "主营业务收入",

    # 成本费用类
    "成本": "主营业务成本",
    "费用": "管理费用",
    "支出": "管理费用",

    # 资产负债类
    "资产": "资产总计",
    "负债": "负债总计",
    "现金": "货币资金"
}
```

**3. 查询结果验证**
```python
def validate_query_result(query, result):
    """验证查询结果合理性"""
    validations = []

    # 检查数值合理性
    if "收入" in query and result:
        for row in result:
            if any(value < 0 for value in row.values() if isinstance(value, (int, float))):
                validations.append("警告：收入数据出现负值")

    # 检查数据完整性
    if len(result) == 0:
        validations.append("提示：查询未返回任何数据，请检查查询条件")

    # 检查异常值
    if len(result) > 10000:
        validations.append("提示：结果集较大，建议添加限制条件")

    return validations
```

#### 数据建模最佳实践

**1. 表关系设计原则**
```
🏗️ 关系设计原则:
├── 一对一关系 (1:1)
│   └── 用于扩展表，如员工基本信息和详细信息
├── 一对多关系 (1:N)
│   └── 最常见，如部门对员工，客户对订单
├── 多对一关系 (N:1)
│   └── 从子表角度看的一对多关系
└── 多对多关系 (N:M)
    └── 需要中间表，如学生和课程关系
```

**2. 元数据管理规范**
```python
# 表描述规范
TABLE_DESCRIPTION_TEMPLATE = {
    "table_name": "financial_data",
    "display_name": "财务数据表",
    "description": "存储企业日常财务交易数据，包括收入、支出、资产变动等",
    "business_purpose": "支持财务报表生成和经营分析",
    "data_source": "财务系统自动导入",
    "update_frequency": "每日",
    "data_quality": "高",
    "owner": "财务部门"
}

# 字段描述规范
COLUMN_DESCRIPTION_TEMPLATE = {
    "column_name": "credit_amount",
    "display_name": "贷方金额",
    "description": "会计分录的贷方发生额，通常表示收入、负债增加或资产减少",
    "data_type": "DECIMAL(15,2)",
    "business_rules": ["收入类科目使用贷方", "金额不能为负数"],
    "examples": ["主营业务收入: 1000000.00", "应付账款: 50000.00"]
}
```

#### 性能优化最佳实践

**1. 数据库索引策略**
```sql
-- 基础索引策略
-- 1. 主键索引（自动创建）
-- 2. 外键索引
CREATE INDEX idx_financial_dept_id ON financial_data(dept_id);

-- 3. 查询频繁的字段组合索引
CREATE INDEX idx_financial_date_account ON financial_data(year, month, account_code);

-- 4. 部分索引（条件索引）
CREATE INDEX idx_financial_revenue ON financial_data(account_code, credit_amount)
WHERE account_code LIKE '60%' AND credit_amount > 0;

-- 5. 覆盖索引（包含所需的所有列）
CREATE INDEX idx_financial_summary ON financial_data(year, month, account_code)
INCLUDE (credit_amount, debit_amount);
```

**2. 缓存策略分层**
```python
class CacheStrategy:
    def __init__(self):
        self.cache_levels = {
            'L1_MEMORY': {
                'ttl': 300,      # 5分钟
                'max_size': 1000,
                'use_for': ['metadata', 'user_sessions']
            },
            'L2_REDIS': {
                'ttl': 3600,     # 1小时
                'max_size': 10000,
                'use_for': ['query_results', 'schema_info']
            },
            'L3_DATABASE': {
                'ttl': 86400,    # 24小时
                'max_size': None,
                'use_for': ['aggregated_data', 'reports']
            }
        }

    def get_cache_key(self, cache_type, params):
        """生成缓存键"""
        import hashlib
        param_str = json.dumps(params, sort_keys=True)
        hash_key = hashlib.md5(param_str.encode()).hexdigest()[:8]
        return f"chatdb:{cache_type}:{hash_key}"

    def should_cache(self, query_type, execution_time):
        """判断是否应该缓存"""
        # 执行时间超过1秒的查询都应该缓存
        if execution_time > 1.0:
            return True

        # 聚合查询应该缓存
        if query_type in ['aggregation', 'summary', 'report']:
            return True

        return False
```

#### 安全管理最佳实践

**1. 访问控制策略**
```python
class SecurityPolicy:
    def __init__(self):
        self.access_rules = {
            'data_access': {
                'financial_data': ['admin', 'finance_manager', 'analyst'],
                'employee_data': ['admin', 'hr_manager'],
                'system_config': ['admin']
            },
            'operation_permissions': {
                'create_connection': ['admin'],
                'modify_schema': ['admin', 'data_manager'],
                'execute_query': ['admin', 'analyst', 'viewer'],
                'export_data': ['admin', 'analyst']
            }
        }

    def check_data_access(self, user_role, table_name):
        """检查数据访问权限"""
        allowed_roles = self.access_rules['data_access'].get(table_name, [])
        return user_role in allowed_roles

    def audit_access(self, user_id, action, resource, success):
        """记录访问审计"""
        audit_record = {
            'timestamp': datetime.utcnow(),
            'user_id': user_id,
            'action': action,
            'resource': resource,
            'success': success,
            'ip_address': self.get_client_ip()
        }

        # 记录到审计日志
        self.log_audit_event(audit_record)
```

**2. 数据脱敏策略**
```python
class DataMasking:
    def __init__(self):
        self.masking_rules = {
            'phone': lambda x: x[:3] + '****' + x[-4:] if len(x) >= 7 else '****',
            'email': lambda x: x.split('@')[0][:2] + '***@' + x.split('@')[1],
            'id_card': lambda x: x[:6] + '********' + x[-4:] if len(x) >= 10 else '****',
            'bank_account': lambda x: '****' + x[-4:] if len(x) >= 4 else '****'
        }

    def mask_sensitive_data(self, data, field_type):
        """脱敏敏感数据"""
        if field_type in self.masking_rules:
            return self.masking_rules[field_type](str(data))
        return data

    def apply_masking_to_results(self, results, sensitive_fields):
        """对查询结果应用脱敏"""
        masked_results = []

        for row in results:
            masked_row = {}
            for key, value in row.items():
                if key in sensitive_fields:
                    field_type = sensitive_fields[key]
                    masked_row[key] = self.mask_sensitive_data(value, field_type)
                else:
                    masked_row[key] = value
            masked_results.append(masked_row)

        return masked_results
```

### 🎯 运维管理最佳实践

#### 监控告警策略

**1. 关键指标监控**
```python
class SystemMonitor:
    def __init__(self):
        self.thresholds = {
            'response_time': {'warning': 2.0, 'critical': 5.0},
            'error_rate': {'warning': 0.01, 'critical': 0.05},
            'cpu_usage': {'warning': 0.7, 'critical': 0.85},
            'memory_usage': {'warning': 0.8, 'critical': 0.9},
            'disk_usage': {'warning': 0.8, 'critical': 0.9},
            'connection_pool': {'warning': 0.8, 'critical': 0.95}
        }

    def check_system_health(self):
        """检查系统健康状态"""
        health_status = {}

        # 检查响应时间
        avg_response_time = self.get_average_response_time()
        health_status['response_time'] = self.evaluate_metric(
            avg_response_time, self.thresholds['response_time']
        )

        # 检查错误率
        error_rate = self.get_error_rate()
        health_status['error_rate'] = self.evaluate_metric(
            error_rate, self.thresholds['error_rate']
        )

        # 检查资源使用
        cpu_usage = psutil.cpu_percent()
        memory_usage = psutil.virtual_memory().percent / 100

        health_status['cpu_usage'] = self.evaluate_metric(
            cpu_usage / 100, self.thresholds['cpu_usage']
        )
        health_status['memory_usage'] = self.evaluate_metric(
            memory_usage, self.thresholds['memory_usage']
        )

        return health_status

    def evaluate_metric(self, value, thresholds):
        """评估指标状态"""
        if value >= thresholds['critical']:
            return {'status': 'critical', 'value': value}
        elif value >= thresholds['warning']:
            return {'status': 'warning', 'value': value}
        else:
            return {'status': 'normal', 'value': value}
```

**2. 自动化运维脚本**
```bash
#!/bin/bash
# 系统健康检查脚本

LOG_FILE="/var/log/chatdb/health_check.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

echo "[$DATE] 开始系统健康检查" >> $LOG_FILE

# 检查服务状态
check_service() {
    local service_name=$1
    local port=$2

    if curl -f -s "http://localhost:$port/health" > /dev/null; then
        echo "[$DATE] ✅ $service_name 服务正常" >> $LOG_FILE
        return 0
    else
        echo "[$DATE] ❌ $service_name 服务异常" >> $LOG_FILE
        return 1
    fi
}

# 检查磁盘空间
check_disk_space() {
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')

    if [ $usage -gt 90 ]; then
        echo "[$DATE] ❌ 磁盘空间不足: ${usage}%" >> $LOG_FILE
        return 1
    elif [ $usage -gt 80 ]; then
        echo "[$DATE] ⚠️ 磁盘空间警告: ${usage}%" >> $LOG_FILE
        return 0
    else
        echo "[$DATE] ✅ 磁盘空间正常: ${usage}%" >> $LOG_FILE
        return 0
    fi
}

# 执行检查
check_service "Backend" 8000
backend_status=$?

check_service "Frontend" 3000
frontend_status=$?

check_disk_space
disk_status=$?

# 发送告警（如果需要）
if [ $backend_status -ne 0 ] || [ $frontend_status -ne 0 ] || [ $disk_status -ne 0 ]; then
    echo "[$DATE] 系统存在异常，请检查日志" >> $LOG_FILE
    # 这里可以添加邮件或短信告警逻辑
fi

echo "[$DATE] 健康检查完成" >> $LOG_FILE
```

通过以上详细的用户操作手册，用户可以全面了解和掌握智能数据分析系统的各项功能，从安装部署到日常使用，从基础操作到高级优化，都有详细的指导和最佳实践建议。这份手册将帮助不同角色的用户快速上手并充分发挥系统的价值。
