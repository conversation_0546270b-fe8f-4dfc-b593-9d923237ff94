import sqlite3

# 测试字段映射
metadata_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
conn = sqlite3.connect(metadata_db)
cursor = conn.cursor()

print('=== 测试字段映射 ===')
cursor.execute('SELECT COUNT(*) FROM valuemapping')
count = cursor.fetchone()[0]
print(f'总共有 {count} 个字段映射')

# 测试关键映射
test_mappings = ['company_id', '公司', '收入']
for term in test_mappings:
    cursor.execute('SELECT vm.nl_term, sc.column_name FROM valuemapping vm JOIN schemacolumn sc ON vm.column_id = sc.id WHERE vm.nl_term = ?', (term,))
    results = cursor.fetchall()
    if results:
        print(f'✅ "{term}" -> {results[0][1]}')
    else:
        print(f'❌ "{term}" 没有找到映射')

conn.close()

# 测试实际查询
business_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/fin_data.db'
conn = sqlite3.connect(business_db)
cursor = conn.cursor()

print('\n=== 测试实际查询 ===')
try:
    cursor.execute('''
        SELECT accounting_unit_name, 
               SUM(CAST(debit_amount AS REAL)) as total_amount
        FROM financial_data 
        WHERE year = 2024 AND month = 5
          AND account_code LIKE '64%'
        GROUP BY accounting_unit_name
        LIMIT 3
    ''')
    
    results = cursor.fetchall()
    print('✅ 查询成功执行')
    for row in results:
        print(f'  {row[0]}: {row[1]}')
        
except Exception as e:
    print(f'❌ 查询失败: {e}')

conn.close()
