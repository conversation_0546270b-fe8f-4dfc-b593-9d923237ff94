#!/usr/bin/env python3
"""
测试全面的日志监控系统
"""

import requests
import json
import time

def test_logging_system():
    """测试日志系统"""
    
    print("🔍 测试Text2SQL日志监控系统")
    print("=" * 60)
    
    base_url = "http://localhost:8000/api"
    
    # 1. 测试系统状态
    print("1️⃣ 测试系统状态...")
    try:
        response = requests.get(f"{base_url}/debug/system-status")
        if response.status_code == 200:
            status = response.json()
            print(f"   ✅ 系统状态: {status['status']}")
            print(f"   📄 日志文件存在: {status['logging']['log_file_exists']}")
            print(f"   📊 日志文件大小: {status['logging']['log_file_size_bytes']} bytes")
        else:
            print(f"   ❌ 获取系统状态失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        return
    
    # 2. 测试字段映射诊断
    print(f"\n2️⃣ 测试字段映射诊断...")
    try:
        response = requests.get(f"{base_url}/debug/field-mappings/fin_data")
        if response.status_code == 200:
            mappings = response.json()
            print(f"   ✅ 连接: {mappings['connection_name']}")
            print(f"   📊 总字段数: {mappings['statistics']['total_fields']}")
            print(f"   🔗 已映射字段: {mappings['statistics']['mapped_fields']}")
            print(f"   📋 总映射数: {mappings['statistics']['total_mappings']}")
            
            # 显示关键映射
            if mappings['key_mappings']:
                print(f"   🔑 关键映射:")
                for field, key_maps in mappings['key_mappings'].items():
                    print(f"      {field}: {key_maps}")
            else:
                print(f"   ⚠️ 没有找到关键映射")
        else:
            print(f"   ❌ 获取字段映射失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 3. 测试Text2SQL查询（这会触发详细日志）
    print(f"\n3️⃣ 测试Text2SQL查询（触发日志记录）...")
    try:
        test_query = "2024年1月各公司的销售费用合计"
        response = requests.post(
            f"{base_url}/debug/test-text2sql",
            json={
                "query": test_query,
                "connection_name": "fin_data"
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 查询: {result['query']}")
            print(f"   🔗 连接: {result['connection_name']}")
            print(f"   📝 生成SQL: {bool(result['result']['sql'])}")
            print(f"   ❌ 错误: {result['result']['error']}")
            print(f"   🆔 查询ID: {result['debug_info']['query_id']}")
            
            # 显示生成的SQL（如果有）
            if result['result']['sql']:
                print(f"   📄 生成的SQL:")
                print(f"      {result['result']['sql'][:200]}...")
        else:
            print(f"   ❌ Text2SQL查询失败: {response.status_code}")
            print(f"      {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 4. 获取最新日志
    print(f"\n4️⃣ 获取最新日志...")
    try:
        response = requests.get(f"{base_url}/debug/text2sql-logs?lines=20")
        if response.status_code == 200:
            logs = response.json()
            print(f"   ✅ 总日志行数: {logs['total_lines']}")
            print(f"   📄 返回行数: {logs['returned_lines']}")
            
            if logs['logs']:
                print(f"   📋 最新日志（最后5行）:")
                for line in logs['logs'][-5:]:
                    print(f"      {line}")
            else:
                print(f"   ⚠️ 没有日志内容")
        else:
            print(f"   ❌ 获取日志失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 5. 搜索特定日志
    print(f"\n5️⃣ 搜索问题字段相关日志...")
    try:
        response = requests.get(f"{base_url}/debug/text2sql-logs/search?keyword=company_id&lines=10")
        if response.status_code == 200:
            logs = response.json()
            print(f"   ✅ 匹配数: {logs['total_matches']}")
            print(f"   🔍 搜索条件: {logs['search_criteria']}")
            
            if logs['logs']:
                print(f"   📋 相关日志:")
                for line in logs['logs']:
                    print(f"      {line}")
            else:
                print(f"   ⚠️ 没有找到相关日志")
        else:
            print(f"   ❌ 搜索日志失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

def provide_usage_instructions():
    """提供使用说明"""
    
    print(f"\n" + "=" * 60)
    print("📖 日志监控系统使用说明")
    print("=" * 60)
    
    print("""
🔧 **可用的调试API端点**:

1️⃣ **系统状态检查**:
   GET /api/debug/system-status
   - 检查日志系统是否正常工作

2️⃣ **字段映射诊断**:
   GET /api/debug/field-mappings/{connection_name}
   - 查看指定连接的所有字段映射
   - 检查关键映射是否存在

3️⃣ **Text2SQL测试**:
   POST /api/debug/test-text2sql
   - 测试查询并获取详细调试信息
   - 返回查询ID用于日志追踪

4️⃣ **日志查看**:
   GET /api/debug/text2sql-logs?lines=100
   - 获取最新的日志内容

5️⃣ **日志搜索**:
   GET /api/debug/text2sql-logs/search?keyword=company_id
   GET /api/debug/text2sql-logs/search?query_id=query_123456
   - 搜索特定关键词或查询ID的日志

6️⃣ **Schema上下文检查**:
   GET /api/debug/schema-context/{connection_name}
   - 检查schema检索是否正常

---

🔍 **调试流程建议**:

1. 首先检查系统状态和字段映射
2. 执行测试查询获取查询ID
3. 使用查询ID搜索相关日志
4. 分析日志中的每个处理步骤
5. 定位问题所在的具体环节

---

📄 **日志文件位置**:
- 文件名: text2sql_debug.log
- 位置: 后端根目录
- 编码: UTF-8
- 自动轮转: 支持备份

---

🚨 **重要提示**:
- 日志包含详细的SQL和提示内容
- 生产环境中注意日志文件大小
- 可以使用 DELETE /api/debug/clear-logs 清理日志
""")

if __name__ == "__main__":
    test_logging_system()
    provide_usage_instructions()
    
    print(f"\n" + "=" * 60)
    print("🎯 下一步：重启后端服务，然后运行此测试脚本")
    print("=" * 60)
