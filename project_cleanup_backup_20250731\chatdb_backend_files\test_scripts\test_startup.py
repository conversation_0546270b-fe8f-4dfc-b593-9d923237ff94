#!/usr/bin/env python3
"""
测试启动问题
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有导入"""
    print("🔍 测试导入...")
    
    try:
        print("1. 导入 api_router...")
        from app.api.api_v1.api import api_router
        print("   ✅ api_router 导入成功")
        
        print("2. 导入 settings...")
        from app.core.config import settings
        print("   ✅ settings 导入成功")
        
        print("3. 导入 startup_sequence...")
        from app.core.startup import startup_sequence
        print("   ✅ startup_sequence 导入成功")
        
        print("4. 导入 pipeline_monitor...")
        from app.utils.pipeline_monitor import pipeline_monitor
        print("   ✅ pipeline_monitor 导入成功")
        
        print("5. 导入 debug API...")
        from app.api.v1.debug import router
        print("   ✅ debug API 导入成功")
        
        print("6. 创建 FastAPI 应用...")
        from fastapi import FastAPI
        from fastapi.middleware.cors import CORSMiddleware
        
        app = FastAPI(
            title="ChatDB API",
            description="智能数据分析系统API",
            version="0.1.0"
        )
        
        # 添加CORS中间件
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 添加路由
        app.include_router(api_router, prefix="/api")
        
        print("   ✅ FastAPI 应用创建成功")
        
        print("\n✅ 所有导入和初始化成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_uvicorn_start():
    """测试uvicorn启动"""
    print("\n🚀 测试uvicorn启动...")
    
    try:
        import uvicorn
        print("   ✅ uvicorn 导入成功")
        
        # 尝试创建应用实例
        from main import app
        print("   ✅ main.app 导入成功")
        
        print("   🔧 准备启动服务器...")
        print("   📍 地址: http://0.0.0.0:8000")
        print("   🔄 重载模式: 启用")
        
        # 启动服务器
        uvicorn.run(
            "main:app", 
            host="0.0.0.0", 
            port=8000, 
            reload=True,
            log_level="info"
        )
        
    except Exception as e:
        print(f"   ❌ uvicorn 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 ChatDB 后端启动诊断")
    print("=" * 50)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试启动
        test_uvicorn_start()
    else:
        print("\n❌ 导入失败，无法启动服务器")
