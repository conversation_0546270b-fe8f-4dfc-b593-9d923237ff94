#!/usr/bin/env python3
"""
补充缺失的字段映射
"""

import sqlite3

def add_missing_mappings():
    """添加缺失的字段映射"""
    
    metadata_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(metadata_db)
    cursor = conn.cursor()
    
    print("🔄 开始添加缺失的字段映射...")
    
    # 获取financial_data表的column信息
    cursor.execute('''
        SELECT sc.id, sc.column_name 
        FROM schemacolumn sc 
        JOIN schematable st ON sc.table_id = st.id 
        WHERE st.table_name = 'financial_data' AND st.connection_id = 1
    ''')
    columns = cursor.fetchall()
    column_dict = {col[1]: col[0] for col in columns}
    
    # 定义缺失的映射关系
    missing_mappings = {
        # 公司名称相关
        'accounting_unit_name': [
            'company_name', '公司名称', '企业名称', '单位名称', 
            'organization', 'organization_name', '机构名称'
        ],
        
        # 日期相关 - 特殊处理
        'year': ['年份', 'year', '年度'],
        'month': ['月份', 'month', '月'],
        
        # 科目相关
        'account_full_name': [
            'account_name', '科目名称', '会计科目', '科目', 
            'subject', 'subject_name'
        ],
        
        # 金额相关
        'debit_amount': [
            'expense', 'expenses', '费用', '支出', '成本', 
            'cost', 'expenditure', 'sales_expense', '销售费用',
            'management_expense', '管理费用', 'admin_expense'
        ],
        
        'credit_amount': [
            'income', 'revenue', '收入', '营收', '销售收入',
            'sales_revenue', 'operating_income', '营业收入'
        ]
    }
    
    # 添加映射
    for column_name, nl_terms in missing_mappings.items():
        if column_name in column_dict:
            column_id = column_dict[column_name]
            
            for nl_term in nl_terms:
                # 检查映射是否已存在
                cursor.execute(
                    'SELECT id FROM valuemapping WHERE column_id = ? AND nl_term = ?',
                    (column_id, nl_term)
                )
                
                if not cursor.fetchone():
                    # 添加新的映射关系
                    cursor.execute('''
                        INSERT INTO valuemapping (column_id, nl_term, db_value, created_at)
                        VALUES (?, ?, ?, datetime('now'))
                    ''', (column_id, nl_term, column_name))
                    
                    print(f"✅ 添加映射: '{nl_term}' -> {column_name}")
    
    # 添加特殊的日期组合映射说明
    print("\n🔄 添加特殊日期处理说明...")
    
    cursor.execute('''
        INSERT OR REPLACE INTO meta_column_descriptions 
        (table_name, column_name, chinese_name, description, ai_understanding_points)
        VALUES 
        ('financial_data', 'date_combination', '日期组合字段', 
         '当用户查询涉及日期时，需要同时使用year和month字段', 
         '重要：不存在date字段！用户提到日期时，必须使用year和month字段组合查询。例如：WHERE year = 2024 AND month = 2')
    ''')
    
    # 添加公司名称字段的特殊说明
    cursor.execute('''
        INSERT OR REPLACE INTO meta_column_descriptions 
        (table_name, column_name, chinese_name, description, ai_understanding_points)
        VALUES 
        ('financial_data', 'company_name_mapping', '公司名称映射', 
         '公司相关查询必须使用accounting_unit_name字段', 
         '重要：不存在company_name或company_id字段！用户提到公司时，必须使用accounting_unit_name字段')
    ''')
    
    conn.commit()
    conn.close()
    
    print("✅ 缺失字段映射添加完成!")

if __name__ == "__main__":
    add_missing_mappings()
