import React, { useState, useEffect, useCallback } from 'react';
import {
  Card, Select, Button, Table, Spin, message,
  Typography, Form, Input, Modal, Popconfirm, Space
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import * as api from '../services/api';

const { Option } = Select;
const { Title } = Typography;

interface DBConnection {
  id: number;
  name: string;
}

interface SchemaTable {
  id: number;
  table_name: string;
  description?: string;
}

interface SchemaColumn {
  id: number;
  column_name: string;
  data_type: string;
  description?: string;
  table_id: number;
  table_name: string;
}

interface ValueMapping {
  id: number;
  column_id: number;
  nl_term: string;
  db_value: string;
}

const ValueMappingsPage: React.FC = () => {
  const [connections, setConnections] = useState<DBConnection[]>([]);
  const [selectedConnection, setSelectedConnection] = useState<number | null>(null);
  const [tables, setTables] = useState<SchemaTable[]>([]);
  const [selectedTable, setSelectedTable] = useState<number | null>(null);
  const [columns, setColumns] = useState<SchemaColumn[]>([]);
  const [selectedColumn, setSelectedColumn] = useState<number | null>(null);
  const [valueMappings, setValueMappings] = useState<ValueMapping[]>([]);
  const [totalMappings, setTotalMappings] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [editingMapping, setEditingMapping] = useState<ValueMapping | null>(null);
  const [form] = Form.useForm();

  // 新增：显示模式切换
  const [viewMode, setViewMode] = useState<'hierarchy' | 'direct'>('direct');

  // 搜索功能
  const [searchText, setSearchText] = useState<string>('');

  // Define fetch functions with useCallback
  const fetchConnections = async () => {
    try {
      const response = await api.getConnections();
      setConnections(response.data);
    } catch (error) {
      message.error('获取连接失败');
      console.error(error);
    }
  };

  // 获取总的映射数量（用于显示提示信息）
  const fetchTotalMappings = async () => {
    try {
      const response = await api.getValueMappings();
      setTotalMappings(response.data.length);
    } catch (error) {
      console.error('获取映射总数失败:', error);
    }
  };

  const fetchTables = async (connectionId: number) => {
    setLoading(true);
    try {
      const response = await api.getSchemaMetadata(connectionId);
      setTables(response.data);
      setSelectedTable(null);
      setColumns([]);
      setSelectedColumn(null);
      setValueMappings([]);
    } catch (error) {
      message.error('获取表失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const fetchColumns = useCallback(async (tableId: number) => {
    setLoading(true);
    try {
      const selectedTableData = tables.find(t => t.id === tableId);
      if (!selectedTableData) return;

      // Get columns for this table from the schema metadata
      const response = await api.getSchemaMetadata(selectedConnection!);
      const tableData = response.data.find((t: any) => t.id === tableId);

      if (tableData && tableData.columns) {
        const columnsWithTableInfo = tableData.columns.map((col: any) => ({
          ...col,
          table_id: tableId,
          table_name: selectedTableData.table_name
        }));
        setColumns(columnsWithTableInfo);
      } else {
        setColumns([]);
      }

      setSelectedColumn(null);
      setValueMappings([]);
    } catch (error) {
      message.error('获取列失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [selectedConnection, tables]);

  const fetchValueMappings = async (columnId: number) => {
    setLoading(true);
    try {
      // 特殊处理：如果选择的是valuemapping表，直接获取所有映射数据
      const selectedTableData = tables.find(t => t.id === selectedTable);
      if (selectedTableData?.table_name === 'valuemapping') {
        // 获取所有映射数据，不按column_id过滤
        const response = await api.getValueMappings();
        setValueMappings(response.data);
        setTotalMappings(response.data.length);
      } else {
        // 正常的按column_id过滤逻辑
        const response = await api.getValueMappings(columnId);
        setValueMappings(response.data);
        setTotalMappings(response.data.length);
      }
    } catch (error) {
      message.error('获取值映射失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Setup effects
  useEffect(() => {
    fetchConnections();
    fetchTotalMappings();
  }, []);

  useEffect(() => {
    if (selectedConnection) {
      fetchTables(selectedConnection);
    }
  }, [selectedConnection]);

  useEffect(() => {
    if (selectedTable) {
      fetchColumns(selectedTable);
    }
  }, [selectedTable, fetchColumns]);

  useEffect(() => {
    if (selectedColumn) {
      fetchValueMappings(selectedColumn);
    }
  }, [selectedColumn]);

  const handleConnectionChange = (value: number) => {
    setSelectedConnection(value);
  };

  const handleTableChange = (value: number) => {
    setSelectedTable(value);
  };

  const handleColumnChange = (value: number) => {
    setSelectedColumn(value);
  };

  const showModal = (mapping?: ValueMapping) => {
    setEditingMapping(mapping || null);
    form.resetFields();
    if (mapping) {
      form.setFieldsValue({
        nl_term: mapping.nl_term,
        db_value: mapping.db_value,
      });
    }
    setModalVisible(true);
  };

  const handleCancel = () => {
    setModalVisible(false);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (editingMapping) {
        await api.updateValueMapping(editingMapping.id, values);
        message.success('值映射更新成功');
      } else {
        await api.createValueMapping({
          ...values,
          column_id: selectedColumn
        });
        message.success('值映射创建成功');
      }

      setModalVisible(false);
      fetchValueMappings(selectedColumn!);
    } catch (error) {
      message.error('保存值映射失败');
      console.error(error);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      await api.deleteValueMapping(id);
      message.success('值映射删除成功');
      fetchValueMappings(selectedColumn!);
    } catch (error) {
      message.error('删除值映射失败');
      console.error(error);
    }
  };

  const columns_table = [
    {
      title: '自然语言术语',
      dataIndex: 'nl_term',
      key: 'nl_term',
      width: '40%',
      ellipsis: true,
    },
    {
      title: '数据库值',
      dataIndex: 'db_value',
      key: 'db_value',
      width: '40%',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'actions',
      width: '20%',
      fixed: 'right' as const,
      render: (_: any, record: ValueMapping) => (
        <Space size="middle">
          <Button
            icon={<EditOutlined />}
            onClick={() => showModal(record)}
            size="small"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个映射吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <Title level={4}>值映射</Title>
          <div>
            <Select
              placeholder="选择数据库连接"
              style={{ width: 200, marginRight: 8 }}
              onChange={handleConnectionChange}
              value={selectedConnection || undefined}
            >
              {connections.map(conn => (
                <Option key={conn.id} value={conn.id}>{conn.name}</Option>
              ))}
            </Select>

            <Select
              placeholder="选择表"
              style={{ width: 200, marginRight: 8 }}
              onChange={handleTableChange}
              value={selectedTable || undefined}
              disabled={!selectedConnection}
            >
              {tables.map(table => (
                <Option key={table.id} value={table.id}>{table.table_name}</Option>
              ))}
            </Select>

            <Select
              placeholder="选择列"
              style={{ width: 200 }}
              onChange={handleColumnChange}
              value={selectedColumn || undefined}
              disabled={!selectedTable}
            >
              {columns.map(column => (
                <Option key={column.id} value={column.id}>{column.column_name}</Option>
              ))}
            </Select>
          </div>
        </div>

        {selectedColumn && (
          <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => showModal()}
            >
              添加值映射
            </Button>

            {/* 搜索框 */}
            <Input.Search
              placeholder="搜索自然语言术语或数据库值"
              allowClear
              style={{ width: 300 }}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={(value) => setSearchText(value)}
            />
          </div>
        )}

        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin />
          </div>
        ) : selectedColumn ? (
          <>
            {/* 特殊提示：当选择valuemapping表时 */}
            {tables.find(t => t.id === selectedTable)?.table_name === 'valuemapping' && (
              <div style={{ marginBottom: 16, padding: '8px 12px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '4px' }}>
                <span style={{ color: '#52c41a' }}>
                  💡 提示：显示所有字段映射关系 ({valueMappings.length} 个映射)
                </span>
              </div>
            )}
            <Table
              columns={columns_table}
              dataSource={valueMappings.filter(mapping =>
                !searchText ||
                mapping.nl_term.toLowerCase().includes(searchText.toLowerCase()) ||
                mapping.db_value.toLowerCase().includes(searchText.toLowerCase())
              )}
              rowKey="id"
              pagination={{
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
                pageSizeOptions: ['10', '20', '50', '100'],
              }}
              scroll={{
                y: 400,  // 设置表格最大高度，超出时显示滚动条
                x: 'max-content'  // 水平滚动
              }}
              size="middle"
            />
          </>
        ) : (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <p>请选择连接、表和列来管理值映射</p>
            {selectedConnection === 2 && selectedTable && tables.find(t => t.id === selectedTable)?.table_name === 'valuemapping' && (
              <p style={{ color: '#1890ff', marginTop: '8px' }}>
                💡 选择任意字段即可查看所有字段映射关系
                {totalMappings > 0 ? ` (共 ${totalMappings} 个)` : ' (暂无数据)'}
              </p>
            )}
          </div>
        )}
      </Card>

      <Modal
        title={editingMapping ? '编辑值映射' : '添加值映射'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="nl_term"
            label="自然语言术语"
            rules={[{ required: true, message: '请输入自然语言术语' }]}
          >
            <Input placeholder="e.g., 中石化" />
          </Form.Item>

          <Form.Item
            name="db_value"
            label="数据库值"
            rules={[{ required: true, message: '请输入数据库值' }]}
          >
            <Input placeholder="e.g., 中国石化" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ValueMappingsPage;
