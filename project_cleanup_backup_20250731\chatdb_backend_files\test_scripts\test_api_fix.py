#!/usr/bin/env python3
"""
测试API修复效果
"""

import requests
import json

def test_value_mappings_api():
    """测试value mappings API"""
    
    base_url = "http://localhost:8000/api/v1"
    
    print("🧪 测试Value Mappings API修复效果")
    print("=" * 50)
    
    # 测试1: 获取所有映射（无参数）
    print("1️⃣ 测试获取所有映射:")
    try:
        response = requests.get(f"{base_url}/value-mappings")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功获取 {len(data)} 个映射")
            
            # 显示前5个映射示例
            print("   📋 映射示例:")
            for i, mapping in enumerate(data[:5]):
                print(f"      {i+1}. '{mapping['nl_term']}' -> {mapping['db_value']}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
    
    # 测试2: 测试错误的column_id（前端之前的行为）
    print(f"\n2️⃣ 测试错误的column_id (160):")
    try:
        response = requests.get(f"{base_url}/value-mappings?column_id=160")
        if response.status_code == 200:
            data = response.json()
            print(f"   结果: {len(data)} 个映射 (应该为0)")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
    
    # 测试3: 测试正确的column_id
    print(f"\n3️⃣ 测试正确的column_id (4 - accounting_unit_name):")
    try:
        response = requests.get(f"{base_url}/value-mappings?column_id=4")
        if response.status_code == 200:
            data = response.json()
            print(f"   结果: {len(data)} 个映射")
            if data:
                print("   📋 映射示例:")
                for mapping in data[:3]:
                    print(f"      '{mapping['nl_term']}' -> {mapping['db_value']}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")

def test_connections_api():
    """测试连接API"""
    
    base_url = "http://localhost:8000/api/v1"
    
    print(f"\n4️⃣ 测试数据库连接API:")
    try:
        response = requests.get(f"{base_url}/connections")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功获取 {len(data)} 个连接")
            for conn in data:
                print(f"      ID: {conn['id']}, Name: {conn['name']}, Type: {conn['db_type']}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")

def show_frontend_test_guide():
    """显示前端测试指南"""
    
    print(f"\n" + "=" * 50)
    print("📱 前端测试指南")
    print("=" * 50)
    
    print("""
🔄 测试步骤:
1. 刷新前端页面: http://************:3000/value-mappings
2. 选择数据库连接: "resource"
3. 选择表: "valuemapping"
4. 选择任意字段: "nl_term" 或 "db_value"
5. 应该看到所有81个字段映射

✅ 预期结果:
- 不再显示 "No data"
- 显示完整的映射列表
- 可以进行增删改操作
- 显示绿色提示框说明显示所有映射

🔍 调试方法:
- 打开浏览器开发者工具
- 查看Network标签页
- 确认API调用 GET /api/v1/value-mappings (无参数)
- 检查返回的数据量

⚠️ 如果仍有问题:
- 检查后端服务是否运行在 localhost:8000
- 确认前端代码修改已保存并重新编译
- 清除浏览器缓存
""")

if __name__ == "__main__":
    test_value_mappings_api()
    test_connections_api()
    show_frontend_test_guide()
    
    print(f"\n" + "=" * 50)
    print("🎯 修复完成！前端应该可以正常显示数据了")
    print("=" * 50)
