#!/usr/bin/env python3
"""
诊断监控系统集成问题
"""

import sys
import os
import importlib.util

def check_monitoring_imports():
    """检查监控系统的导入情况"""
    print("🔍 检查监控系统导入")
    print("-" * 50)
    
    try:
        # 检查pipeline_monitor是否能正确导入
        from app.utils.pipeline_monitor import pipeline_monitor
        print("✅ pipeline_monitor 导入成功")
        
        # 检查是否有log方法
        if hasattr(pipeline_monitor, 'log_pipeline_start'):
            print("✅ log_pipeline_start 方法存在")
        else:
            print("❌ log_pipeline_start 方法不存在")
            
        # 测试创建日志
        test_query_id = pipeline_monitor.log_pipeline_start("测试查询", "test_connection")
        print(f"✅ 测试日志创建成功，查询ID: {test_query_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ pipeline_monitor 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_text2sql_service_integration():
    """检查Text2SQL服务的监控集成"""
    print(f"\n🔧 检查Text2SQL服务集成")
    print("-" * 50)
    
    try:
        # 检查text2sql_service是否导入了pipeline_monitor
        import inspect
        from app.services.text2sql_service import process_text2sql_query
        
        # 获取函数源码
        source = inspect.getsource(process_text2sql_query)
        
        # 检查是否包含监控代码
        monitoring_keywords = [
            'pipeline_monitor',
            'log_pipeline_start',
            'monitor_step1',
            'monitor_step2',
            'log_pipeline_summary'
        ]
        
        found_keywords = []
        for keyword in monitoring_keywords:
            if keyword in source:
                found_keywords.append(keyword)
        
        print(f"✅ 找到监控关键词: {found_keywords}")
        
        if len(found_keywords) >= 3:
            print("✅ Text2SQL服务已集成监控系统")
            return True
        else:
            print("❌ Text2SQL服务监控集成不完整")
            print("   缺少关键监控代码")
            return False
            
    except Exception as e:
        print(f"❌ 检查Text2SQL服务失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_log_file_permissions():
    """检查日志文件权限"""
    print(f"\n📄 检查日志文件权限")
    print("-" * 50)
    
    log_files = ['pipeline_monitor.log', 'text2sql_debug.log']
    
    for log_file in log_files:
        try:
            # 尝试写入测试内容
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"# 测试写入 - {log_file}\n")
            
            print(f"✅ {log_file}: 可写入")
            
            # 检查文件大小
            size = os.path.getsize(log_file)
            print(f"   当前大小: {size} bytes")
            
        except Exception as e:
            print(f"❌ {log_file}: 写入失败 - {e}")

def test_direct_monitoring():
    """直接测试监控系统"""
    print(f"\n🧪 直接测试监控系统")
    print("-" * 50)
    
    try:
        from app.utils.pipeline_monitor import pipeline_monitor
        
        # 模拟完整的监控流程
        query_id = pipeline_monitor.log_pipeline_start("直接测试查询", "test_connection")
        print(f"✅ 步骤1: 流水线开始 - {query_id}")
        
        # 模拟步骤1
        schema_context = {
            'tables': [{'name': 'test_table', 'id': 1}],
            'columns': [{'name': 'test_column', 'id': 1, 'table_name': 'test_table'}]
        }
        step1_result = pipeline_monitor.monitor_step1_schema_retrieval(query_id, 1, schema_context)
        print(f"✅ 步骤2: Schema检索监控 - {step1_result['status']}")
        
        # 模拟步骤2
        value_mappings = {
            'test_table.test_column': {'test_term': 'test_value'}
        }
        step2_result = pipeline_monitor.monitor_step2_value_mappings(query_id, schema_context, value_mappings)
        print(f"✅ 步骤3: 字段映射监控 - {step2_result['status']}")
        
        # 记录总结
        pipeline_monitor.log_pipeline_summary(query_id, [step1_result, step2_result], 1.0, True)
        print(f"✅ 步骤4: 流水线总结")
        
        print(f"\n📋 检查日志文件:")
        if os.path.exists('pipeline_monitor.log'):
            size = os.path.getsize('pipeline_monitor.log')
            print(f"   pipeline_monitor.log: {size} bytes")
            
            if size > 0:
                with open('pipeline_monitor.log', 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    print(f"   最后5行:")
                    for line in lines[-5:]:
                        print(f"      {line.strip()}")
            else:
                print(f"   文件仍然为空！")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_actual_text2sql_call():
    """检查实际的Text2SQL调用路径"""
    print(f"\n🔍 检查实际Text2SQL调用路径")
    print("-" * 50)
    
    try:
        # 检查前端实际调用的API端点
        print("前端可能调用的API端点:")
        print("1. /api/text2sql/ (主要端点)")
        print("2. /api/text2sql-sse/ (流式端点)")
        print("3. /api/query/ (查询端点)")
        
        # 检查这些端点是否使用了process_text2sql_query
        from app.api.api_v1.endpoints import text2sql, text2sql_sse, query
        
        import inspect
        
        # 检查text2sql端点
        if hasattr(text2sql, 'process_query'):
            source = inspect.getsource(text2sql.process_query)
            if 'process_text2sql_query' in source:
                print("✅ text2sql端点使用了process_text2sql_query")
            else:
                print("❌ text2sql端点没有使用process_text2sql_query")
        
        # 检查text2sql_sse端点
        if hasattr(text2sql_sse, 'process_query_stream'):
            source = inspect.getsource(text2sql_sse.process_query_stream)
            if 'process_text2sql_query' in source:
                print("✅ text2sql_sse端点使用了process_text2sql_query")
            else:
                print("❌ text2sql_sse端点没有使用process_text2sql_query")
                
        return True
        
    except Exception as e:
        print(f"❌ 检查调用路径失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def provide_solutions():
    """提供解决方案"""
    print(f"\n🎯 问题诊断和解决方案")
    print("=" * 60)
    
    print("""
基于诊断结果，可能的问题和解决方案：

🔍 **问题1: 监控系统没有被正确集成**
   - 原因: process_text2sql_query函数没有被实际调用
   - 解决: 检查前端实际调用的API端点
   - 行动: 确保所有Text2SQL端点都使用了带监控的函数

🔍 **问题2: 日志文件权限问题**
   - 原因: 无法写入日志文件
   - 解决: 检查文件权限和目录权限
   - 行动: 修复文件权限或更改日志目录

🔍 **问题3: 导入或初始化问题**
   - 原因: pipeline_monitor没有正确初始化
   - 解决: 检查导入路径和初始化代码
   - 行动: 修复导入问题

🔍 **问题4: 前端使用了不同的API端点**
   - 原因: 前端可能调用了没有监控的旧端点
   - 解决: 确认前端实际调用的API路径
   - 行动: 统一所有端点使用监控系统

---

🚨 **立即行动方案**:

1️⃣ 运行此诊断脚本查看具体问题
2️⃣ 根据诊断结果修复发现的问题
3️⃣ 重新测试监控系统
4️⃣ 如果仍有问题，检查前端API调用路径
""")

if __name__ == "__main__":
    print("🔍 监控系统集成诊断")
    print("=" * 60)
    
    # 1. 检查导入
    imports_ok = check_monitoring_imports()
    
    # 2. 检查服务集成
    if imports_ok:
        integration_ok = check_text2sql_service_integration()
    else:
        integration_ok = False
    
    # 3. 检查文件权限
    check_log_file_permissions()
    
    # 4. 直接测试监控
    if imports_ok:
        direct_test_ok = test_direct_monitoring()
    else:
        direct_test_ok = False
    
    # 5. 检查调用路径
    check_actual_text2sql_call()
    
    # 6. 提供解决方案
    provide_solutions()
    
    print(f"\n" + "=" * 60)
    print("🎯 诊断完成，请根据结果修复问题")
    print("=" * 60)
