#!/usr/bin/env python3
"""
诊断前端"No data"问题的根本原因
"""

import sqlite3

def diagnose_column_id_mismatch():
    """诊断column_id不匹配的问题"""
    
    metadata_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(metadata_db)
    cursor = conn.cursor()
    
    print("🔍 诊断前端'No data'问题")
    print("=" * 60)
    
    # 1. 检查valuemapping表的字段ID
    print("1️⃣ valuemapping表的字段ID分布:")
    cursor.execute('''
        SELECT vm.column_id, sc.column_name, COUNT(*) as mapping_count
        FROM valuemapping vm
        JOIN schemacolumn sc ON vm.column_id = sc.id
        GROUP BY vm.column_id, sc.column_name
        ORDER BY mapping_count DESC
    ''')
    
    mapping_distribution = cursor.fetchall()
    for column_id, column_name, count in mapping_distribution:
        print(f"   Column ID {column_id} ({column_name}): {count}个映射")
    
    # 2. 检查resource连接的valuemapping表字段ID
    print(f"\n2️⃣ resource连接中valuemapping表的字段ID:")
    cursor.execute('''
        SELECT sc.id, sc.column_name
        FROM schemacolumn sc
        JOIN schematable st ON sc.table_id = st.id
        WHERE st.table_name = 'valuemapping' AND st.connection_id = 2
        ORDER BY sc.id
    ''')
    
    resource_columns = cursor.fetchall()
    for col_id, col_name in resource_columns:
        print(f"   ID: {col_id}, Name: {col_name}")
    
    # 3. 关键问题：检查ID冲突
    print(f"\n3️⃣ 🚨 关键问题分析:")
    
    # 获取nl_term字段的ID（在resource连接中）
    nl_term_id = None
    db_value_id = None
    
    for col_id, col_name in resource_columns:
        if col_name == 'nl_term':
            nl_term_id = col_id
        elif col_name == 'db_value':
            db_value_id = col_id
    
    print(f"   resource连接中 nl_term 字段ID: {nl_term_id}")
    print(f"   resource连接中 db_value 字段ID: {db_value_id}")
    
    # 检查这些ID是否有对应的映射数据
    if nl_term_id:
        cursor.execute('SELECT COUNT(*) FROM valuemapping WHERE column_id = ?', (nl_term_id,))
        nl_term_mappings = cursor.fetchone()[0]
        print(f"   column_id={nl_term_id} 的映射数量: {nl_term_mappings}")
    
    if db_value_id:
        cursor.execute('SELECT COUNT(*) FROM valuemapping WHERE column_id = ?', (db_value_id,))
        db_value_mappings = cursor.fetchone()[0]
        print(f"   column_id={db_value_id} 的映射数量: {db_value_mappings}")
    
    # 4. 显示实际的映射数据使用的column_id范围
    print(f"\n4️⃣ 实际映射数据的column_id范围:")
    cursor.execute('SELECT MIN(column_id), MAX(column_id) FROM valuemapping')
    min_id, max_id = cursor.fetchone()
    print(f"   最小column_id: {min_id}")
    print(f"   最大column_id: {max_id}")
    
    # 5. 检查这些column_id对应的实际字段
    print(f"\n5️⃣ 实际映射使用的字段:")
    cursor.execute('''
        SELECT DISTINCT vm.column_id, sc.column_name, st.table_name, st.connection_id
        FROM valuemapping vm
        JOIN schemacolumn sc ON vm.column_id = sc.id
        JOIN schematable st ON sc.table_id = st.id
        ORDER BY vm.column_id
        LIMIT 10
    ''')
    
    actual_fields = cursor.fetchall()
    for column_id, column_name, table_name, connection_id in actual_fields:
        print(f"   ID: {column_id}, Field: {column_name}, Table: {table_name}, Connection: {connection_id}")
    
    conn.close()

def show_solution():
    """显示解决方案"""
    
    print(f"\n" + "=" * 60)
    print("💡 问题根本原因和解决方案")
    print("=" * 60)
    
    print("""
🔍 根本原因:
   前端选择的是resource连接中valuemapping表的字段ID (158-163)
   但实际的映射数据使用的是financial_data表的字段ID (1-31)
   
   这是一个ID不匹配的问题：
   - 前端传递: column_id=160 (nl_term字段)
   - 后端查询: WHERE column_id = 160
   - 实际数据: column_id 在 1-31 范围内
   - 结果: 查询返回空数据

🛠️ 解决方案选择:

方案1: 修改前端逻辑 (推荐)
   - 不通过valuemapping表的字段选择
   - 直接显示所有映射数据
   - 或者按实际的financial_data字段分组

方案2: 修改后端API
   - 添加不需要column_id的查询接口
   - 支持按表名直接查询映射

方案3: 数据重构 (复杂)
   - 重新整理column_id的关联关系
   - 风险较高，不推荐
""")

def create_test_api_calls():
    """创建测试API调用"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试API调用")
    print("=" * 60)
    
    print("""
测试当前API行为:

1. 测试无参数调用 (应该返回所有映射):
   GET /api/v1/value-mappings

2. 测试错误的column_id (前端当前行为):
   GET /api/v1/value-mappings?column_id=160

3. 测试正确的column_id (实际数据):
   GET /api/v1/value-mappings?column_id=1

建议在浏览器开发者工具中检查网络请求，确认API调用的参数。
""")

if __name__ == "__main__":
    diagnose_column_id_mismatch()
    show_solution()
    create_test_api_calls()
