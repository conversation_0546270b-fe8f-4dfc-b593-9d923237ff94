#!/usr/bin/env python3
"""
修复字段映射问题的脚本
添加自然语言术语到实际数据库字段的映射关系
"""

import sqlite3

def fix_field_mappings():
    """修复字段映射关系"""
    
    # 连接元数据库
    metadata_db = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    conn = sqlite3.connect(metadata_db)
    cursor = conn.cursor()
    
    # 获取financial_data表的column信息
    cursor.execute('''
        SELECT sc.id, sc.column_name 
        FROM schemacolumn sc 
        JOIN schematable st ON sc.table_id = st.id 
        WHERE st.table_name = 'financial_data' AND st.connection_id = 1
    ''')
    columns = cursor.fetchall()
    
    # 定义字段映射关系
    field_mappings = {
        # 公司相关映射
        'accounting_unit_name': ['公司', '公司名称', 'company', 'company_id', '企业', '单位'],
        
        # 时间相关映射  
        'year': ['年份', '年', 'year'],
        'month': ['月份', '月', 'month'],
        
        # 金额相关映射
        'debit_amount': ['借方金额', '借方', '支出', '费用金额', 'debit'],
        'credit_amount': ['贷方金额', '贷方', '收入', '收入金额', 'credit'],
        
        # 科目相关映射
        'account_code': ['科目代码', '会计科目代码', '科目编码', 'account_code'],
        'account_full_name': ['科目名称', '会计科目名称', '科目全称', 'account_name'],
        'account_name': ['科目简称', '科目', 'account'],
        
        # 项目相关映射
        'project_name': ['项目名称', '项目', 'project'],
        'project_code': ['项目代码', '项目编码'],
        
        # 银行相关映射
        'bank_name': ['银行名称', '银行', 'bank'],
        'bank_account_id': ['银行账户', '账户'],
        
        # 余额相关映射
        'balance': ['余额', '结余', 'balance'],
        'debit_cumulative': ['借方累计', '累计借方'],
        'credit_cumulative': ['贷方累计', '累计贷方']
    }
    
    print("🔄 开始添加字段映射关系...")
    
    # 为每个字段添加映射关系
    for column_id, column_name in columns:
        if column_name in field_mappings:
            nl_terms = field_mappings[column_name]
            
            for nl_term in nl_terms:
                # 检查映射是否已存在
                cursor.execute(
                    'SELECT id FROM valuemapping WHERE column_id = ? AND nl_term = ?',
                    (column_id, nl_term)
                )
                
                if not cursor.fetchone():
                    # 添加新的映射关系
                    cursor.execute('''
                        INSERT INTO valuemapping (column_id, nl_term, db_value, created_at)
                        VALUES (?, ?, ?, datetime('now'))
                    ''', (column_id, nl_term, column_name))
                    
                    print(f"✅ 添加映射: '{nl_term}' -> {column_name}")
    
    # 添加特殊的组合映射（如日期）
    print("\n🔄 添加特殊组合映射...")
    
    # 为日期查询添加特殊处理说明
    cursor.execute('''
        INSERT OR REPLACE INTO meta_column_descriptions 
        (table_name, column_name, chinese_name, description, ai_understanding_points)
        VALUES 
        ('financial_data', 'year_month_combo', '日期组合', 
         '年月组合字段，用于日期范围查询', 
         '当用户查询包含日期时，需要同时使用year和month字段进行筛选')
    ''')
    
    conn.commit()
    conn.close()
    
    print("✅ 字段映射修复完成!")

if __name__ == "__main__":
    fix_field_mappings()
