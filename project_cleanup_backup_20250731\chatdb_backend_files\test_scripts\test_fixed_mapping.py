#!/usr/bin/env python3
"""
测试修复后的字段映射函数
"""

import re

def process_sql_with_value_mappings(sql: str, value_mappings: dict) -> str:
    """
    修复后的SQL处理函数（复制用于测试）
    """
    if not value_mappings:
        return sql
    
    # 处理字段名替换 - 这是关键修复
    for column, mappings in value_mappings.items():
        table, col = column.split('.')
        
        for nl_term, db_value in mappings.items():
            # 跳过相同的术语，避免无意义替换
            if nl_term == db_value:
                continue
                
            # 1. 替换字段名（最重要的修复）
            # 匹配 SELECT, GROUP BY, ORDER BY 等位置的字段名
            # 使用单词边界确保精确匹配
            field_pattern = rf'\b{re.escape(nl_term)}\b'
            sql = re.sub(field_pattern, db_value, sql, flags=re.IGNORECASE)
            
            # 2. 处理值替换（保留原有功能）
            # 匹配 WHERE column = 'value' 模式
            value_pattern1 = rf"({re.escape(col)}\s*=\s*['\"])({re.escape(nl_term)})(['\"])"
            sql = re.sub(value_pattern1, f"\\1{db_value}\\3", sql, flags=re.IGNORECASE)
            
            # 匹配 WHERE table.column = 'value' 模式
            value_pattern2 = rf"({re.escape(table)}\.{re.escape(col)}\s*=\s*['\"])({re.escape(nl_term)})(['\"])"
            sql = re.sub(value_pattern2, f"\\1{db_value}\\3", sql, flags=re.IGNORECASE)
            
            # 匹配 LIKE 模式
            like_pattern1 = rf"({re.escape(col)}\s+LIKE\s+['\"])%?({re.escape(nl_term)})%?(['\"])"
            sql = re.sub(like_pattern1, f"\\1%{db_value}%\\3", sql, flags=re.IGNORECASE)
            
            like_pattern2 = rf"({re.escape(table)}\.{re.escape(col)}\s+LIKE\s+['\"])%?({re.escape(nl_term)})%?(['\"])"
            sql = re.sub(like_pattern2, f"\\1%{db_value}%\\3", sql, flags=re.IGNORECASE)

    return sql

def test_fixed_mapping():
    """测试修复后的映射函数"""
    
    print("🔧 测试修复后的字段映射函数")
    print("=" * 60)
    
    # 模拟映射数据
    value_mappings = {
        'financial_data.accounting_unit_name': {
            'company_name': 'accounting_unit_name',
            'company_id': 'accounting_unit_name',
            '公司': 'accounting_unit_name'
        },
        'financial_data.year': {
            'date': 'year',
            'expense_date': 'year'
        },
        'financial_data.month': {
            'date': 'month'
        }
    }
    
    # 测试用例1：基本字段名替换
    print("📝 测试用例1：基本字段名替换")
    
    error_sql1 = """
    SELECT 
        company_name, 
        SUM(CAST(debit_amount AS REAL)) AS total_sales_expense
    FROM 
        financial_data
    WHERE 
        date BETWEEN '2024-01-01' AND '2024-01-31'
        AND account_code LIKE '64%'
    GROUP BY 
        company_name
    ORDER BY 
        company_name;
    """
    
    print("❌ 原始SQL:")
    print(f"   包含错误字段: company_name, date")
    
    fixed_sql1 = process_sql_with_value_mappings(error_sql1, value_mappings)
    
    print("✅ 修复后SQL:")
    print(fixed_sql1)
    
    # 检查修复效果
    if 'company_name' not in fixed_sql1:
        print("✅ company_name 已被正确替换")
    else:
        print("❌ company_name 仍然存在")
        
    if 'accounting_unit_name' in fixed_sql1:
        print("✅ accounting_unit_name 已正确出现")
    else:
        print("❌ accounting_unit_name 没有出现")
    
    # 测试用例2：复杂SQL
    print(f"\n📝 测试用例2：复杂SQL")
    
    error_sql2 = """
    SELECT company_id, company_name, 
           SUM(debit_amount) as total
    FROM financial_data 
    WHERE company_name LIKE '%公司%' 
    AND date >= '2024-01-01'
    GROUP BY company_id, company_name
    ORDER BY company_name;
    """
    
    print("❌ 原始SQL:")
    print(f"   包含错误字段: company_id, company_name, date")
    
    fixed_sql2 = process_sql_with_value_mappings(error_sql2, value_mappings)
    
    print("✅ 修复后SQL:")
    print(fixed_sql2)
    
    # 检查修复效果
    error_fields = ['company_id', 'company_name', 'date']
    remaining_errors = [field for field in error_fields if field in fixed_sql2]
    
    if not remaining_errors:
        print("✅ 所有错误字段都已被替换")
    else:
        print(f"❌ 仍有错误字段: {remaining_errors}")
    
    # 测试用例3：边界情况
    print(f"\n📝 测试用例3：边界情况")
    
    edge_case_sql = """
    SELECT company_name_backup, company_name, company_names
    FROM financial_data;
    """
    
    print("❌ 原始SQL (边界测试):")
    print(edge_case_sql)
    
    fixed_edge_sql = process_sql_with_value_mappings(edge_case_sql, value_mappings)
    
    print("✅ 修复后SQL:")
    print(fixed_edge_sql)
    
    # 检查是否只替换了精确匹配的字段
    if 'company_name_backup' in fixed_edge_sql and 'company_names' in fixed_edge_sql:
        print("✅ 边界情况处理正确，只替换精确匹配的字段")
    else:
        print("❌ 边界情况处理有问题，可能过度替换")

def provide_next_steps():
    """提供下一步操作"""
    
    print(f"\n" + "=" * 60)
    print("🎯 下一步操作")
    print("=" * 60)
    
    print("""
✅ **修复完成**:
1. process_sql_with_value_mappings函数已修复
2. 现在可以正确处理字段名替换
3. 使用单词边界确保精确匹配

🔄 **需要重启服务**:
修改了后端代码，需要重启服务使修复生效：

```bash
# 停止当前服务 (Ctrl+C)
# 重新启动
cd chatdb/backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

🧪 **测试验证**:
重启后，重新测试查询：
- "2024年1月各公司的销售费用合计"
- 期望：使用 accounting_unit_name 而不是 company_name
- 期望：使用 year=2024 AND month=1 而不是 date

📊 **预期效果**:
- ✅ 字段名正确替换
- ✅ SQL可以成功执行
- ✅ 返回正确的查询结果
""")

if __name__ == "__main__":
    test_fixed_mapping()
    provide_next_steps()
    
    print(f"\n" + "=" * 60)
    print("🎯 关键修复：现在可以正确处理字段名替换了！")
    print("=" * 60)
