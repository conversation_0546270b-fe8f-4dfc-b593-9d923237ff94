#!/usr/bin/env python3
"""
生成Neo4j数据检查报告
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent / "chatdb" / "backend"
sys.path.insert(0, str(project_root))

async def generate_neo4j_data_report():
    """生成Neo4j数据报告"""
    print("📊 生成Neo4j数据检查报告...")
    
    try:
        from app.services.neo4j_connection_pool import get_neo4j_pool
        from app.db.session import SessionLocal
        from app import crud
        
        pool = await get_neo4j_pool()
        
        # 报告内容
        report_lines = []
        report_lines.append("# Neo4j数据检查报告")
        report_lines.append("")
        report_lines.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 1. 总体统计
        report_lines.append("## 📊 总体数据统计")
        report_lines.append("")
        
        # 获取总体统计
        stats_query = """
        MATCH (t:Table) 
        WITH count(t) as table_count, collect(DISTINCT t.connection_id) as connection_ids
        MATCH (c:Column)
        WITH table_count, connection_ids, count(c) as column_count
        MATCH ()-[r:REFERENCES]->()
        RETURN table_count, column_count, count(r) as relationship_count, connection_ids
        """
        
        stats_result = await pool.execute_read_query(stats_query)
        if stats_result:
            stats = stats_result[0]
            report_lines.append(f"- **表总数**: {stats['table_count']}")
            report_lines.append(f"- **列总数**: {stats['column_count']}")
            report_lines.append(f"- **关系总数**: {stats['relationship_count']}")
            report_lines.append(f"- **连接IDs**: {stats['connection_ids']}")
        
        report_lines.append("")
        
        # 2. 按连接分组的统计
        report_lines.append("## 🔗 按连接分组统计")
        report_lines.append("")
        
        connection_stats_query = """
        MATCH (t:Table)
        WITH t.connection_id as connection_id, count(t) as table_count
        MATCH (t2:Table {connection_id: connection_id})-[:HAS_COLUMN]->(c:Column)
        WITH connection_id, table_count, count(c) as column_count
        OPTIONAL MATCH (t3:Table {connection_id: connection_id})-[:HAS_COLUMN]->(c1:Column)
        -[r:REFERENCES]->(c2:Column)<-[:HAS_COLUMN]-(t4:Table {connection_id: connection_id})
        RETURN connection_id, table_count, column_count, count(r) as relationship_count
        ORDER BY connection_id
        """
        
        conn_stats = await pool.execute_read_query(connection_stats_query)
        for conn in conn_stats:
            report_lines.append(f"### 连接ID {conn['connection_id']}")
            report_lines.append(f"- 表数量: {conn['table_count']}")
            report_lines.append(f"- 列数量: {conn['column_count']}")
            report_lines.append(f"- 关系数量: {conn['relationship_count']}")
            report_lines.append("")
        
        # 3. 详细表结构信息
        for connection_id in [1, 2]:  # 检查连接1和2
            report_lines.append(f"## 📋 连接ID {connection_id} 详细表结构")
            report_lines.append("")
            
            # 获取表信息
            tables_query = """
            MATCH (t:Table {connection_id: $connection_id})
            RETURN t.id as id, t.name as name, t.description as description
            ORDER BY t.name
            """
            
            tables = await pool.execute_read_query(tables_query, {'connection_id': connection_id})
            
            if not tables:
                report_lines.append(f"⚠️ 连接ID {connection_id} 没有找到表数据")
                report_lines.append("")
                continue
            
            for table in tables:
                table_name = table['name']
                table_id = table['id']
                table_desc = table['description'] or "无描述"
                
                report_lines.append(f"### 表: {table_name}")
                report_lines.append(f"- **表ID**: {table_id}")
                report_lines.append(f"- **描述**: {table_desc}")
                report_lines.append("")
                
                # 获取该表的列信息
                columns_query = """
                MATCH (t:Table {id: $table_id})-[:HAS_COLUMN]->(c:Column)
                RETURN c.id as id, c.name as name, c.type as type, c.description as description,
                       c.is_pk as is_pk, c.is_fk as is_fk
                ORDER BY c.name
                """
                
                columns = await pool.execute_read_query(columns_query, {'table_id': table_id})
                
                if columns:
                    report_lines.append("#### 列信息")
                    report_lines.append("")
                    report_lines.append("| 列名 | 数据类型 | 主键 | 外键 | 描述 |")
                    report_lines.append("|------|----------|------|------|------|")
                    
                    for col in columns:
                        col_name = col['name']
                        col_type = col['type'] or 'N/A'
                        is_pk = '✅' if col['is_pk'] else '❌'
                        is_fk = '✅' if col['is_fk'] else '❌'
                        col_desc = col['description'] or '无描述'
                        
                        report_lines.append(f"| {col_name} | {col_type} | {is_pk} | {is_fk} | {col_desc} |")
                    
                    report_lines.append("")
                    report_lines.append(f"**列总数**: {len(columns)}")
                else:
                    report_lines.append("⚠️ 该表没有找到列数据")
                
                report_lines.append("")
                report_lines.append("---")
                report_lines.append("")
        
        # 4. 关系信息
        report_lines.append("## 🔗 表关系信息")
        report_lines.append("")
        
        for connection_id in [1, 2]:
            relationships_query = """
            MATCH (t1:Table {connection_id: $connection_id})-[:HAS_COLUMN]->(c1:Column)
            -[r:REFERENCES]->(c2:Column)<-[:HAS_COLUMN]-(t2:Table {connection_id: $connection_id})
            RETURN t1.name as source_table, c1.name as source_column,
                   t2.name as target_table, c2.name as target_column,
                   type(r) as relationship_type
            ORDER BY source_table, target_table
            """
            
            relationships = await pool.execute_read_query(relationships_query, {'connection_id': connection_id})
            
            if relationships:
                report_lines.append(f"### 连接ID {connection_id} 的表关系")
                report_lines.append("")
                report_lines.append("| 源表 | 源列 | 目标表 | 目标列 | 关系类型 |")
                report_lines.append("|------|------|--------|--------|----------|")
                
                for rel in relationships:
                    report_lines.append(f"| {rel['source_table']} | {rel['source_column']} | {rel['target_table']} | {rel['target_column']} | {rel['relationship_type']} |")
                
                report_lines.append("")
                report_lines.append(f"**关系总数**: {len(relationships)}")
                report_lines.append("")
            else:
                report_lines.append(f"### 连接ID {connection_id}")
                report_lines.append("⚠️ 没有找到表关系数据")
                report_lines.append("")
        
        # 5. 数据一致性检查
        report_lines.append("## ✅ 数据一致性检查")
        report_lines.append("")
        
        # 与SQLite数据对比
        db = SessionLocal()
        try:
            for connection_id in [1, 2]:
                report_lines.append(f"### 连接ID {connection_id} 一致性检查")
                report_lines.append("")
                
                # SQLite数据
                sqlite_tables = crud.schema_table.get_by_connection(db, connection_id=connection_id)
                sqlite_table_count = len(sqlite_tables)
                
                sqlite_column_count = 0
                for table in sqlite_tables:
                    columns = crud.schema_column.get_by_table(db, table_id=table.id)
                    sqlite_column_count += len(columns)
                
                sqlite_rel_count = len(crud.schema_relationship.get_by_connection(db, connection_id=connection_id))
                
                # Neo4j数据
                neo4j_stats = await pool.execute_read_query("""
                    MATCH (t:Table {connection_id: $connection_id})
                    WITH count(t) as table_count
                    MATCH (t2:Table {connection_id: $connection_id})-[:HAS_COLUMN]->(c:Column)
                    WITH table_count, count(c) as column_count
                    OPTIONAL MATCH (t3:Table {connection_id: $connection_id})-[:HAS_COLUMN]->(c1:Column)
                    -[r:REFERENCES]->(c2:Column)<-[:HAS_COLUMN]-(t4:Table {connection_id: $connection_id})
                    RETURN table_count, column_count, count(r) as relationship_count
                """, {'connection_id': connection_id})
                
                if neo4j_stats:
                    neo4j_data = neo4j_stats[0]
                    
                    report_lines.append("| 数据类型 | SQLite | Neo4j | 状态 |")
                    report_lines.append("|----------|--------|-------|------|")
                    
                    table_status = "✅ 一致" if sqlite_table_count == neo4j_data['table_count'] else "❌ 不一致"
                    column_status = "✅ 一致" if sqlite_column_count == neo4j_data['column_count'] else "❌ 不一致"
                    rel_status = "✅ 一致" if sqlite_rel_count == neo4j_data['relationship_count'] else "❌ 不一致"
                    
                    report_lines.append(f"| 表数量 | {sqlite_table_count} | {neo4j_data['table_count']} | {table_status} |")
                    report_lines.append(f"| 列数量 | {sqlite_column_count} | {neo4j_data['column_count']} | {column_status} |")
                    report_lines.append(f"| 关系数量 | {sqlite_rel_count} | {neo4j_data['relationship_count']} | {rel_status} |")
                    
                    report_lines.append("")
        finally:
            db.close()
        
        # 6. 建议和注意事项
        report_lines.append("## 💡 建议和注意事项")
        report_lines.append("")
        report_lines.append("1. **定期同步检查**: 建议定期运行数据同步检查，确保SQLite和Neo4j数据一致")
        report_lines.append("2. **缓存管理**: 当数据结构发生变化时，及时清理缓存")
        report_lines.append("3. **性能监控**: 关注Schema检索的性能，特别是大表的查询效率")
        report_lines.append("4. **数据验证**: 定期验证表结构数据的准确性和完整性")
        report_lines.append("")
        report_lines.append("---")
        report_lines.append("")
        report_lines.append("*此报告由Neo4j数据检查工具自动生成*")
        
        # 写入文件
        report_content = "\n".join(report_lines)
        
        with open("Neo4j数据检查报告.md", "w", encoding="utf-8") as f:
            f.write(report_content)
        
        print("✅ 报告生成完成: Neo4j数据检查报告.md")
        print(f"📄 报告包含 {len(report_lines)} 行内容")
        
        return True
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("📊 Neo4j数据检查报告生成器")
    print("=" * 50)
    
    success = await generate_neo4j_data_report()
    
    if success:
        print("\n🎉 报告生成成功！")
        print("请查看文件: Neo4j数据检查报告.md")
    else:
        print("\n❌ 报告生成失败")

if __name__ == "__main__":
    asyncio.run(main())
