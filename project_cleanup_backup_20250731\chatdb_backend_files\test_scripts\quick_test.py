#!/usr/bin/env python3
"""
快速测试修复后的API
"""

import requests
import json

def test_field_mappings():
    """测试字段映射API"""
    print("🔧 测试字段映射API...")
    
    try:
        response = requests.get('http://localhost:8000/api/debug/field-mappings/fin_data')
        print(f'状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print('✅ 字段映射API正常')
            stats = data['statistics']
            print(f'总映射数: {stats["total_mappings"]}')
            print(f'映射字段数: {stats["mapped_fields"]}')
            
            # 显示关键映射
            key_mappings = data.get('key_mappings', {})
            if key_mappings:
                print('🔑 关键映射:')
                for field, mappings in key_mappings.items():
                    print(f'   {field}: {mappings}')
            
            return True
        else:
            print(f'❌ 错误: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ 请求失败: {e}')
        return False

def test_text2sql():
    """测试Text2SQL API"""
    print('\n🚀 测试Text2SQL API...')
    
    try:
        response = requests.post(
            'http://localhost:8000/api/debug/test-text2sql',
            json={
                "query": "查询2024年2月的销售费用数据",
                "connection_name": "fin_data"
            }
        )
        
        print(f'状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print('✅ Text2SQL API正常')
            
            query_id = data.get('debug_info', {}).get('query_id')
            has_error = bool(data['result']['error'])
            
            print(f'查询ID: {query_id}')
            print(f'有错误: {has_error}')
            
            if has_error:
                print(f'错误信息: {data["result"]["error"]}')
            
            return query_id
        else:
            print(f'❌ 错误: {response.text}')
            return None
            
    except Exception as e:
        print(f'❌ 请求失败: {e}')
        return None

def check_logs():
    """检查日志文件"""
    print('\n📋 检查日志文件...')
    
    import os
    
    log_files = ['pipeline_monitor.log', 'text2sql_debug.log']
    
    for log_file in log_files:
        if os.path.exists(log_file):
            size = os.path.getsize(log_file)
            print(f'{log_file}: {size} bytes')
            
            if size > 0:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    print(f'   最后3行:')
                    for line in lines[-3:]:
                        print(f'      {line.strip()}')
            else:
                print(f'   文件为空')
        else:
            print(f'{log_file}: 不存在')

if __name__ == "__main__":
    print('🔍 快速API测试')
    print('=' * 40)
    
    # 1. 测试字段映射API
    mappings_ok = test_field_mappings()
    
    if mappings_ok:
        # 2. 测试Text2SQL API
        query_id = test_text2sql()
        
        if query_id:
            print(f'\n⏳ 等待3秒让日志写入...')
            import time
            time.sleep(3)
            
            # 3. 检查日志
            check_logs()
        else:
            print('\n❌ Text2SQL测试失败，无法检查日志')
    else:
        print('\n❌ 字段映射API测试失败')
    
    print('\n🎯 如果看到日志内容，说明监控系统正常工作！')
