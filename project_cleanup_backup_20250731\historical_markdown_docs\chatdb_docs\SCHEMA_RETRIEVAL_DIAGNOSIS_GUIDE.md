# Text2SQL Schema检索问题诊断与修复指南

## 📋 概述

本指南提供了Text2SQL系统中Schema检索失败问题的全面诊断和修复方案。当`retrieve_relevant_schema`函数返回空的columns列表时，可以按照本指南进行系统性的问题排查和修复。

## 🔍 问题症状

- `retrieve_relevant_schema`函数返回空的tables或columns列表
- Schema检索Agent显示"未找到相关表结构"
- Neo4j图数据库查询失败
- 系统回退到SQLite直接查询

## 🛠️ 诊断工具

### 1. 一键修复脚本

```bash
cd chatdb/backend
python scripts/fix_schema_retrieval.py
```

这个脚本会自动：
- 诊断Neo4j连接问题
- 检查数据同步状态
- 验证数据库连接配置
- 应用可能的修复方案
- 生成详细的修复报告

### 2. API诊断端点

#### 系统健康总览
```bash
GET /api/v1/diagnostics/health/overview
```

#### Neo4j完整诊断
```bash
GET /api/v1/diagnostics/neo4j/full
```

#### 数据同步状态检查
```bash
GET /api/v1/diagnostics/data-sync?connection_id=1
```

#### Schema检索功能测试
```bash
GET /api/v1/diagnostics/schema-retrieval/test?connection_id=1&query=查询销售数据
```

## 🔧 手动诊断步骤

### 步骤1: Neo4j连接诊断

#### 1.1 网络连通性检查
```bash
# 测试Neo4j服务器连接
telnet ************** 7687

# 或使用Python测试
python -c "
import socket
sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
result = sock.connect_ex(('**************', 7687))
print('连接成功' if result == 0 else '连接失败')
sock.close()
"
```

#### 1.2 Neo4j服务状态检查
```bash
# 检查Neo4j服务状态（如果是本地服务）
systemctl status neo4j

# 或检查Neo4j进程
ps aux | grep neo4j
```

#### 1.3 认证信息验证
检查配置文件中的Neo4j认证信息：
```python
# 在Python中验证
from app.core.config import settings
print(f"Neo4j URI: {settings.NEO4J_URI}")
print(f"Neo4j User: {settings.NEO4J_USER}")
print(f"Neo4j Password: {'*' * len(settings.NEO4J_PASSWORD)}")
```

### 步骤2: 数据同步状态检查

#### 2.1 SQLite数据检查
```python
from app.db.session import SessionLocal
from app import crud

db = SessionLocal()
try:
    # 检查连接数量
    connections = crud.db_connection.get_multi(db)
    print(f"数据库连接数: {len(connections)}")
    
    # 检查表数量
    for conn in connections:
        tables = crud.schema_table.get_by_connection(db, connection_id=conn.id)
        print(f"连接ID {conn.id}: {len(tables)} 个表")
finally:
    db.close()
```

#### 2.2 Neo4j数据检查
```python
from app.services.neo4j_connection_pool import get_neo4j_pool
import asyncio

async def check_neo4j_data():
    pool = await get_neo4j_pool()
    
    # 检查表节点数量
    tables = await pool.execute_read_query("MATCH (t:Table) RETURN count(t) as count")
    print(f"Neo4j表节点数: {tables[0]['count'] if tables else 0}")
    
    # 检查列节点数量
    columns = await pool.execute_read_query("MATCH (c:Column) RETURN count(c) as count")
    print(f"Neo4j列节点数: {columns[0]['count'] if columns else 0}")

asyncio.run(check_neo4j_data())
```

### 步骤3: 连接池状态检查

```python
from app.services.neo4j_connection_pool import neo4j_pool

print(f"连接池初始化状态: {neo4j_pool._initialized}")
print(f"连接池配置: {neo4j_pool._connection_config}")
```

## 🔨 修复方案

### 方案1: Neo4j连接问题修复

#### 1.1 网络连接问题
- 检查Neo4j服务器是否运行
- 验证防火墙设置
- 确认网络连通性
- 检查端口是否被占用

#### 1.2 认证问题
- 验证用户名和密码
- 检查用户权限
- 重置Neo4j密码（如需要）

#### 1.3 配置问题
- 检查URI格式（bolt://host:port）
- 验证SSL/TLS设置
- 调整连接超时参数

### 方案2: 数据同步问题修复

#### 2.1 自动同步修复
```bash
# 使用API修复数据同步
POST /api/v1/diagnostics/data-sync/fix/1
```

#### 2.2 手动同步修复
```python
from app.services.schema_service import sync_schema_to_graph_db

# 同步特定连接的数据
result = sync_schema_to_graph_db(connection_id=1)
print(f"同步结果: {result}")
```

#### 2.3 重新导入Schema数据
```bash
# 使用Schema导入API
POST /api/v1/schema/import/{connection_id}
```

### 方案3: 连接池问题修复

#### 3.1 重新初始化连接池
```python
from app.services.neo4j_connection_pool import neo4j_pool
import asyncio

async def reinit_pool():
    await neo4j_pool.close()
    await neo4j_pool.initialize()
    print("连接池重新初始化完成")

asyncio.run(reinit_pool())
```

#### 3.2 调整连接池配置
修改`neo4j_connection_pool.py`中的配置参数：
```python
self._connection_config = {
    'max_connection_lifetime': 7200,  # 增加到2小时
    'max_connection_pool_size': 100,  # 增加连接数
    'connection_acquisition_timeout': 120,  # 增加超时时间
    'keep_alive': True,
    'trust': 'TRUST_ALL_CERTIFICATES'
}
```

## 🚨 故障转移和降级策略

### 自动降级
系统已实现自动降级机制，当Neo4j不可用时：
1. 自动切换到SQLite直接查询
2. 返回结果中包含`_fallback: true`标记
3. 记录降级原因和错误信息

### 手动启用故障转移
```python
from app.services.failover_manager import failover_manager
import asyncio

async def enable_failover():
    await failover_manager.start_monitoring()
    print("故障转移监控已启动")

asyncio.run(enable_failover())
```

## 📊 监控和预防

### 1. 健康检查监控
设置定期健康检查：
```bash
# 每5分钟检查一次
*/5 * * * * curl -s http://localhost:8000/api/v1/diagnostics/health/overview
```

### 2. 性能基准测试
```bash
GET /api/v1/diagnostics/performance/benchmark
```

### 3. 日志监控
关键日志关键词：
- `❌ [Schema检索]` - Schema检索错误
- `⚠️ [节点X/7]` - 管道节点警告
- `Neo4j连接池初始化失败` - 连接池问题
- `数据同步不一致` - 数据同步问题

## 🔄 预防性维护

### 1. 定期数据同步
```bash
# 每天凌晨2点同步数据
0 2 * * * python scripts/sync_all_connections.py
```

### 2. 连接池健康检查
```bash
# 每小时检查连接池状态
0 * * * * curl -s http://localhost:8000/api/v1/diagnostics/neo4j
```

### 3. 缓存清理
```bash
# 每周清理一次缓存
0 0 * * 0 curl -X POST http://localhost:8000/api/v1/diagnostics/cache/clear
```

## 📞 故障排除联系

如果按照本指南仍无法解决问题，请：

1. 收集诊断信息：
   ```bash
   python scripts/fix_schema_retrieval.py > diagnosis_report.txt 2>&1
   ```

2. 检查日志文件：
   - `schema_fix.log` - 修复脚本日志
   - 应用主日志文件

3. 提供以下信息：
   - 错误症状描述
   - 诊断报告
   - 相关日志片段
   - 系统环境信息

## 📚 相关文档

- [Neo4j连接池优化文档](../NEO4J_CONNECTION_POOL_OPTIMIZATION.md)
- [Text2SQL架构分析](../technical_analysis/智能查询与元数据库技术架构分析报告.md)
- [API文档](../api/README.md)

---

**注意**: 本指南会根据系统更新持续维护，请确保使用最新版本。
