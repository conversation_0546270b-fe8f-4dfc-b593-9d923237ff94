#!/usr/bin/env python3
"""
智能数据分析系统诊断脚本
用于检测系统升级后的状态和潜在问题
"""

import os
import sys
import sqlite3
import json
import time
import requests
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "chatdb" / "backend"))

try:
    from app.core.config import settings
    from app.db.session import SessionLocal
    from app.services.cache_service import CacheService
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

class SystemDiagnostics:
    """系统诊断器"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'UNKNOWN',
            'components': {},
            'issues': [],
            'recommendations': []
        }
        
    def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🔍 开始系统诊断...")
        print("=" * 60)
        
        # 1. 检查配置文件
        self._check_configuration()
        
        # 2. 检查数据库连接
        self._check_database_connections()
        
        # 3. 检查元数据完整性
        self._check_metadata_integrity()
        
        # 4. 检查缓存系统
        self._check_cache_system()
        
        # 5. 检查LLM API连接
        self._check_llm_api()
        
        # 6. 检查Neo4j连接
        self._check_neo4j_connection()
        
        # 7. 生成诊断报告
        self._generate_report()
        
        return self.results
    
    def _check_configuration(self):
        """检查配置文件"""
        print("📋 检查配置文件...")
        
        config_status = {
            'env_file_exists': False,
            'required_vars_present': False,
            'database_paths_valid': False,
            'issues': []
        }
        
        # 检查.env文件是否存在
        env_path = Path("chatdb/.env")
        if env_path.exists():
            config_status['env_file_exists'] = True
            print("  ✅ .env文件存在")
        else:
            config_status['issues'].append("❌ .env文件不存在")
            print("  ❌ .env文件不存在")
        
        # 检查必需的环境变量
        required_vars = [
            'DATABASE_TYPE', 'SQLITE_DB_PATH', 'METADATA_DB_PATH', 
            'BUSINESS_DB_PATH', 'OPENAI_API_KEY', 'LLM_MODEL'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not hasattr(settings, var) or not getattr(settings, var):
                missing_vars.append(var)
        
        if not missing_vars:
            config_status['required_vars_present'] = True
            print("  ✅ 必需环境变量完整")
        else:
            config_status['issues'].append(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
            print(f"  ❌ 缺少环境变量: {', '.join(missing_vars)}")
        
        # 检查数据库文件路径
        db_paths = {
            'metadata_db': getattr(settings, 'METADATA_DB_PATH', ''),
            'business_db': getattr(settings, 'BUSINESS_DB_PATH', '')
        }
        
        valid_paths = 0
        for db_name, db_path in db_paths.items():
            if db_path and Path(db_path).exists():
                valid_paths += 1
                print(f"  ✅ {db_name}: {db_path}")
            else:
                config_status['issues'].append(f"❌ {db_name}文件不存在: {db_path}")
                print(f"  ❌ {db_name}文件不存在: {db_path}")
        
        config_status['database_paths_valid'] = valid_paths == len(db_paths)
        self.results['components']['configuration'] = config_status
    
    def _check_database_connections(self):
        """检查数据库连接"""
        print("\n🗄️ 检查数据库连接...")
        
        db_status = {
            'metadata_db': self._test_sqlite_connection(settings.METADATA_DB_PATH, "元数据库"),
            'business_db': self._test_sqlite_connection(settings.BUSINESS_DB_PATH, "业务数据库"),
            'sqlalchemy_session': self._test_sqlalchemy_session()
        }
        
        self.results['components']['databases'] = db_status
    
    def _test_sqlite_connection(self, db_path: str, db_name: str):
        """测试SQLite数据库连接"""
        try:
            conn = sqlite3.connect(db_path, timeout=5)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            
            # 获取表数量
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"  ✅ {db_name}连接成功 (包含{table_count}个表)")
            return {
                'healthy': True,
                'table_count': table_count,
                'path': db_path
            }
            
        except Exception as e:
            print(f"  ❌ {db_name}连接失败: {e}")
            return {
                'healthy': False,
                'error': str(e),
                'path': db_path
            }
    
    def _test_sqlalchemy_session(self):
        """测试SQLAlchemy会话"""
        try:
            db = SessionLocal()
            # 执行简单查询测试连接
            result = db.execute("SELECT 1").fetchone()
            db.close()
            
            print("  ✅ SQLAlchemy会话连接成功")
            return {'healthy': True}
            
        except Exception as e:
            print(f"  ❌ SQLAlchemy会话连接失败: {e}")
            return {'healthy': False, 'error': str(e)}
    
    def _check_metadata_integrity(self):
        """检查元数据完整性"""
        print("\n📊 检查元数据完整性...")
        
        metadata_status = {
            'tables_exist': False,
            'data_present': False,
            'table_counts': {},
            'issues': []
        }
        
        try:
            conn = sqlite3.connect(settings.METADATA_DB_PATH)
            cursor = conn.cursor()
            
            # 检查元数据表是否存在
            required_tables = ['table_descriptions', 'column_descriptions', 'business_rules']
            existing_tables = []
            
            for table in required_tables:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    existing_tables.append(table)
                    
                    # 获取表中数据数量
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    metadata_status['table_counts'][table] = count
                    print(f"  ✅ {table}: {count}条记录")
                else:
                    metadata_status['issues'].append(f"❌ 缺少表: {table}")
                    print(f"  ❌ 缺少表: {table}")
            
            metadata_status['tables_exist'] = len(existing_tables) == len(required_tables)
            metadata_status['data_present'] = sum(metadata_status['table_counts'].values()) > 0
            
            conn.close()
            
        except Exception as e:
            metadata_status['issues'].append(f"❌ 元数据检查失败: {e}")
            print(f"  ❌ 元数据检查失败: {e}")
        
        self.results['components']['metadata'] = metadata_status
    
    def _check_cache_system(self):
        """检查缓存系统"""
        print("\n🚀 检查缓存系统...")
        
        cache_status = {
            'service_available': False,
            'redis_enabled': False,
            'memory_cache_working': False,
            'stats': {}
        }
        
        try:
            # 测试内存缓存
            cache_service = CacheService()
            
            # 测试缓存设置和获取
            test_key = "diagnostic_test"
            test_value = {"test": True, "timestamp": time.time()}
            
            cache_service.set(test_key, test_value, 60)
            retrieved_value = cache_service.get(test_key)
            
            if retrieved_value == test_value:
                cache_status['memory_cache_working'] = True
                print("  ✅ 内存缓存工作正常")
            else:
                print("  ❌ 内存缓存测试失败")
            
            # 获取缓存统计
            cache_status['stats'] = cache_service.get_stats()
            cache_status['service_available'] = True
            
            # 清理测试数据
            cache_service.delete(test_key)
            
        except Exception as e:
            print(f"  ❌ 缓存系统检查失败: {e}")
            cache_status['error'] = str(e)
        
        # 检查Redis配置
        redis_enabled = getattr(settings, 'REDIS_ENABLED', False)
        cache_status['redis_enabled'] = redis_enabled
        
        if redis_enabled:
            print("  ℹ️ Redis缓存已启用")
        else:
            print("  ℹ️ Redis缓存未启用，使用内存缓存")
        
        self.results['components']['cache'] = cache_status
    
    def _check_llm_api(self):
        """检查LLM API连接"""
        print("\n🤖 检查LLM API连接...")
        
        llm_status = {
            'api_accessible': False,
            'model_available': False,
            'response_time': None,
            'api_base': getattr(settings, 'OPENAI_API_BASE', ''),
            'model': getattr(settings, 'LLM_MODEL', '')
        }
        
        try:
            api_key = getattr(settings, 'OPENAI_API_KEY', '')
            api_base = llm_status['api_base']
            
            if not api_key or not api_base:
                print("  ❌ LLM API配置不完整")
                llm_status['error'] = "API配置不完整"
                self.results['components']['llm_api'] = llm_status
                return
            
            # 测试API连接
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            # 测试模型列表接口
            start_time = time.time()
            response = requests.get(f"{api_base}/models", headers=headers, timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                llm_status['api_accessible'] = True
                llm_status['response_time'] = round(response_time, 3)
                print(f"  ✅ LLM API连接成功 (响应时间: {response_time:.3f}s)")
                
                # 检查模型是否可用
                models_data = response.json()
                available_models = [model.get('id', '') for model in models_data.get('data', [])]
                
                if llm_status['model'] in available_models:
                    llm_status['model_available'] = True
                    print(f"  ✅ 模型 {llm_status['model']} 可用")
                else:
                    print(f"  ⚠️ 模型 {llm_status['model']} 不在可用列表中")
                    print(f"  可用模型: {', '.join(available_models[:5])}...")
            else:
                print(f"  ❌ LLM API连接失败: HTTP {response.status_code}")
                llm_status['error'] = f"HTTP {response.status_code}: {response.text}"
                
        except Exception as e:
            print(f"  ❌ LLM API检查失败: {e}")
            llm_status['error'] = str(e)
        
        self.results['components']['llm_api'] = llm_status
    
    def _check_neo4j_connection(self):
        """检查Neo4j连接"""
        print("\n🕸️ 检查Neo4j连接...")
        
        neo4j_status = {
            'connection_available': False,
            'database_accessible': False,
            'node_count': 0
        }
        
        try:
            # 尝试导入neo4j驱动
            from neo4j import GraphDatabase
            
            # 从环境变量获取Neo4j配置
            neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
            neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
            neo4j_password = os.getenv('NEO4J_PASSWORD', 'password')
            
            print(f"  尝试连接: {neo4j_uri}")
            
            driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
            
            with driver.session() as session:
                # 测试连接
                result = session.run("RETURN 1 as test")
                test_value = result.single()['test']
                
                if test_value == 1:
                    neo4j_status['connection_available'] = True
                    print("  ✅ Neo4j连接成功")
                    
                    # 获取节点数量
                    result = session.run("MATCH (n) RETURN count(n) as node_count")
                    node_count = result.single()['node_count']
                    neo4j_status['node_count'] = node_count
                    neo4j_status['database_accessible'] = True
                    
                    print(f"  ✅ 数据库可访问，包含 {node_count} 个节点")
            
            driver.close()
            
        except ImportError:
            print("  ❌ Neo4j驱动未安装")
            neo4j_status['error'] = "Neo4j驱动未安装"
        except Exception as e:
            print(f"  ❌ Neo4j连接失败: {e}")
            neo4j_status['error'] = str(e)
        
        self.results['components']['neo4j'] = neo4j_status
    
    def _generate_report(self):
        """生成诊断报告"""
        print("\n" + "=" * 60)
        print("📋 诊断报告")
        print("=" * 60)
        
        # 统计组件状态
        healthy_components = 0
        total_components = len(self.results['components'])
        
        for component_name, component_status in self.results['components'].items():
            if self._is_component_healthy(component_status):
                healthy_components += 1
        
        # 确定整体状态
        if healthy_components == total_components:
            self.results['overall_status'] = 'HEALTHY'
            print("🟢 系统整体状态: 健康")
        elif healthy_components >= total_components * 0.7:
            self.results['overall_status'] = 'WARNING'
            print("🟡 系统整体状态: 警告")
        else:
            self.results['overall_status'] = 'CRITICAL'
            print("🔴 系统整体状态: 严重")
        
        print(f"健康组件: {healthy_components}/{total_components}")
        
        # 生成建议
        self._generate_recommendations()
        
        # 保存报告到文件
        report_file = f"system_diagnosis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
    
    def _is_component_healthy(self, component_status):
        """判断组件是否健康"""
        if isinstance(component_status, dict):
            return component_status.get('healthy', True) and not component_status.get('issues', [])
        return True
    
    def _generate_recommendations(self):
        """生成修复建议"""
        recommendations = []
        
        # 检查各组件状态并生成建议
        for component_name, component_status in self.results['components'].items():
            if not self._is_component_healthy(component_status):
                if component_name == 'databases':
                    recommendations.append("检查数据库文件路径和权限")
                elif component_name == 'metadata':
                    recommendations.append("重新初始化元数据表")
                elif component_name == 'llm_api':
                    recommendations.append("检查LLM API密钥和网络连接")
                elif component_name == 'neo4j':
                    recommendations.append("检查Neo4j服务状态和连接配置")
        
        self.results['recommendations'] = recommendations
        
        if recommendations:
            print("\n🔧 修复建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")

def main():
    """主函数"""
    print("🚀 智能数据分析系统诊断工具")
    print(f"📅 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    diagnostics = SystemDiagnostics()
    results = diagnostics.run_full_diagnosis()
    
    return results

if __name__ == "__main__":
    main()
