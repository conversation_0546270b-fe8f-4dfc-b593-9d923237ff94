#!/usr/bin/env python3
"""
字段映射管理工具
提供添加、删除、查看字段映射的便捷接口
"""

import sqlite3
import sys

class MappingManager:
    def __init__(self):
        self.db_path = r'C:/Users/<USER>/PycharmProjects/智能数据分析系统/resource.db'
    
    def add_mapping(self, field_name, nl_term, description=""):
        """添加新的字段映射"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 获取字段ID
            cursor.execute('''
                SELECT sc.id 
                FROM schemacolumn sc 
                JOIN schematable st ON sc.table_id = st.id 
                WHERE sc.column_name = ? AND st.table_name = 'financial_data'
            ''', (field_name,))
            
            result = cursor.fetchone()
            if not result:
                print(f"❌ 字段 '{field_name}' 不存在")
                return False
            
            column_id = result[0]
            
            # 检查映射是否已存在
            cursor.execute('''
                SELECT id FROM valuemapping 
                WHERE column_id = ? AND nl_term = ?
            ''', (column_id, nl_term))
            
            if cursor.fetchone():
                print(f"⚠️ 映射 '{nl_term}' -> {field_name} 已存在")
                return False
            
            # 添加新映射
            cursor.execute('''
                INSERT INTO valuemapping (column_id, nl_term, db_value, created_at)
                VALUES (?, ?, ?, datetime('now'))
            ''', (column_id, nl_term, field_name))
            
            conn.commit()
            print(f"✅ 成功添加映射: '{nl_term}' -> {field_name}")
            return True
            
        except Exception as e:
            print(f"❌ 添加映射失败: {e}")
            return False
        finally:
            conn.close()
    
    def remove_mapping(self, nl_term):
        """删除字段映射"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 查找映射
            cursor.execute('''
                SELECT vm.id, sc.column_name
                FROM valuemapping vm
                JOIN schemacolumn sc ON vm.column_id = sc.id
                WHERE vm.nl_term = ?
            ''', (nl_term,))
            
            result = cursor.fetchone()
            if not result:
                print(f"❌ 映射 '{nl_term}' 不存在")
                return False
            
            mapping_id, field_name = result
            
            # 删除映射
            cursor.execute('DELETE FROM valuemapping WHERE id = ?', (mapping_id,))
            conn.commit()
            
            print(f"✅ 成功删除映射: '{nl_term}' -> {field_name}")
            return True
            
        except Exception as e:
            print(f"❌ 删除映射失败: {e}")
            return False
        finally:
            conn.close()
    
    def search_mappings(self, search_term):
        """搜索字段映射"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT vm.nl_term, sc.column_name, st.table_name
                FROM valuemapping vm
                JOIN schemacolumn sc ON vm.column_id = sc.id
                JOIN schematable st ON sc.table_id = st.id
                WHERE vm.nl_term LIKE ? OR sc.column_name LIKE ?
                ORDER BY sc.column_name, vm.nl_term
            ''', (f'%{search_term}%', f'%{search_term}%'))
            
            results = cursor.fetchall()
            
            if results:
                print(f"🔍 搜索 '{search_term}' 的结果:")
                for nl_term, column_name, table_name in results:
                    print(f"   '{nl_term}' -> {table_name}.{column_name}")
            else:
                print(f"❌ 没有找到包含 '{search_term}' 的映射")
            
            return results
            
        except Exception as e:
            print(f"❌ 搜索失败: {e}")
            return []
        finally:
            conn.close()
    
    def list_field_mappings(self, field_name):
        """列出指定字段的所有映射"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT vm.nl_term, vm.created_at
                FROM valuemapping vm
                JOIN schemacolumn sc ON vm.column_id = sc.id
                WHERE sc.column_name = ?
                ORDER BY vm.nl_term
            ''', (field_name,))
            
            results = cursor.fetchall()
            
            if results:
                print(f"📋 字段 '{field_name}' 的所有映射:")
                for nl_term, created_at in results:
                    print(f"   '{nl_term}' (添加时间: {created_at})")
            else:
                print(f"❌ 字段 '{field_name}' 没有映射")
            
            return results
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return []
        finally:
            conn.close()
    
    def batch_add_mappings(self, mappings_dict):
        """批量添加映射"""
        
        success_count = 0
        for field_name, nl_terms in mappings_dict.items():
            print(f"\n🔄 为字段 '{field_name}' 添加映射...")
            for nl_term in nl_terms:
                if self.add_mapping(field_name, nl_term):
                    success_count += 1
        
        print(f"\n✅ 批量添加完成，成功添加 {success_count} 个映射")

def main():
    """命令行界面"""
    
    manager = MappingManager()
    
    if len(sys.argv) < 2:
        print("""
🛠️ 字段映射管理工具

用法:
  python mapping_manager.py add <字段名> <自然语言术语>     # 添加映射
  python mapping_manager.py remove <自然语言术语>          # 删除映射
  python mapping_manager.py search <搜索词>               # 搜索映射
  python mapping_manager.py list <字段名>                 # 列出字段的所有映射

示例:
  python mapping_manager.py add accounting_unit_name "企业名"
  python mapping_manager.py remove "企业名"
  python mapping_manager.py search "公司"
  python mapping_manager.py list accounting_unit_name
        """)
        return
    
    command = sys.argv[1].lower()
    
    if command == 'add' and len(sys.argv) >= 4:
        field_name = sys.argv[2]
        nl_term = sys.argv[3]
        manager.add_mapping(field_name, nl_term)
        
    elif command == 'remove' and len(sys.argv) >= 3:
        nl_term = sys.argv[2]
        manager.remove_mapping(nl_term)
        
    elif command == 'search' and len(sys.argv) >= 3:
        search_term = sys.argv[2]
        manager.search_mappings(search_term)
        
    elif command == 'list' and len(sys.argv) >= 3:
        field_name = sys.argv[2]
        manager.list_field_mappings(field_name)
        
    else:
        print("❌ 无效的命令或参数不足")

if __name__ == "__main__":
    main()
