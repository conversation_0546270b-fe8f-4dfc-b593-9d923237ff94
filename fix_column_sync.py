#!/usr/bin/env python3
"""
修复列数据同步问题
"""
import asyncio
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent / "chatdb" / "backend"
sys.path.insert(0, str(project_root))

async def check_column_data():
    """检查列数据状态"""
    print("🔍 检查列数据状态")
    print("=" * 40)
    
    try:
        from app.db.session import SessionLocal
        from app import crud
        from app.services.neo4j_connection_pool import get_neo4j_pool
        
        # 检查SQLite中的列数据
        db = SessionLocal()
        try:
            print("📊 SQLite数据检查:")
            
            # 检查连接1的表和列
            tables = crud.schema_table.get_by_connection(db, connection_id=1)
            print(f"  连接ID 1 的表数量: {len(tables)}")
            
            total_columns = 0
            for table in tables[:5]:  # 检查前5个表
                columns = crud.schema_column.get_by_table(db, table_id=table.id)
                print(f"    表 {table.table_name} (ID: {table.id}): {len(columns)} 列")
                total_columns += len(columns)
            
            print(f"  总列数 (前5表): {total_columns}")
            
            # 检查连接2的数据
            tables2 = crud.schema_table.get_by_connection(db, connection_id=2)
            print(f"  连接ID 2 的表数量: {len(tables2)}")
            
        finally:
            db.close()
        
        # 检查Neo4j中的列数据
        print("\n🔗 Neo4j数据检查:")
        pool = await get_neo4j_pool()
        
        # 检查表节点
        tables_result = await pool.execute_read_query("""
            MATCH (t:Table) 
            RETURN count(t) as table_count, collect(DISTINCT t.connection_id) as connection_ids
        """)
        
        if tables_result:
            print(f"  表节点数量: {tables_result[0]['table_count']}")
            print(f"  连接IDs: {tables_result[0]['connection_ids']}")
        
        # 检查列节点
        columns_result = await pool.execute_read_query("""
            MATCH (c:Column) 
            RETURN count(c) as column_count
        """)
        
        if columns_result:
            print(f"  列节点数量: {columns_result[0]['column_count']}")
        
        # 检查表-列关系
        relations_result = await pool.execute_read_query("""
            MATCH (t:Table)-[:HAS_COLUMN]->(c:Column) 
            RETURN count(*) as relation_count
        """)
        
        if relations_result:
            print(f"  表-列关系数量: {relations_result[0]['relation_count']}")
        
        # 检查特定连接的数据
        conn1_data = await pool.execute_read_query("""
            MATCH (t:Table {connection_id: 1})-[:HAS_COLUMN]->(c:Column)
            RETURN t.name as table_name, count(c) as column_count
            ORDER BY column_count DESC
            LIMIT 5
        """)
        
        print(f"\n  连接ID 1 的表-列数据 (前5个):")
        for record in conn1_data:
            print(f"    {record['table_name']}: {record['column_count']} 列")
        
        return len(conn1_data) > 0
        
    except Exception as e:
        print(f"❌ 数据检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def fix_column_sync():
    """修复列数据同步"""
    print("\n🔧 修复列数据同步")
    print("=" * 30)
    
    try:
        from app.services.schema_service import sync_schema_to_graph_db
        
        # 重新同步连接1的数据
        print("🔄 重新同步连接ID 1...")
        result1 = sync_schema_to_graph_db(1)
        print(f"  连接ID 1 同步结果: {'✅ 成功' if result1 else '❌ 失败'}")
        
        # 重新同步连接2的数据
        print("🔄 重新同步连接ID 2...")
        result2 = sync_schema_to_graph_db(2)
        print(f"  连接ID 2 同步结果: {'✅ 成功' if result2 else '❌ 失败'}")
        
        return result1 and result2
        
    except Exception as e:
        print(f"❌ 同步修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def verify_fix():
    """验证修复结果"""
    print("\n✅ 验证修复结果")
    print("=" * 30)
    
    try:
        from app.db.session import SessionLocal
        from app.services.text2sql_utils import retrieve_relevant_schema
        
        db = SessionLocal()
        try:
            # 重新测试Schema检索
            result = await retrieve_relevant_schema(db, 1, "查询销售数据")
            
            print(f"修复后的检索结果:")
            print(f"  表数量: {len(result.get('tables', []))}")
            print(f"  列数量: {len(result.get('columns', []))}")
            print(f"  关系数量: {len(result.get('relationships', []))}")
            
            if result.get('columns'):
                print(f"\n🏛️ 找到的列 (前5个):")
                for i, column in enumerate(result['columns'][:5]):
                    print(f"  {i+1}. {column.get('table_name', 'N/A')}.{column.get('name', 'N/A')} ({column.get('type', 'N/A')})")
                return True
            else:
                print("❌ 仍然没有找到列数据")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

async def main():
    print("🔧 列数据同步修复工具")
    print("=" * 50)
    
    # 步骤1: 检查当前状态
    has_columns = await check_column_data()
    
    if not has_columns:
        print("\n❌ 发现列数据缺失，开始修复...")
        
        # 步骤2: 执行修复
        fix_success = await fix_column_sync()
        
        if fix_success:
            # 步骤3: 验证修复
            verify_success = await verify_fix()
            
            if verify_success:
                print("\n🎉 列数据同步修复成功！")
            else:
                print("\n⚠️ 修复完成但验证失败，可能需要手动检查")
        else:
            print("\n❌ 修复失败，请检查错误信息")
    else:
        print("\n✅ 列数据正常，无需修复")
        
        # 仍然验证一下Schema检索
        verify_success = await verify_fix()
        if not verify_success:
            print("\n⚠️ 虽然Neo4j有列数据，但Schema检索仍有问题")

if __name__ == "__main__":
    asyncio.run(main())
