{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\pages\\\\ValueMappingsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Card, Select, Button, Table, Spin, message, Typography, Form, Input, Modal, Popconfirm, Space } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';\nimport * as api from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  Title\n} = Typography;\nconst ValueMappingsPage = () => {\n  _s();\n  var _tables$find, _tables$find2;\n  const [connections, setConnections] = useState([]);\n  const [selectedConnection, setSelectedConnection] = useState(null);\n  const [tables, setTables] = useState([]);\n  const [selectedTable, setSelectedTable] = useState(null);\n  const [columns, setColumns] = useState([]);\n  const [selectedColumn, setSelectedColumn] = useState(null);\n  const [valueMappings, setValueMappings] = useState([]);\n  const [totalMappings, setTotalMappings] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingMapping, setEditingMapping] = useState(null);\n  const [form] = Form.useForm();\n\n  // 新增：显示模式切换\n  const [viewMode, setViewMode] = useState('direct');\n\n  // 搜索功能\n  const [searchText, setSearchText] = useState('');\n\n  // Define fetch functions with useCallback\n  const fetchConnections = async () => {\n    try {\n      const response = await api.getConnections();\n      setConnections(response.data);\n    } catch (error) {\n      message.error('获取连接失败');\n      console.error(error);\n    }\n  };\n  const fetchTables = async connectionId => {\n    setLoading(true);\n    try {\n      const response = await api.getSchemaMetadata(connectionId);\n      setTables(response.data);\n      setSelectedTable(null);\n      setColumns([]);\n      setSelectedColumn(null);\n      setValueMappings([]);\n    } catch (error) {\n      message.error('获取表失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchColumns = useCallback(async tableId => {\n    setLoading(true);\n    try {\n      const selectedTableData = tables.find(t => t.id === tableId);\n      if (!selectedTableData) return;\n\n      // Get columns for this table from the schema metadata\n      const response = await api.getSchemaMetadata(selectedConnection);\n      const tableData = response.data.find(t => t.id === tableId);\n      if (tableData && tableData.columns) {\n        const columnsWithTableInfo = tableData.columns.map(col => ({\n          ...col,\n          table_id: tableId,\n          table_name: selectedTableData.table_name\n        }));\n        setColumns(columnsWithTableInfo);\n      } else {\n        setColumns([]);\n      }\n      setSelectedColumn(null);\n      setValueMappings([]);\n    } catch (error) {\n      message.error('获取列失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedConnection, tables]);\n  const fetchValueMappings = async columnId => {\n    setLoading(true);\n    try {\n      // 特殊处理：如果选择的是valuemapping表，直接获取所有映射数据\n      const selectedTableData = tables.find(t => t.id === selectedTable);\n      if ((selectedTableData === null || selectedTableData === void 0 ? void 0 : selectedTableData.table_name) === 'valuemapping') {\n        // 获取所有映射数据，不按column_id过滤\n        const response = await api.getValueMappings();\n        setValueMappings(response.data);\n        setTotalMappings(response.data.length);\n      } else {\n        // 正常的按column_id过滤逻辑\n        const response = await api.getValueMappings(columnId);\n        setValueMappings(response.data);\n        setTotalMappings(response.data.length);\n      }\n    } catch (error) {\n      message.error('获取值映射失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Setup effects\n  useEffect(() => {\n    fetchConnections();\n  }, []);\n  useEffect(() => {\n    if (selectedConnection) {\n      fetchTables(selectedConnection);\n    }\n  }, [selectedConnection]);\n  useEffect(() => {\n    if (selectedTable) {\n      fetchColumns(selectedTable);\n    }\n  }, [selectedTable, fetchColumns]);\n  useEffect(() => {\n    if (selectedColumn) {\n      fetchValueMappings(selectedColumn);\n    }\n  }, [selectedColumn]);\n  const handleConnectionChange = value => {\n    setSelectedConnection(value);\n  };\n  const handleTableChange = value => {\n    setSelectedTable(value);\n  };\n  const handleColumnChange = value => {\n    setSelectedColumn(value);\n  };\n  const showModal = mapping => {\n    setEditingMapping(mapping || null);\n    form.resetFields();\n    if (mapping) {\n      form.setFieldsValue({\n        nl_term: mapping.nl_term,\n        db_value: mapping.db_value\n      });\n    }\n    setModalVisible(true);\n  };\n  const handleCancel = () => {\n    setModalVisible(false);\n  };\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingMapping) {\n        await api.updateValueMapping(editingMapping.id, values);\n        message.success('值映射更新成功');\n      } else {\n        await api.createValueMapping({\n          ...values,\n          column_id: selectedColumn\n        });\n        message.success('值映射创建成功');\n      }\n      setModalVisible(false);\n      fetchValueMappings(selectedColumn);\n    } catch (error) {\n      message.error('保存值映射失败');\n      console.error(error);\n    }\n  };\n  const handleDelete = async id => {\n    try {\n      await api.deleteValueMapping(id);\n      message.success('值映射删除成功');\n      fetchValueMappings(selectedColumn);\n    } catch (error) {\n      message.error('删除值映射失败');\n      console.error(error);\n    }\n  };\n  const columns_table = [{\n    title: '自然语言术语',\n    dataIndex: 'nl_term',\n    key: 'nl_term',\n    width: '40%',\n    ellipsis: true\n  }, {\n    title: '数据库值',\n    dataIndex: 'db_value',\n    key: 'db_value',\n    width: '40%',\n    ellipsis: true\n  }, {\n    title: '操作',\n    key: 'actions',\n    width: '20%',\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 19\n        }, this),\n        onClick: () => showModal(record),\n        size: \"small\",\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u6620\\u5C04\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u662F\",\n        cancelText: \"\\u5426\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u503C\\u6620\\u5C04\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u6570\\u636E\\u5E93\\u8FDE\\u63A5\",\n            style: {\n              width: 200,\n              marginRight: 8\n            },\n            onChange: handleConnectionChange,\n            value: selectedConnection || undefined,\n            children: connections.map(conn => /*#__PURE__*/_jsxDEV(Option, {\n              value: conn.id,\n              children: conn.name\n            }, conn.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u8868\",\n            style: {\n              width: 200,\n              marginRight: 8\n            },\n            onChange: handleTableChange,\n            value: selectedTable || undefined,\n            disabled: !selectedConnection,\n            children: tables.map(table => /*#__PURE__*/_jsxDEV(Option, {\n              value: table.id,\n              children: table.table_name\n            }, table.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u5217\",\n            style: {\n              width: 200\n            },\n            onChange: handleColumnChange,\n            value: selectedColumn || undefined,\n            disabled: !selectedTable,\n            children: columns.map(column => /*#__PURE__*/_jsxDEV(Option, {\n              value: column.id,\n              children: column.column_name\n            }, column.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), selectedColumn && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16,\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 21\n          }, this),\n          onClick: () => showModal(),\n          children: \"\\u6DFB\\u52A0\\u503C\\u6620\\u5C04\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input.Search, {\n          placeholder: \"\\u641C\\u7D22\\u81EA\\u7136\\u8BED\\u8A00\\u672F\\u8BED\\u6216\\u6570\\u636E\\u5E93\\u503C\",\n          allowClear: true,\n          style: {\n            width: 300\n          },\n          onChange: e => setSearchText(e.target.value),\n          onSearch: value => setSearchText(value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Spin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 11\n      }, this) : selectedColumn ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [((_tables$find = tables.find(t => t.id === selectedTable)) === null || _tables$find === void 0 ? void 0 : _tables$find.table_name) === 'valuemapping' && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 16,\n            padding: '8px 12px',\n            backgroundColor: '#f6ffed',\n            border: '1px solid #b7eb8f',\n            borderRadius: '4px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#52c41a'\n            },\n            children: [\"\\uD83D\\uDCA1 \\u63D0\\u793A\\uFF1A\\u663E\\u793A\\u6240\\u6709\\u5B57\\u6BB5\\u6620\\u5C04\\u5173\\u7CFB (\", valueMappings.length, \" \\u4E2A\\u6620\\u5C04)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Table, {\n          columns: columns_table,\n          dataSource: valueMappings.filter(mapping => !searchText || mapping.nl_term.toLowerCase().includes(searchText.toLowerCase()) || mapping.db_value.toLowerCase().includes(searchText.toLowerCase())),\n          rowKey: \"id\",\n          pagination: {\n            pageSize: 20,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,\n            pageSizeOptions: ['10', '20', '50', '100']\n          },\n          scroll: {\n            y: 400,\n            // 设置表格最大高度，超出时显示滚动条\n            x: 'max-content' // 水平滚动\n          },\n          size: \"middle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u8BF7\\u9009\\u62E9\\u8FDE\\u63A5\\u3001\\u8868\\u548C\\u5217\\u6765\\u7BA1\\u7406\\u503C\\u6620\\u5C04\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this), selectedConnection === 2 && selectedTable && ((_tables$find2 = tables.find(t => t.id === selectedTable)) === null || _tables$find2 === void 0 ? void 0 : _tables$find2.table_name) === 'valuemapping' && /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#1890ff',\n            marginTop: '8px'\n          },\n          children: [\"\\uD83D\\uDCA1 \\u9009\\u62E9\\u4EFB\\u610F\\u5B57\\u6BB5\\u5373\\u53EF\\u67E5\\u770B\\u6240\\u6709\\u5B57\\u6BB5\\u6620\\u5C04\\u5173\\u7CFB\", valueMappings.length > 0 && ` (共 ${valueMappings.length} 个)`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingMapping ? '编辑值映射' : '添加值映射',\n      open: modalVisible,\n      onOk: handleSubmit,\n      onCancel: handleCancel,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"nl_term\",\n          label: \"\\u81EA\\u7136\\u8BED\\u8A00\\u672F\\u8BED\",\n          rules: [{\n            required: true,\n            message: '请输入自然语言术语'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"e.g., \\u4E2D\\u77F3\\u5316\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"db_value\",\n          label: \"\\u6570\\u636E\\u5E93\\u503C\",\n          rules: [{\n            required: true,\n            message: '请输入数据库值'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"e.g., \\u4E2D\\u56FD\\u77F3\\u5316\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 276,\n    columnNumber: 5\n  }, this);\n};\n_s(ValueMappingsPage, \"GXn1ct0G22kTc2x++E3RCqDcbl8=\", false, function () {\n  return [Form.useForm];\n});\n_c = ValueMappingsPage;\nexport default ValueMappingsPage;\nvar _c;\n$RefreshReg$(_c, \"ValueMappingsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Card", "Select", "<PERSON><PERSON>", "Table", "Spin", "message", "Typography", "Form", "Input", "Modal", "Popconfirm", "Space", "PlusOutlined", "EditOutlined", "DeleteOutlined", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "Title", "ValueMappingsPage", "_s", "_tables$find", "_tables$find2", "connections", "setConnections", "selectedConnection", "setSelectedConnection", "tables", "setTables", "selectedTable", "setSelectedTable", "columns", "setColumns", "selectedColumn", "setSelectedColumn", "valueMappings", "setValueMappings", "totalMappings", "setTotalMappings", "loading", "setLoading", "modalVisible", "setModalVisible", "editingMapping", "setEditingMapping", "form", "useForm", "viewMode", "setViewMode", "searchText", "setSearchText", "fetchConnections", "response", "getConnections", "data", "error", "console", "fetchTables", "connectionId", "getSchemaMetadata", "fetchColumns", "tableId", "selectedTableData", "find", "t", "id", "tableData", "columnsWithTableInfo", "map", "col", "table_id", "table_name", "fetchValueMappings", "columnId", "getValueMappings", "length", "handleConnectionChange", "value", "handleTableChange", "handleColumnChange", "showModal", "mapping", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nl_term", "db_value", "handleCancel", "handleSubmit", "values", "validateFields", "updateValueMapping", "success", "createValueMapping", "column_id", "handleDelete", "deleteValueMapping", "columns_table", "title", "dataIndex", "key", "width", "ellipsis", "fixed", "render", "_", "record", "size", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onConfirm", "okText", "cancelText", "danger", "style", "display", "justifyContent", "marginBottom", "level", "placeholder", "marginRight", "onChange", "undefined", "conn", "name", "disabled", "table", "column", "column_name", "alignItems", "type", "Search", "allowClear", "e", "target", "onSearch", "textAlign", "padding", "backgroundColor", "border", "borderRadius", "color", "dataSource", "filter", "toLowerCase", "includes", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "pageSizeOptions", "scroll", "y", "x", "marginTop", "open", "onOk", "onCancel", "layout", "<PERSON><PERSON>", "label", "rules", "required", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/pages/ValueMappingsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Card, Select, Button, Table, Spin, message,\n  Typography, Form, Input, Modal, Popconfirm, Space\n} from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';\nimport * as api from '../services/api';\n\nconst { Option } = Select;\nconst { Title } = Typography;\n\ninterface DBConnection {\n  id: number;\n  name: string;\n}\n\ninterface SchemaTable {\n  id: number;\n  table_name: string;\n  description?: string;\n}\n\ninterface SchemaColumn {\n  id: number;\n  column_name: string;\n  data_type: string;\n  description?: string;\n  table_id: number;\n  table_name: string;\n}\n\ninterface ValueMapping {\n  id: number;\n  column_id: number;\n  nl_term: string;\n  db_value: string;\n}\n\nconst ValueMappingsPage: React.FC = () => {\n  const [connections, setConnections] = useState<DBConnection[]>([]);\n  const [selectedConnection, setSelectedConnection] = useState<number | null>(null);\n  const [tables, setTables] = useState<SchemaTable[]>([]);\n  const [selectedTable, setSelectedTable] = useState<number | null>(null);\n  const [columns, setColumns] = useState<SchemaColumn[]>([]);\n  const [selectedColumn, setSelectedColumn] = useState<number | null>(null);\n  const [valueMappings, setValueMappings] = useState<ValueMapping[]>([]);\n  const [totalMappings, setTotalMappings] = useState<number>(0);\n  const [loading, setLoading] = useState<boolean>(false);\n  const [modalVisible, setModalVisible] = useState<boolean>(false);\n  const [editingMapping, setEditingMapping] = useState<ValueMapping | null>(null);\n  const [form] = Form.useForm();\n\n  // 新增：显示模式切换\n  const [viewMode, setViewMode] = useState<'hierarchy' | 'direct'>('direct');\n\n  // 搜索功能\n  const [searchText, setSearchText] = useState<string>('');\n\n  // Define fetch functions with useCallback\n  const fetchConnections = async () => {\n    try {\n      const response = await api.getConnections();\n      setConnections(response.data);\n    } catch (error) {\n      message.error('获取连接失败');\n      console.error(error);\n    }\n  };\n\n  const fetchTables = async (connectionId: number) => {\n    setLoading(true);\n    try {\n      const response = await api.getSchemaMetadata(connectionId);\n      setTables(response.data);\n      setSelectedTable(null);\n      setColumns([]);\n      setSelectedColumn(null);\n      setValueMappings([]);\n    } catch (error) {\n      message.error('获取表失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchColumns = useCallback(async (tableId: number) => {\n    setLoading(true);\n    try {\n      const selectedTableData = tables.find(t => t.id === tableId);\n      if (!selectedTableData) return;\n\n      // Get columns for this table from the schema metadata\n      const response = await api.getSchemaMetadata(selectedConnection!);\n      const tableData = response.data.find((t: any) => t.id === tableId);\n\n      if (tableData && tableData.columns) {\n        const columnsWithTableInfo = tableData.columns.map((col: any) => ({\n          ...col,\n          table_id: tableId,\n          table_name: selectedTableData.table_name\n        }));\n        setColumns(columnsWithTableInfo);\n      } else {\n        setColumns([]);\n      }\n\n      setSelectedColumn(null);\n      setValueMappings([]);\n    } catch (error) {\n      message.error('获取列失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedConnection, tables]);\n\n  const fetchValueMappings = async (columnId: number) => {\n    setLoading(true);\n    try {\n      // 特殊处理：如果选择的是valuemapping表，直接获取所有映射数据\n      const selectedTableData = tables.find(t => t.id === selectedTable);\n      if (selectedTableData?.table_name === 'valuemapping') {\n        // 获取所有映射数据，不按column_id过滤\n        const response = await api.getValueMappings();\n        setValueMappings(response.data);\n        setTotalMappings(response.data.length);\n      } else {\n        // 正常的按column_id过滤逻辑\n        const response = await api.getValueMappings(columnId);\n        setValueMappings(response.data);\n        setTotalMappings(response.data.length);\n      }\n    } catch (error) {\n      message.error('获取值映射失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Setup effects\n  useEffect(() => {\n    fetchConnections();\n  }, []);\n\n  useEffect(() => {\n    if (selectedConnection) {\n      fetchTables(selectedConnection);\n    }\n  }, [selectedConnection]);\n\n  useEffect(() => {\n    if (selectedTable) {\n      fetchColumns(selectedTable);\n    }\n  }, [selectedTable, fetchColumns]);\n\n  useEffect(() => {\n    if (selectedColumn) {\n      fetchValueMappings(selectedColumn);\n    }\n  }, [selectedColumn]);\n\n  const handleConnectionChange = (value: number) => {\n    setSelectedConnection(value);\n  };\n\n  const handleTableChange = (value: number) => {\n    setSelectedTable(value);\n  };\n\n  const handleColumnChange = (value: number) => {\n    setSelectedColumn(value);\n  };\n\n  const showModal = (mapping?: ValueMapping) => {\n    setEditingMapping(mapping || null);\n    form.resetFields();\n    if (mapping) {\n      form.setFieldsValue({\n        nl_term: mapping.nl_term,\n        db_value: mapping.db_value,\n      });\n    }\n    setModalVisible(true);\n  };\n\n  const handleCancel = () => {\n    setModalVisible(false);\n  };\n\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n\n      if (editingMapping) {\n        await api.updateValueMapping(editingMapping.id, values);\n        message.success('值映射更新成功');\n      } else {\n        await api.createValueMapping({\n          ...values,\n          column_id: selectedColumn\n        });\n        message.success('值映射创建成功');\n      }\n\n      setModalVisible(false);\n      fetchValueMappings(selectedColumn!);\n    } catch (error) {\n      message.error('保存值映射失败');\n      console.error(error);\n    }\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await api.deleteValueMapping(id);\n      message.success('值映射删除成功');\n      fetchValueMappings(selectedColumn!);\n    } catch (error) {\n      message.error('删除值映射失败');\n      console.error(error);\n    }\n  };\n\n  const columns_table = [\n    {\n      title: '自然语言术语',\n      dataIndex: 'nl_term',\n      key: 'nl_term',\n      width: '40%',\n      ellipsis: true,\n    },\n    {\n      title: '数据库值',\n      dataIndex: 'db_value',\n      key: 'db_value',\n      width: '40%',\n      ellipsis: true,\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      width: '20%',\n      fixed: 'right' as const,\n      render: (_: any, record: ValueMapping) => (\n        <Space size=\"middle\">\n          <Button\n            icon={<EditOutlined />}\n            onClick={() => showModal(record)}\n            size=\"small\"\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个映射吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"是\"\n            cancelText=\"否\"\n          >\n            <Button\n              danger\n              icon={<DeleteOutlined />}\n              size=\"small\"\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>\n          <Title level={4}>值映射</Title>\n          <div>\n            <Select\n              placeholder=\"选择数据库连接\"\n              style={{ width: 200, marginRight: 8 }}\n              onChange={handleConnectionChange}\n              value={selectedConnection || undefined}\n            >\n              {connections.map(conn => (\n                <Option key={conn.id} value={conn.id}>{conn.name}</Option>\n              ))}\n            </Select>\n\n            <Select\n              placeholder=\"选择表\"\n              style={{ width: 200, marginRight: 8 }}\n              onChange={handleTableChange}\n              value={selectedTable || undefined}\n              disabled={!selectedConnection}\n            >\n              {tables.map(table => (\n                <Option key={table.id} value={table.id}>{table.table_name}</Option>\n              ))}\n            </Select>\n\n            <Select\n              placeholder=\"选择列\"\n              style={{ width: 200 }}\n              onChange={handleColumnChange}\n              value={selectedColumn || undefined}\n              disabled={!selectedTable}\n            >\n              {columns.map(column => (\n                <Option key={column.id} value={column.id}>{column.column_name}</Option>\n              ))}\n            </Select>\n          </div>\n        </div>\n\n        {selectedColumn && (\n          <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={() => showModal()}\n            >\n              添加值映射\n            </Button>\n\n            {/* 搜索框 */}\n            <Input.Search\n              placeholder=\"搜索自然语言术语或数据库值\"\n              allowClear\n              style={{ width: 300 }}\n              onChange={(e) => setSearchText(e.target.value)}\n              onSearch={(value) => setSearchText(value)}\n            />\n          </div>\n        )}\n\n        {loading ? (\n          <div style={{ textAlign: 'center', padding: '20px' }}>\n            <Spin />\n          </div>\n        ) : selectedColumn ? (\n          <>\n            {/* 特殊提示：当选择valuemapping表时 */}\n            {tables.find(t => t.id === selectedTable)?.table_name === 'valuemapping' && (\n              <div style={{ marginBottom: 16, padding: '8px 12px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '4px' }}>\n                <span style={{ color: '#52c41a' }}>\n                  💡 提示：显示所有字段映射关系 ({valueMappings.length} 个映射)\n                </span>\n              </div>\n            )}\n            <Table\n              columns={columns_table}\n              dataSource={valueMappings.filter(mapping =>\n                !searchText ||\n                mapping.nl_term.toLowerCase().includes(searchText.toLowerCase()) ||\n                mapping.db_value.toLowerCase().includes(searchText.toLowerCase())\n              )}\n              rowKey=\"id\"\n              pagination={{\n                pageSize: 20,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,\n                pageSizeOptions: ['10', '20', '50', '100'],\n              }}\n              scroll={{\n                y: 400,  // 设置表格最大高度，超出时显示滚动条\n                x: 'max-content'  // 水平滚动\n              }}\n              size=\"middle\"\n            />\n          </>\n        ) : (\n          <div style={{ textAlign: 'center', padding: '20px' }}>\n            <p>请选择连接、表和列来管理值映射</p>\n            {selectedConnection === 2 && selectedTable && tables.find(t => t.id === selectedTable)?.table_name === 'valuemapping' && (\n              <p style={{ color: '#1890ff', marginTop: '8px' }}>\n                💡 选择任意字段即可查看所有字段映射关系\n                {valueMappings.length > 0 && ` (共 ${valueMappings.length} 个)`}\n              </p>\n            )}\n          </div>\n        )}\n      </Card>\n\n      <Modal\n        title={editingMapping ? '编辑值映射' : '添加值映射'}\n        open={modalVisible}\n        onOk={handleSubmit}\n        onCancel={handleCancel}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"nl_term\"\n            label=\"自然语言术语\"\n            rules={[{ required: true, message: '请输入自然语言术语' }]}\n          >\n            <Input placeholder=\"e.g., 中石化\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"db_value\"\n            label=\"数据库值\"\n            rules={[{ required: true, message: '请输入数据库值' }]}\n          >\n            <Input placeholder=\"e.g., 中国石化\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ValueMappingsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,EAC1CC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,QAC5C,MAAM;AACb,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,QAAQ,mBAAmB;AAC9E,OAAO,KAAKC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAM;EAAEC;AAAO,CAAC,GAAGnB,MAAM;AACzB,MAAM;EAAEoB;AAAM,CAAC,GAAGf,UAAU;AA6B5B,MAAMgB,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA;EACxC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAiB,EAAE,CAAC;EAClE,MAAM,CAAC+B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhC,QAAQ,CAAgB,IAAI,CAAC;EACjF,MAAM,CAACiC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAgB,EAAE,CAAC;EACvD,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAiB,EAAE,CAAC;EAC1D,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAAC2C,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAS,CAAC,CAAC;EAC7D,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAU,KAAK,CAAC;EAChE,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAsB,IAAI,CAAC;EAC/E,MAAM,CAACmD,IAAI,CAAC,GAAGzC,IAAI,CAAC0C,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAyB,QAAQ,CAAC;;EAE1E;EACA,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAS,EAAE,CAAC;;EAExD;EACA,MAAMyD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMxC,GAAG,CAACyC,cAAc,CAAC,CAAC;MAC3C7B,cAAc,CAAC4B,QAAQ,CAACE,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,QAAQ,CAAC;MACvBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAME,WAAW,GAAG,MAAOC,YAAoB,IAAK;IAClDlB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAMxC,GAAG,CAAC+C,iBAAiB,CAACD,YAAY,CAAC;MAC1D9B,SAAS,CAACwB,QAAQ,CAACE,IAAI,CAAC;MACxBxB,gBAAgB,CAAC,IAAI,CAAC;MACtBE,UAAU,CAAC,EAAE,CAAC;MACdE,iBAAiB,CAAC,IAAI,CAAC;MACvBE,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,OAAO,CAAC;MACtBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,YAAY,GAAGhE,WAAW,CAAC,MAAOiE,OAAe,IAAK;IAC1DrB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMsB,iBAAiB,GAAGnC,MAAM,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,OAAO,CAAC;MAC5D,IAAI,CAACC,iBAAiB,EAAE;;MAExB;MACA,MAAMV,QAAQ,GAAG,MAAMxC,GAAG,CAAC+C,iBAAiB,CAAClC,kBAAmB,CAAC;MACjE,MAAMyC,SAAS,GAAGd,QAAQ,CAACE,IAAI,CAACS,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACC,EAAE,KAAKJ,OAAO,CAAC;MAElE,IAAIK,SAAS,IAAIA,SAAS,CAACnC,OAAO,EAAE;QAClC,MAAMoC,oBAAoB,GAAGD,SAAS,CAACnC,OAAO,CAACqC,GAAG,CAAEC,GAAQ,KAAM;UAChE,GAAGA,GAAG;UACNC,QAAQ,EAAET,OAAO;UACjBU,UAAU,EAAET,iBAAiB,CAACS;QAChC,CAAC,CAAC,CAAC;QACHvC,UAAU,CAACmC,oBAAoB,CAAC;MAClC,CAAC,MAAM;QACLnC,UAAU,CAAC,EAAE,CAAC;MAChB;MAEAE,iBAAiB,CAAC,IAAI,CAAC;MACvBE,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,OAAO,CAAC;MACtBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACf,kBAAkB,EAAEE,MAAM,CAAC,CAAC;EAEhC,MAAM6C,kBAAkB,GAAG,MAAOC,QAAgB,IAAK;IACrDjC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMsB,iBAAiB,GAAGnC,MAAM,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKpC,aAAa,CAAC;MAClE,IAAI,CAAAiC,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAES,UAAU,MAAK,cAAc,EAAE;QACpD;QACA,MAAMnB,QAAQ,GAAG,MAAMxC,GAAG,CAAC8D,gBAAgB,CAAC,CAAC;QAC7CtC,gBAAgB,CAACgB,QAAQ,CAACE,IAAI,CAAC;QAC/BhB,gBAAgB,CAACc,QAAQ,CAACE,IAAI,CAACqB,MAAM,CAAC;MACxC,CAAC,MAAM;QACL;QACA,MAAMvB,QAAQ,GAAG,MAAMxC,GAAG,CAAC8D,gBAAgB,CAACD,QAAQ,CAAC;QACrDrC,gBAAgB,CAACgB,QAAQ,CAACE,IAAI,CAAC;QAC/BhB,gBAAgB,CAACc,QAAQ,CAACE,IAAI,CAACqB,MAAM,CAAC;MACxC;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,SAAS,CAAC;MACxBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA7C,SAAS,CAAC,MAAM;IACdwD,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAENxD,SAAS,CAAC,MAAM;IACd,IAAI8B,kBAAkB,EAAE;MACtBgC,WAAW,CAAChC,kBAAkB,CAAC;IACjC;EACF,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB9B,SAAS,CAAC,MAAM;IACd,IAAIkC,aAAa,EAAE;MACjB+B,YAAY,CAAC/B,aAAa,CAAC;IAC7B;EACF,CAAC,EAAE,CAACA,aAAa,EAAE+B,YAAY,CAAC,CAAC;EAEjCjE,SAAS,CAAC,MAAM;IACd,IAAIsC,cAAc,EAAE;MAClBuC,kBAAkB,CAACvC,cAAc,CAAC;IACpC;EACF,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAEpB,MAAM2C,sBAAsB,GAAIC,KAAa,IAAK;IAChDnD,qBAAqB,CAACmD,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMC,iBAAiB,GAAID,KAAa,IAAK;IAC3C/C,gBAAgB,CAAC+C,KAAK,CAAC;EACzB,CAAC;EAED,MAAME,kBAAkB,GAAIF,KAAa,IAAK;IAC5C3C,iBAAiB,CAAC2C,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMG,SAAS,GAAIC,OAAsB,IAAK;IAC5CrC,iBAAiB,CAACqC,OAAO,IAAI,IAAI,CAAC;IAClCpC,IAAI,CAACqC,WAAW,CAAC,CAAC;IAClB,IAAID,OAAO,EAAE;MACXpC,IAAI,CAACsC,cAAc,CAAC;QAClBC,OAAO,EAAEH,OAAO,CAACG,OAAO;QACxBC,QAAQ,EAAEJ,OAAO,CAACI;MACpB,CAAC,CAAC;IACJ;IACA3C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4C,YAAY,GAAGA,CAAA,KAAM;IACzB5C,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAM6C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM3C,IAAI,CAAC4C,cAAc,CAAC,CAAC;MAE1C,IAAI9C,cAAc,EAAE;QAClB,MAAM/B,GAAG,CAAC8E,kBAAkB,CAAC/C,cAAc,CAACsB,EAAE,EAAEuB,MAAM,CAAC;QACvDtF,OAAO,CAACyF,OAAO,CAAC,SAAS,CAAC;MAC5B,CAAC,MAAM;QACL,MAAM/E,GAAG,CAACgF,kBAAkB,CAAC;UAC3B,GAAGJ,MAAM;UACTK,SAAS,EAAE5D;QACb,CAAC,CAAC;QACF/B,OAAO,CAACyF,OAAO,CAAC,SAAS,CAAC;MAC5B;MAEAjD,eAAe,CAAC,KAAK,CAAC;MACtB8B,kBAAkB,CAACvC,cAAe,CAAC;IACrC,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,SAAS,CAAC;MACxBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMuC,YAAY,GAAG,MAAO7B,EAAU,IAAK;IACzC,IAAI;MACF,MAAMrD,GAAG,CAACmF,kBAAkB,CAAC9B,EAAE,CAAC;MAChC/D,OAAO,CAACyF,OAAO,CAAC,SAAS,CAAC;MAC1BnB,kBAAkB,CAACvC,cAAe,CAAC;IACrC,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,SAAS,CAAC;MACxBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMyC,aAAa,GAAG,CACpB;IACEC,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,KAAK;IACZE,KAAK,EAAE,OAAgB;IACvBC,MAAM,EAAEA,CAACC,CAAM,EAAEC,MAAoB,kBACnC3F,OAAA,CAACN,KAAK;MAACkG,IAAI,EAAC,QAAQ;MAAAC,QAAA,gBAClB7F,OAAA,CAACf,MAAM;QACL6G,IAAI,eAAE9F,OAAA,CAACJ,YAAY;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,OAAO,EAAEA,CAAA,KAAMjC,SAAS,CAACyB,MAAM,CAAE;QACjCC,IAAI,EAAC,OAAO;QAAAC,QAAA,EACb;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlG,OAAA,CAACP,UAAU;QACT0F,KAAK,EAAC,oEAAa;QACnBiB,SAAS,EAAEA,CAAA,KAAMpB,YAAY,CAACW,MAAM,CAACxC,EAAE,CAAE;QACzCkD,MAAM,EAAC,QAAG;QACVC,UAAU,EAAC,QAAG;QAAAT,QAAA,eAEd7F,OAAA,CAACf,MAAM;UACLsH,MAAM;UACNT,IAAI,eAAE9F,OAAA,CAACH,cAAc;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBN,IAAI,EAAC,OAAO;UAAAC,QAAA,EACb;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACElG,OAAA;IAAA6F,QAAA,gBACE7F,OAAA,CAACjB,IAAI;MAAA8G,QAAA,gBACH7F,OAAA;QAAKwG,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAd,QAAA,gBACjF7F,OAAA,CAACI,KAAK;UAACwG,KAAK,EAAE,CAAE;UAAAf,QAAA,EAAC;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5BlG,OAAA;UAAA6F,QAAA,gBACE7F,OAAA,CAAChB,MAAM;YACL6H,WAAW,EAAC,4CAAS;YACrBL,KAAK,EAAE;cAAElB,KAAK,EAAE,GAAG;cAAEwB,WAAW,EAAE;YAAE,CAAE;YACtCC,QAAQ,EAAEjD,sBAAuB;YACjCC,KAAK,EAAEpD,kBAAkB,IAAIqG,SAAU;YAAAnB,QAAA,EAEtCpF,WAAW,CAAC6C,GAAG,CAAC2D,IAAI,iBACnBjH,OAAA,CAACG,MAAM;cAAe4D,KAAK,EAAEkD,IAAI,CAAC9D,EAAG;cAAA0C,QAAA,EAAEoB,IAAI,CAACC;YAAI,GAAnCD,IAAI,CAAC9D,EAAE;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqC,CAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAETlG,OAAA,CAAChB,MAAM;YACL6H,WAAW,EAAC,oBAAK;YACjBL,KAAK,EAAE;cAAElB,KAAK,EAAE,GAAG;cAAEwB,WAAW,EAAE;YAAE,CAAE;YACtCC,QAAQ,EAAE/C,iBAAkB;YAC5BD,KAAK,EAAEhD,aAAa,IAAIiG,SAAU;YAClCG,QAAQ,EAAE,CAACxG,kBAAmB;YAAAkF,QAAA,EAE7BhF,MAAM,CAACyC,GAAG,CAAC8D,KAAK,iBACfpH,OAAA,CAACG,MAAM;cAAgB4D,KAAK,EAAEqD,KAAK,CAACjE,EAAG;cAAA0C,QAAA,EAAEuB,KAAK,CAAC3D;YAAU,GAA5C2D,KAAK,CAACjE,EAAE;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA6C,CACnE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAETlG,OAAA,CAAChB,MAAM;YACL6H,WAAW,EAAC,oBAAK;YACjBL,KAAK,EAAE;cAAElB,KAAK,EAAE;YAAI,CAAE;YACtByB,QAAQ,EAAE9C,kBAAmB;YAC7BF,KAAK,EAAE5C,cAAc,IAAI6F,SAAU;YACnCG,QAAQ,EAAE,CAACpG,aAAc;YAAA8E,QAAA,EAExB5E,OAAO,CAACqC,GAAG,CAAC+D,MAAM,iBACjBrH,OAAA,CAACG,MAAM;cAAiB4D,KAAK,EAAEsD,MAAM,CAAClE,EAAG;cAAA0C,QAAA,EAAEwB,MAAM,CAACC;YAAW,GAAhDD,MAAM,CAAClE,EAAE;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgD,CACvE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL/E,cAAc,iBACbnB,OAAA;QAAKwG,KAAK,EAAE;UAAEG,YAAY,EAAE,EAAE;UAAEF,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEa,UAAU,EAAE;QAAS,CAAE;QAAA1B,QAAA,gBACvG7F,OAAA,CAACf,MAAM;UACLuI,IAAI,EAAC,SAAS;UACd1B,IAAI,eAAE9F,OAAA,CAACL,YAAY;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,OAAO,EAAEA,CAAA,KAAMjC,SAAS,CAAC,CAAE;UAAA2B,QAAA,EAC5B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAGTlG,OAAA,CAACT,KAAK,CAACkI,MAAM;UACXZ,WAAW,EAAC,gFAAe;UAC3Ba,UAAU;UACVlB,KAAK,EAAE;YAAElB,KAAK,EAAE;UAAI,CAAE;UACtByB,QAAQ,EAAGY,CAAC,IAAKvF,aAAa,CAACuF,CAAC,CAACC,MAAM,CAAC7D,KAAK,CAAE;UAC/C8D,QAAQ,EAAG9D,KAAK,IAAK3B,aAAa,CAAC2B,KAAK;QAAE;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAEAzE,OAAO,gBACNzB,OAAA;QAAKwG,KAAK,EAAE;UAAEsB,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAlC,QAAA,eACnD7F,OAAA,CAACb,IAAI;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,GACJ/E,cAAc,gBAChBnB,OAAA,CAAAE,SAAA;QAAA2F,QAAA,GAEG,EAAAtF,YAAA,GAAAM,MAAM,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKpC,aAAa,CAAC,cAAAR,YAAA,uBAAxCA,YAAA,CAA0CkD,UAAU,MAAK,cAAc,iBACtEzD,OAAA;UAAKwG,KAAK,EAAE;YAAEG,YAAY,EAAE,EAAE;YAAEoB,OAAO,EAAE,UAAU;YAAEC,eAAe,EAAE,SAAS;YAAEC,MAAM,EAAE,mBAAmB;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAArC,QAAA,eAClI7F,OAAA;YAAMwG,KAAK,EAAE;cAAE2B,KAAK,EAAE;YAAU,CAAE;YAAAtC,QAAA,GAAC,+FACf,EAACxE,aAAa,CAACwC,MAAM,EAAC,sBAC1C;UAAA;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eACDlG,OAAA,CAACd,KAAK;UACJ+B,OAAO,EAAEiE,aAAc;UACvBkD,UAAU,EAAE/G,aAAa,CAACgH,MAAM,CAAClE,OAAO,IACtC,CAAChC,UAAU,IACXgC,OAAO,CAACG,OAAO,CAACgE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpG,UAAU,CAACmG,WAAW,CAAC,CAAC,CAAC,IAChEnE,OAAO,CAACI,QAAQ,CAAC+D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpG,UAAU,CAACmG,WAAW,CAAC,CAAC,CAClE,CAAE;UACFE,MAAM,EAAC,IAAI;UACXC,UAAU,EAAE;YACVC,QAAQ,EAAE,EAAE;YACZC,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE,IAAI;YACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KAAK,MAAMA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK,MAAM;YAC1EE,eAAe,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK;UAC3C,CAAE;UACFC,MAAM,EAAE;YACNC,CAAC,EAAE,GAAG;YAAG;YACTC,CAAC,EAAE,aAAa,CAAE;UACpB,CAAE;UACFvD,IAAI,EAAC;QAAQ;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA,eACF,CAAC,gBAEHlG,OAAA;QAAKwG,KAAK,EAAE;UAAEsB,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAlC,QAAA,gBACnD7F,OAAA;UAAA6F,QAAA,EAAG;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EACrBvF,kBAAkB,KAAK,CAAC,IAAII,aAAa,IAAI,EAAAP,aAAA,GAAAK,MAAM,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKpC,aAAa,CAAC,cAAAP,aAAA,uBAAxCA,aAAA,CAA0CiD,UAAU,MAAK,cAAc,iBACnHzD,OAAA;UAAGwG,KAAK,EAAE;YAAE2B,KAAK,EAAE,SAAS;YAAEiB,SAAS,EAAE;UAAM,CAAE;UAAAvD,QAAA,GAAC,2HAEhD,EAACxE,aAAa,CAACwC,MAAM,GAAG,CAAC,IAAI,OAAOxC,aAAa,CAACwC,MAAM,KAAK;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEPlG,OAAA,CAACR,KAAK;MACJ2F,KAAK,EAAEtD,cAAc,GAAG,OAAO,GAAG,OAAQ;MAC1CwH,IAAI,EAAE1H,YAAa;MACnB2H,IAAI,EAAE7E,YAAa;MACnB8E,QAAQ,EAAE/E,YAAa;MAAAqB,QAAA,eAEvB7F,OAAA,CAACV,IAAI;QACHyC,IAAI,EAAEA,IAAK;QACXyH,MAAM,EAAC,UAAU;QAAA3D,QAAA,gBAEjB7F,OAAA,CAACV,IAAI,CAACmK,IAAI;UACRvC,IAAI,EAAC,SAAS;UACdwC,KAAK,EAAC,sCAAQ;UACdC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExK,OAAO,EAAE;UAAY,CAAC,CAAE;UAAAyG,QAAA,eAElD7F,OAAA,CAACT,KAAK;YAACsH,WAAW,EAAC;UAAW;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAEZlG,OAAA,CAACV,IAAI,CAACmK,IAAI;UACRvC,IAAI,EAAC,UAAU;UACfwC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExK,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyG,QAAA,eAEhD7F,OAAA,CAACT,KAAK;YAACsH,WAAW,EAAC;UAAY;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5F,EAAA,CA1XID,iBAA2B;EAAA,QAYhBf,IAAI,CAAC0C,OAAO;AAAA;AAAA6H,EAAA,GAZvBxJ,iBAA2B;AA4XjC,eAAeA,iBAAiB;AAAC,IAAAwJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}