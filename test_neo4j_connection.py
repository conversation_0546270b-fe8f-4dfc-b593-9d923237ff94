#!/usr/bin/env python3
"""
Neo4j连接测试脚本
"""
import socket
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent / "chatdb" / "backend"
sys.path.insert(0, str(project_root))

def test_network_connection():
    """测试网络连接"""
    print("=== Neo4j网络连接测试 ===")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex(('155.138.220.75', 7687))
        if result == 0:
            print("✅ Neo4j服务器网络连接正常 (155.138.220.75:7687)")
            return True
        else:
            print(f"❌ Neo4j服务器网络连接失败 (错误码: {result})")
            return False
    except Exception as e:
        print(f"❌ 网络测试异常: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

def check_config():
    """检查配置"""
    print("\n=== 配置检查 ===")
    try:
        from app.core.config import settings
        print(f"Neo4j URI: {settings.NEO4J_URI}")
        print(f"Neo4j User: {settings.NEO4J_USER}")
        print(f"Neo4j Password: {'*' * len(settings.NEO4J_PASSWORD)}")
        return True
    except Exception as e:
        print(f"❌ 配置读取失败: {e}")
        return False

def test_neo4j_driver():
    """测试Neo4j驱动连接"""
    print("\n=== Neo4j驱动连接测试 ===")
    try:
        from neo4j import GraphDatabase
        from app.core.config import settings
        
        driver = GraphDatabase.driver(
            settings.NEO4J_URI,
            auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD),
            connection_acquisition_timeout=30
        )
        
        with driver.session() as session:
            result = session.run("RETURN 1 as test")
            record = result.single()
            if record and record["test"] == 1:
                print("✅ Neo4j驱动连接成功")
                return True
            else:
                print("❌ Neo4j驱动连接失败 - 无响应")
                return False
                
    except Exception as e:
        print(f"❌ Neo4j驱动连接失败: {e}")
        return False
    finally:
        try:
            driver.close()
        except:
            pass

def suggest_solutions():
    """提供解决方案建议"""
    print("\n=== 解决方案建议 ===")
    print("1. 检查Neo4j服务状态:")
    print("   - 远程服务器 155.138.220.75 上的Neo4j是否运行")
    print("   - 防火墙是否允许7687端口访问")
    print("   - 网络连接是否稳定")
    
    print("\n2. 验证认证信息:")
    print("   - 用户名: neo4j")
    print("   - 密码是否正确")
    print("   - 用户是否有足够权限")
    
    print("\n3. 配置替代方案:")
    print("   - 使用本地Neo4j实例")
    print("   - 修改配置文件使用不同的Neo4j服务器")
    print("   - 启用故障转移模式（仅使用SQLite）")

def main():
    print("🔍 Neo4j连接问题诊断工具")
    print("=" * 50)
    
    # 测试网络连接
    network_ok = test_network_connection()
    
    # 检查配置
    config_ok = check_config()
    
    # 测试驱动连接
    if network_ok and config_ok:
        driver_ok = test_neo4j_driver()
    else:
        driver_ok = False
        print("\n⏭️ 跳过驱动测试（网络或配置问题）")
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 诊断结果总结:")
    print(f"  网络连接: {'✅ 正常' if network_ok else '❌ 失败'}")
    print(f"  配置检查: {'✅ 正常' if config_ok else '❌ 失败'}")
    print(f"  驱动连接: {'✅ 正常' if driver_ok else '❌ 失败'}")
    
    if not (network_ok and config_ok and driver_ok):
        suggest_solutions()
    else:
        print("\n🎉 Neo4j连接完全正常！")

if __name__ == "__main__":
    main()
