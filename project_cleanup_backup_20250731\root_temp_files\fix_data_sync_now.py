#!/usr/bin/env python3
"""
立即修复数据不同步问题
按照正确的数据流向：原始数据库 → SQLite → Neo4j
"""
import asyncio
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent / "chatdb" / "backend"
sys.path.insert(0, str(project_root))

async def fix_data_sync_immediately():
    """立即修复数据同步问题"""
    print("🚨 立即修复数据不同步问题")
    print("=" * 60)
    
    try:
        from app.db.session import SessionLocal
        from app import crud
        from app.services.schema_service import discover_schema, save_discovered_schema, sync_schema_to_graph_db
        from app.services.enhanced_cache_service import enhanced_cache
        
        # 检查需要修复的连接
        connections_to_fix = [1, 2]  # 根据报告，这两个连接有数据不一致
        
        for connection_id in connections_to_fix:
            print(f"\n🔄 修复连接ID {connection_id} 的数据同步...")
            
            db = SessionLocal()
            try:
                # 步骤1: 检查连接是否存在
                connection = crud.db_connection.get(db=db, id=connection_id)
                if not connection:
                    print(f"❌ 连接ID {connection_id} 不存在，跳过")
                    continue
                
                print(f"📊 连接信息: {connection.name}")
                print(f"   类型: {connection.db_type}")
                print(f"   主机: {connection.host}:{connection.port}")
                print(f"   数据库: {connection.database_name}")
                
                # 步骤2: 从原始数据库重新发现Schema
                print(f"\n🔍 步骤1: 从原始数据库重新发现Schema...")
                try:
                    schema_info = discover_schema(connection)
                    print(f"   ✅ 发现 {len(schema_info)} 个表")
                    
                    # 显示发现的表
                    table_names = [table['table_name'] for table in schema_info]
                    print(f"   📋 表列表: {', '.join(table_names[:5])}{'...' if len(table_names) > 5 else ''}")
                    
                except Exception as e:
                    print(f"   ❌ Schema发现失败: {str(e)}")
                    print(f"   💡 可能原因: 数据库连接问题或权限不足")
                    continue
                
                # 步骤3: 清空SQLite中的旧数据
                print(f"\n🧹 步骤2: 清空SQLite中连接ID {connection_id} 的旧数据...")
                try:
                    # 删除关系数据
                    old_relationships = crud.schema_relationship.get_by_connection(db, connection_id=connection_id)
                    for rel in old_relationships:
                        crud.schema_relationship.remove(db=db, id=rel.id)
                    
                    # 删除列数据
                    old_tables = crud.schema_table.get_by_connection(db, connection_id=connection_id)
                    for table in old_tables:
                        old_columns = crud.schema_column.get_by_table(db, table_id=table.id)
                        for column in old_columns:
                            crud.schema_column.remove(db=db, id=column.id)
                    
                    # 删除表数据
                    for table in old_tables:
                        crud.schema_table.remove(db=db, id=table.id)
                    
                    db.commit()
                    print(f"   ✅ 清空了 {len(old_tables)} 个表的旧数据")
                    
                except Exception as e:
                    print(f"   ❌ 清空SQLite数据失败: {str(e)}")
                    db.rollback()
                    continue
                
                # 步骤4: 保存新的Schema到SQLite
                print(f"\n💾 步骤3: 保存新Schema到SQLite...")
                try:
                    tables_data, relationships_data = save_discovered_schema(db, connection_id, schema_info)
                    print(f"   ✅ 保存了 {len(tables_data)} 个表")
                    print(f"   ✅ 保存了 {len(relationships_data)} 个关系")
                    
                except Exception as e:
                    print(f"   ❌ 保存Schema到SQLite失败: {str(e)}")
                    continue
                
            finally:
                db.close()
            
            # 步骤5: 同步到Neo4j
            print(f"\n🔗 步骤4: 同步到Neo4j...")
            try:
                result = sync_schema_to_graph_db(connection_id)
                if result:
                    print(f"   ✅ Neo4j同步成功")
                else:
                    print(f"   ❌ Neo4j同步失败")
                    continue
                    
            except Exception as e:
                print(f"   ❌ Neo4j同步异常: {str(e)}")
                continue
            
            print(f"🎉 连接ID {connection_id} 数据同步修复完成！")
        
        # 步骤6: 清空所有应用缓存
        print(f"\n🧹 步骤5: 清空所有应用缓存...")
        try:
            enhanced_cache.preload_cache.clear()
            enhanced_cache.query_patterns.clear()
            if hasattr(enhanced_cache.base_cache, 'clear'):
                enhanced_cache.base_cache.clear()
            print(f"   ✅ 应用缓存清空完成")
        except Exception as e:
            print(f"   ⚠️ 缓存清空失败: {str(e)}")
        
        print(f"\n🎉 所有数据同步修复完成！")
        
    except Exception as e:
        print(f"❌ 修复过程异常: {str(e)}")
        import traceback
        traceback.print_exc()

async def verify_fix_results():
    """验证修复结果"""
    print(f"\n✅ 验证修复结果")
    print("=" * 40)
    
    try:
        from app.db.session import SessionLocal
        from app import crud
        from app.services.neo4j_connection_pool import get_neo4j_pool
        from app.services.text2sql_utils import retrieve_relevant_schema
        
        connections_to_check = [1, 2]
        
        for connection_id in connections_to_check:
            print(f"\n📊 验证连接ID {connection_id}:")
            
            db = SessionLocal()
            pool = await get_neo4j_pool()
            
            try:
                # SQLite数据统计
                sqlite_tables = crud.schema_table.get_by_connection(db, connection_id=connection_id)
                sqlite_column_count = 0
                for table in sqlite_tables:
                    columns = crud.schema_column.get_by_table(db, table_id=table.id)
                    sqlite_column_count += len(columns)
                sqlite_rel_count = len(crud.schema_relationship.get_by_connection(db, connection_id=connection_id))
                
                # Neo4j数据统计
                neo4j_stats = await pool.execute_read_query("""
                    MATCH (t:Table {connection_id: $connection_id})
                    WITH count(t) as table_count
                    MATCH (t2:Table {connection_id: $connection_id})-[:HAS_COLUMN]->(c:Column)
                    WITH table_count, count(c) as column_count
                    OPTIONAL MATCH (t3:Table {connection_id: $connection_id})-[:HAS_COLUMN]->(c1:Column)
                    -[r:REFERENCES]->(c2:Column)<-[:HAS_COLUMN]-(t4:Table {connection_id: $connection_id})
                    RETURN table_count, column_count, count(r) as relationship_count
                """, {'connection_id': connection_id})
                
                if neo4j_stats:
                    neo4j_data = neo4j_stats[0]
                    
                    print(f"  SQLite: {len(sqlite_tables)}表/{sqlite_column_count}列/{sqlite_rel_count}关系")
                    print(f"  Neo4j:  {neo4j_data['table_count']}表/{neo4j_data['column_count']}列/{neo4j_data['relationship_count']}关系")
                    
                    # 检查一致性
                    tables_match = len(sqlite_tables) == neo4j_data['table_count']
                    columns_match = sqlite_column_count == neo4j_data['column_count']
                    relations_match = sqlite_rel_count == neo4j_data['relationship_count']
                    
                    if tables_match and columns_match and relations_match:
                        print(f"  ✅ 数据完全一致")
                    else:
                        print(f"  ⚠️ 数据仍有差异:")
                        if not tables_match:
                            print(f"     表数量不匹配")
                        if not columns_match:
                            print(f"     列数量不匹配")
                        if not relations_match:
                            print(f"     关系数量不匹配")
                
                # 测试Schema检索功能
                print(f"  🧪 测试Schema检索功能...")
                try:
                    result = await retrieve_relevant_schema(db, connection_id, "查询测试数据")
                    tables_found = len(result.get('tables', []))
                    columns_found = len(result.get('columns', []))
                    
                    if columns_found > 0:
                        print(f"     ✅ Schema检索正常: {tables_found}表/{columns_found}列")
                    else:
                        print(f"     ❌ Schema检索仍有问题: 找到0列")
                        
                except Exception as e:
                    print(f"     ❌ Schema检索测试失败: {str(e)}")
                
            finally:
                db.close()
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")

async def main():
    print("🚨 数据同步问题紧急修复工具")
    print("=" * 80)
    
    # 立即修复
    await fix_data_sync_immediately()
    
    # 验证结果
    await verify_fix_results()
    
    print("\n" + "=" * 80)
    print("📋 修复完成总结:")
    print("✅ 已按正确数据流向修复: 原始数据库 → SQLite → Neo4j")
    print("✅ 已清空所有缓存，确保数据一致性")
    print("✅ 已验证Schema检索功能")
    print()
    print("💡 后续建议:")
    print("1. 定期运行数据同步检查")
    print("2. 监控原始数据库结构变化")
    print("3. 建立自动同步机制")

if __name__ == "__main__":
    asyncio.run(main())
