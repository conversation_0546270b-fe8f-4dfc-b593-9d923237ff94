#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def enhance_metadata_system():
    """增强元数据系统，提高AI理解能力"""
    
    db_path = 'fin_data.db'
    if not os.path.exists(db_path):
        print(f'数据库文件不存在: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🚀 开始增强元数据系统...")
        
        # 1. 扩展字段描述表结构
        print("1. 扩展字段描述表结构...")
        try:
            cursor.execute('''
                ALTER TABLE column_descriptions ADD COLUMN field_category TEXT DEFAULT '';
            ''')
        except:
            pass  # 字段可能已存在

        try:
            cursor.execute('''
                ALTER TABLE column_descriptions ADD COLUMN usage_scenarios TEXT DEFAULT '';
            ''')
        except:
            pass

        try:
            cursor.execute('''
                ALTER TABLE column_descriptions ADD COLUMN common_values TEXT DEFAULT '';
            ''')
        except:
            pass

        try:
            cursor.execute('''
                ALTER TABLE column_descriptions ADD COLUMN related_fields TEXT DEFAULT '';
            ''')
        except:
            pass

        try:
            cursor.execute('''
                ALTER TABLE column_descriptions ADD COLUMN calculation_rules TEXT DEFAULT '';
            ''')
        except:
            pass

        try:
            cursor.execute('''
                ALTER TABLE column_descriptions ADD COLUMN ai_prompt_hints TEXT DEFAULT '';
            ''')
        except:
            pass
        
        # 2. 创建字段关系表
        print("2. 创建字段关系表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS field_relationships (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                primary_field TEXT NOT NULL,
                related_field TEXT NOT NULL,
                relationship_type TEXT NOT NULL,
                relationship_description TEXT,
                usage_example TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 3. 创建查询模式表
        print("3. 创建查询模式表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS query_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_name TEXT NOT NULL,
                pattern_description TEXT NOT NULL,
                natural_language_examples TEXT NOT NULL,
                sql_template TEXT NOT NULL,
                required_fields TEXT NOT NULL,
                business_scenario TEXT,
                difficulty_level TEXT DEFAULT 'MEDIUM',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 4. 创建数据质量规则表
        print("4. 创建数据质量规则表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_quality_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                field_name TEXT NOT NULL,
                rule_type TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                validation_sql TEXT,
                error_message TEXT,
                severity_level TEXT DEFAULT 'WARNING',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 5. 创建AI提示模板表
        print("5. 创建AI提示模板表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_prompt_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_name TEXT NOT NULL,
                template_type TEXT NOT NULL,
                template_content TEXT NOT NULL,
                usage_scenario TEXT,
                priority_level INTEGER DEFAULT 5,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        print("✅ 元数据系统结构扩展完成")
        
        # 6. 更新现有字段描述
        print("6. 更新现有字段描述...")
        enhanced_descriptions = [
            # 时间维度字段增强
            ('financial_data', 'year', '时间维度', '年度财务分析,时间序列对比,跨年度趋势分析', '2024,2023,2022', 'month', '年度汇总计算', '用于年度分组和时间筛选'),
            ('financial_data', 'month', '时间维度', '月度财务分析,季度汇总,月度对比', '1-12', 'year', '月度汇总计算', '用于月度分组和时间筛选'),
            
            # 组织维度字段增强  
            ('financial_data', 'accounting_organization', '组织维度', '组织架构分析,多组织对比,组织绩效评估', '101,102,103', 'accounting_unit_name', '组织级汇总', '用于组织分组和筛选'),
            ('financial_data', 'accounting_unit_name', '组织维度', '具体单位分析,单位绩效对比,单位财务状况', 'REITs专项计划基金,物业公司', 'accounting_organization', '单位级明细查询', '用于单位名称显示和筛选'),
            
            # 科目维度字段增强
            ('financial_data', 'account_code', '科目维度', '科目分类查询,财务报表编制,科目余额分析', '1001,1002,6001,6401', 'account_name,account_direction', '科目分类规则', '根据科目编号确定使用的金额字段'),
            ('financial_data', 'account_name', '科目维度', '科目名称查询,财务科目分析,会计科目管理', '银行存款,主营业务收入,管理费用', 'account_code,account_direction', '科目名称匹配', '用于科目名称显示和模糊查询'),
            ('financial_data', 'account_direction', '科目维度', '借贷方向判断,会计平衡验证,科目性质分析', '借,贷', 'account_code', '借贷平衡验证', '确定科目的正常余额方向'),
            
            # 金额字段增强
            ('financial_data', 'debit_amount', '金额维度', '费用支出分析,成本核算,借方发生额统计', '正数金额', 'credit_amount,debit_cumulative', '借方金额汇总', '成本费用类科目必须使用此字段'),
            ('financial_data', 'credit_amount', '金额维度', '收入分析,贷方发生额统计,收益核算', '正数金额', 'debit_amount,credit_cumulative', '贷方金额汇总', '收入类科目必须使用此字段'),
            ('financial_data', 'balance', '金额维度', '余额分析,资产负债表编制,期末余额查询', '正负数金额,可能包含方向', 'debit_amount,credit_amount', 'CAST(balance AS REAL)', '资产负债类科目必须使用此字段,需类型转换'),
            ('financial_data', 'debit_cumulative', '金额维度', '年度累计分析,累计费用统计,年度成本分析', '正数金额', 'debit_amount', '年度累计汇总', '年初至今的借方累计金额'),
            ('financial_data', 'credit_cumulative', '金额维度', '年度累计收入,累计收益分析,年度收入统计', '正数金额', 'credit_amount', '年度累计汇总', '年初至今的贷方累计金额'),
        ]
        
        for desc in enhanced_descriptions:
            cursor.execute('''
                UPDATE column_descriptions 
                SET field_category = ?, usage_scenarios = ?, common_values = ?, 
                    related_fields = ?, calculation_rules = ?, ai_prompt_hints = ?
                WHERE table_name = ? AND column_name = ?
            ''', (desc[2], desc[3], desc[4], desc[5], desc[6], desc[7], desc[0], desc[1]))
        
        print("✅ 字段描述增强完成")
        
        conn.commit()
        conn.close()
        
        print("🎉 元数据系统增强完成！")
        
    except Exception as e:
        print(f"❌ 增强元数据系统时出错: {e}")

if __name__ == "__main__":
    enhance_metadata_system()
