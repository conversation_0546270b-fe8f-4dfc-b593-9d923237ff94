#!/usr/bin/env python3
"""
测试Text2SQL流水线监控系统
验证7个步骤的监控是否正常工作
"""

import requests
import json
import time

def test_pipeline_monitoring():
    """测试流水线监控系统"""
    
    print("🔍 测试Text2SQL流水线监控系统")
    print("=" * 80)
    
    base_url = "http://localhost:8000/api"
    
    # 1. 测试系统状态
    print("1️⃣ 检查系统状态...")
    try:
        response = requests.get(f"{base_url}/debug/system-status")
        if response.status_code == 200:
            status = response.json()
            print(f"   ✅ 系统状态: {status['status']}")
        else:
            print(f"   ❌ 系统状态检查失败: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        return
    
    # 2. 执行测试查询（触发完整流水线）
    print(f"\n2️⃣ 执行测试查询（触发7步流水线）...")
    
    test_queries = [
        "2024年1月各公司的销售费用合计",
        "查询所有公司2024年的收入情况",
        "显示2024年各月份的费用明细"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n   测试查询 {i}: {query}")
        
        try:
            response = requests.post(
                f"{base_url}/debug/test-text2sql",
                json={
                    "query": query,
                    "connection_name": "fin_data"
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                query_id = result['debug_info']['query_id']
                has_error = bool(result['result']['error'])
                
                print(f"      🆔 查询ID: {query_id}")
                print(f"      ❌ 有错误: {has_error}")
                
                if has_error:
                    print(f"      📝 错误信息: {result['result']['error']}")
                
                # 等待日志写入
                time.sleep(1)
                
                # 获取该查询的详细日志
                log_response = requests.get(
                    f"{base_url}/debug/text2sql-logs/search?query_id={query_id}&lines=50"
                )
                
                if log_response.status_code == 200:
                    logs = log_response.json()
                    print(f"      📊 相关日志行数: {logs['total_matches']}")
                    
                    # 分析日志中的步骤完成情况
                    step_patterns = [
                        "STEP 1: Schema获取",
                        "STEP 2: 字段映射获取", 
                        "STEP 3: 元数据获取",
                        "STEP 4: Schema增强",
                        "STEP 5: 提示构建",
                        "STEP 6: LLM生成",
                        "STEP 7: SQL后处理"
                    ]
                    
                    completed_steps = []
                    failed_steps = []
                    
                    for step_num, pattern in enumerate(step_patterns, 1):
                        step_found = any(pattern in log for log in logs['logs'])
                        if step_found:
                            # 检查该步骤是否有错误
                            step_error = any(f"Step {step_num}" in log and "❌" in log 
                                           for log in logs['logs'])
                            if step_error:
                                failed_steps.append(step_num)
                            else:
                                completed_steps.append(step_num)
                    
                    print(f"      ✅ 完成步骤: {completed_steps}")
                    if failed_steps:
                        print(f"      ❌ 失败步骤: {failed_steps}")
                    
                    # 检查是否有问题字段
                    problem_fields_mentioned = any(
                        'company_id' in log or 'company_name' in log or 'date' in log 
                        for log in logs['logs']
                    )
                    
                    if problem_fields_mentioned:
                        print(f"      ⚠️ 日志中提到了问题字段")
                    else:
                        print(f"      ✅ 日志中未发现问题字段")
                
            else:
                print(f"      ❌ 查询失败: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ 请求失败: {e}")
    
    # 3. 检查流水线日志文件
    print(f"\n3️⃣ 检查流水线日志文件...")
    
    try:
        # 获取流水线监控日志
        import os
        pipeline_log_file = "pipeline_monitor.log"
        
        if os.path.exists(pipeline_log_file):
            with open(pipeline_log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"   📄 流水线日志文件存在")
            print(f"   📊 总行数: {len(lines)}")
            
            # 统计各种日志类型
            pipeline_starts = sum(1 for line in lines if "PIPELINE START" in line)
            step_logs = sum(1 for line in lines if "STEP" in line and ":" in line)
            pipeline_summaries = sum(1 for line in lines if "PIPELINE SUMMARY" in line)
            
            print(f"   🚀 流水线启动: {pipeline_starts}")
            print(f"   📋 步骤日志: {step_logs}")
            print(f"   🏁 流水线总结: {pipeline_summaries}")
            
            # 显示最新的几行日志
            if lines:
                print(f"   📝 最新日志（最后3行）:")
                for line in lines[-3:]:
                    print(f"      {line.strip()}")
        else:
            print(f"   ❌ 流水线日志文件不存在")
            
    except Exception as e:
        print(f"   ❌ 检查日志文件失败: {e}")

def analyze_pipeline_effectiveness():
    """分析流水线监控的有效性"""
    
    print(f"\n" + "=" * 80)
    print("📊 流水线监控有效性分析")
    print("=" * 80)
    
    print("""
🎯 **流水线监控系统的预期效果**:

1️⃣ **精确定位故障点**:
   - 如果步骤1失败 → Schema获取问题（Neo4j连接或查询问题）
   - 如果步骤2失败 → 字段映射问题（valuemapping表数据问题）
   - 如果步骤3失败 → 元数据获取问题（meta_column_descriptions问题）
   - 如果步骤4失败 → Schema增强问题（元数据合并逻辑问题）
   - 如果步骤5失败 → 提示构建问题（提示模板或逻辑问题）
   - 如果步骤6失败 → LLM生成问题（模型本身的问题）
   - 如果步骤7失败 → SQL后处理问题（映射替换逻辑问题）

2️⃣ **数据流追踪**:
   - 每个步骤的输入输出都被记录
   - 可以看到数据在流水线中的变化过程
   - 识别数据丢失或错误转换的位置

3️⃣ **根本原因识别**:
   - 如果所有步骤都成功，但仍有问题 → LLM模型限制或用户配置问题
   - 如果某个步骤失败 → 该步骤的具体实现有问题
   - 如果数据传递有问题 → 接口或数据格式问题

---

🔍 **关键监控指标**:

- **步骤1**: tables_count, columns_count, key_fields_found
- **步骤2**: mapped_fields_count, critical_mappings_found
- **步骤3**: descriptions_count, business_rules_count
- **步骤4**: enhancement_rate, enhanced_fields_count
- **步骤5**: prompt_length, required_sections_found
- **步骤6**: problem_fields_found, correct_fields_found
- **步骤7**: processing_successful, remaining_problems

---

🎯 **下一步行动**:

1. 重启后端服务以启用流水线监控
2. 执行测试查询并观察7个步骤的执行情况
3. 根据监控结果确定确切的故障点
4. 针对性地修复发现的问题
""")

def provide_usage_guide():
    """提供使用指南"""
    
    print(f"\n" + "=" * 80)
    print("📖 流水线监控使用指南")
    print("=" * 80)
    
    print("""
🔧 **监控文件位置**:
- 流水线监控: pipeline_monitor.log
- 详细日志: text2sql_debug.log

🔍 **关键日志标识**:
- 🚀 PIPELINE START - 流水线开始
- 📊 STEP 1: Schema获取 - 步骤1执行
- 🔗 STEP 2: 字段映射获取 - 步骤2执行
- 📋 STEP 3: 元数据获取 - 步骤3执行
- 🔧 STEP 4: Schema增强 - 步骤4执行
- 📝 STEP 5: 提示构建 - 步骤5执行
- 🤖 STEP 6: LLM生成 - 步骤6执行
- 🔧 STEP 7: SQL后处理 - 步骤7执行
- 🏁 PIPELINE SUMMARY - 流水线总结

📊 **状态标识**:
- ✅ success - 步骤成功
- ⚠️ warning - 步骤有警告
- ❌ error - 步骤失败

🎯 **故障诊断流程**:
1. 查看 PIPELINE SUMMARY 确定失败的步骤
2. 查找对应步骤的详细日志
3. 分析该步骤的输入输出数据
4. 确定根本原因并修复
""")

if __name__ == "__main__":
    test_pipeline_monitoring()
    analyze_pipeline_effectiveness()
    provide_usage_guide()
    
    print(f"\n" + "=" * 80)
    print("🎯 流水线监控系统已部署，请重启服务并测试")
    print("=" * 80)
