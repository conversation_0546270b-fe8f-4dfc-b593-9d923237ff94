#!/usr/bin/env python3
"""
Schema检索功能测试脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent / "chatdb" / "backend"
sys.path.insert(0, str(project_root))

async def test_schema_retrieval():
    """测试Schema检索功能"""
    print("🔍 Schema检索功能测试")
    print("=" * 50)
    
    try:
        from app.db.session import SessionLocal
        from app.services.text2sql_utils import retrieve_relevant_schema
        
        # 创建数据库会话
        db = SessionLocal()
        
        try:
            # 测试参数
            connection_id = 1
            test_query = "查询销售数据"
            
            print(f"测试参数:")
            print(f"  连接ID: {connection_id}")
            print(f"  查询: {test_query}")
            print()
            
            # 执行Schema检索
            print("🚀 开始执行Schema检索...")
            result = await retrieve_relevant_schema(db, connection_id, test_query)
            
            # 分析结果
            print("📊 检索结果分析:")
            print(f"  找到表数量: {len(result.get('tables', []))}")
            print(f"  找到列数量: {len(result.get('columns', []))}")
            print(f"  找到关系数量: {len(result.get('relationships', []))}")
            
            # 检查是否使用了回退方案
            if result.get('_fallback'):
                print(f"  ⚠️ 使用了回退方案: {result.get('_fallback_reason', '未知原因')}")
                if result.get('_original_error'):
                    print(f"  原始错误: {result.get('_original_error')}")
            else:
                print("  ✅ 使用了Neo4j正常流程")
            
            # 显示监控信息（如果有）
            if '_monitoring' in result:
                monitoring = result['_monitoring']
                print(f"\n📈 性能监控:")
                print(f"  总耗时: {monitoring.get('total_duration', 'N/A')}")
                print(f"  数据源: {monitoring.get('source', 'N/A')}")
                
                # 显示管道各阶段状态
                stages = monitoring.get('pipeline_stages', {})
                if stages:
                    print(f"  管道阶段:")
                    for stage_name, stage_info in stages.items():
                        status = stage_info.get('status', 'UNKNOWN')
                        duration = stage_info.get('duration', 'N/A')
                        print(f"    {stage_name}: {status} ({duration}s)")
                
                # 显示错误和警告
                errors = monitoring.get('errors', [])
                warnings = monitoring.get('warnings', [])
                if errors:
                    print(f"  ❌ 错误 ({len(errors)}): {errors}")
                if warnings:
                    print(f"  ⚠️ 警告 ({len(warnings)}): {warnings}")
            
            # 显示部分结果数据
            if result.get('tables'):
                print(f"\n📋 找到的表 (前3个):")
                for i, table in enumerate(result['tables'][:3]):
                    print(f"  {i+1}. {table.get('name', 'N/A')} (ID: {table.get('id', 'N/A')})")
            
            if result.get('columns'):
                print(f"\n🏛️ 找到的列 (前5个):")
                for i, column in enumerate(result['columns'][:5]):
                    print(f"  {i+1}. {column.get('table_name', 'N/A')}.{column.get('name', 'N/A')} ({column.get('type', 'N/A')})")
            
            # 判断检索是否成功
            if result.get('tables') or result.get('columns'):
                print(f"\n✅ Schema检索成功！")
                return True
            else:
                print(f"\n❌ Schema检索失败 - 未找到任何表或列")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Schema检索测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_connection_pool():
    """测试连接池状态"""
    print("\n🏊 连接池状态测试")
    print("=" * 30)
    
    try:
        from app.services.neo4j_connection_pool import get_neo4j_pool
        
        # 获取连接池
        pool = await get_neo4j_pool()
        
        print(f"连接池初始化状态: {pool._initialized}")
        print(f"连接池配置: {pool._connection_config}")
        
        # 测试连接池查询
        result = await pool.execute_read_query("RETURN 1 as test")
        print(f"连接池查询测试: {'✅ 成功' if result else '❌ 失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接池测试失败: {e}")
        return False

async def main():
    print("🧪 Text2SQL Schema检索综合测试")
    print("=" * 60)
    
    # 测试连接池
    pool_ok = await test_connection_pool()
    
    # 测试Schema检索
    schema_ok = await test_schema_retrieval()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"  连接池状态: {'✅ 正常' if pool_ok else '❌ 异常'}")
    print(f"  Schema检索: {'✅ 成功' if schema_ok else '❌ 失败'}")
    
    if pool_ok and schema_ok:
        print("\n🎉 所有测试通过！Schema检索功能正常。")
    else:
        print("\n⚠️ 存在问题，需要进一步排查。")

if __name__ == "__main__":
    asyncio.run(main())
