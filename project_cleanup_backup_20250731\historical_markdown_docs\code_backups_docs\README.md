# 智能数据分析系统

## 项目概述

这是一个基于Text2SQL技术的智能财务数据分析系统，能够将自然语言查询转换为SQL语句，并提供智能的数据分析和可视化功能。

## 项目结构

```
智能数据分析系统/
├── chatdb/                 # 主应用程序
│   ├── backend/           # Python FastAPI后端
│   ├── frontend/          # React前端
│   └── docker-compose.yml # Docker配置
├── docs/                  # 项目文档
│   ├── financial_data_column_guide.md
│   ├── TEXT2SQL_DETAILED_ANALYSIS.md
│   ├── TEXT2SQL_INTEGRATION_CHECKLIST.md
│   ├── FINAL_TECHNICAL_RECOMMENDATIONS.md
│   └── DEPLOYMENT_CHECKLIST.md
├── scripts/               # SQL脚本文件
│   ├── financial_data_metadata_queries.sql
│   └── suggested_queries.sql
├── tools/                 # 实用工具
│   ├── view_financial_data_descriptions.py
│   └── ai_metadata_usage_examples.py
├── fin_data.db           # 主数据库（财务数据）
├── resource.db           # 资源数据库（元数据）
└── README.md             # 项目说明
```

## 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+
- SQLite 3

### 后端启动

```bash
cd chatdb/backend
pip install -r requirements.txt
python main.py
```

### 前端启动

```bash
cd chatdb/frontend
npm install
npm start
```

### Docker启动

```bash
cd chatdb
docker-compose up
```

## 主要功能

- **自然语言转SQL查询 (Text2SQL)**: 支持中文自然语言查询转换为SQL
- **数据库连接管理**: 支持多种数据库类型（SQLite、MySQL、PostgreSQL）
- **智能查询处理**: 基于LLM的智能查询理解和优化
- **数据可视化**: 查询结果的图表展示
- **元数据增强**: 智能的数据库结构理解和业务规则集成

## 技术栈

### 后端
- **框架**: Python FastAPI
- **ORM**: SQLAlchemy
- **数据验证**: Pydantic
- **数据库**: SQLite (主要), MySQL, PostgreSQL (可选)
- **图数据库**: Neo4j (可选)

### 前端
- **框架**: React 18 + TypeScript
- **UI库**: Ant Design
- **状态管理**: Zustand
- **图表**: React Flow
- **构建工具**: Create React App

### AI集成
- **LLM支持**: DeepSeek, OpenAI, 阿里云DashScope
- **向量数据库**: Milvus (可选)
- **混合检索**: 结合向量搜索和图数据库

## 数据库说明

- **fin_data.db**: 主要财务数据，包含完整的财务记录
- **resource.db**: 系统资源和元数据，包含数据库连接信息、表结构描述等

## 文档

- [财务数据列指南](docs/financial_data_column_guide.md)
- [Text2SQL详细分析](docs/TEXT2SQL_DETAILED_ANALYSIS.md)
- [Text2SQL集成检查清单](docs/TEXT2SQL_INTEGRATION_CHECKLIST.md)
- [最终技术建议](docs/FINAL_TECHNICAL_RECOMMENDATIONS.md)
- [部署检查清单](docs/DEPLOYMENT_CHECKLIST.md)

## 工具脚本

- [查看财务数据描述](tools/view_financial_data_descriptions.py): 查看和管理财务数据的元数据描述
- [AI元数据使用示例](tools/ai_metadata_usage_examples.py): AI元数据集成的使用示例

## SQL脚本

- [财务数据元数据查询](scripts/financial_data_metadata_queries.sql): 财务数据相关的元数据查询
- [建议查询示例](scripts/suggested_queries.sql): 常用查询示例

## 配置说明

### 环境变量

```bash
# 数据库配置
DATABASE_TYPE=sqlite
SQLITE_DB_PATH=resource.db
BUSINESS_DB_PATH=fin_data.db

# LLM配置
OPENAI_API_KEY=your_api_key
OPENAI_API_BASE=https://api.deepseek.com/v1

# 其他配置
ENABLE_METADATA_ENHANCEMENT=true
```

## 开发指南

### 添加新的查询类型

1. 在后端添加新的查询处理逻辑
2. 更新前端UI组件
3. 添加相应的测试用例
4. 更新文档

### 扩展数据库支持

1. 在`app/services/db_service.py`中添加新的数据库类型支持
2. 更新连接配置
3. 测试兼容性

## 许可证

[添加许可证信息]

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

[添加联系方式]
